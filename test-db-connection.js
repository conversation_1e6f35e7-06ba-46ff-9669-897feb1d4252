const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

// Carregar variáveis de ambiente do arquivo .env
function loadEnv() {
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          process.env[key] = value;
        }
      }
    });
  }
}

// Carregar variáveis de ambiente
loadEnv();

async function testConnection() {
  const prisma = new PrismaClient({
    log: ['query', 'error', 'warn'],
  });

  try {
    console.log('🔄 Testando conexão com o banco de dados...');
    
    // Teste básico de conexão
    await prisma.$connect();
    console.log('✅ Conectado com sucesso!');
    
    // Teste de query simples
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Query de teste executada:', result);
    
    // Teste de tabela de usuários
    const userCount = await prisma.user.count();
    console.log('✅ Contagem de usuários:', userCount);
    
  } catch (error) {
    console.error('❌ Erro na conexão:', error.message);
    console.error('❌ Detalhes do erro:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Desconectado do banco');
  }
}

testConnection();
