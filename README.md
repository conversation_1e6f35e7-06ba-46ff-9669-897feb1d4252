# 🏥 ZapVida - Plataforma de Telemedicina

[![Next.js](https://img.shields.io/badge/Next.js-15.0.2-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Prisma](https://img.shields.io/badge/Prisma-5.0-2D3748)](https://www.prisma.io/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4-38B2AC)](https://tailwindcss.com/)
[![tRPC](https://img.shields.io/badge/tRPC-11.0-2596BE)](https://trpc.io/)

Uma plataforma completa de telemedicina que conecta pacientes e médicos através de consultas virtuais, sistema de plantão 24/7, gestão hospitalar e integração com WhatsApp.

## 📋 Índice

- [Visão Geral](#-visão-geral)
- [Funcionalidades](#-funcionalidades)
- [Tecnologias](#-tecnologias)
- [Arquitetura](#-arquitetura)
- [Instalação](#-instalação)
- [Configuração](#-configuração)
- [Estrutura do Projeto](#-estrutura-do-projeto)
- [Scripts Disponíveis](#-scripts-disponíveis)
- [Deploy](#-deploy)
- [Contribuição](#-contribuição)
- [Licença](#-licença)

## 🎯 Visão Geral

O ZapVida é uma plataforma de telemedicina moderna que oferece:

- **Consultas Virtuais**: Videochamadas, áudio e chat em tempo real
- **Sistema de Plantão**: Atendimento 24/7 com notificações via WhatsApp
- **Gestão Hospitalar**: Controle de agendas, departamentos e equipes médicas
- **Assinaturas**: Planos mensais para pacientes com consultas ilimitadas
- **Integração WhatsApp**: Notificações automáticas para médicos e pacientes
- **Prontuários Eletrônicos**: Gestão completa de documentos médicos
- **Sistema de Pagamentos**: Integração com Asaas (PIX, cartão, boleto)

## ✨ Funcionalidades

### 🏥 Para Pacientes
- **Agendamento Online**: Escolha de médicos e horários disponíveis
- **Plantão 24/7**: Atendimento urgente sem agendamento prévio
- **Consultas Virtuais**: Videochamadas, áudio e chat
- **Prontuário Digital**: Acesso ao histórico médico completo
- **Assinaturas**: Planos mensais com consultas ilimitadas
- **Prescrições Digitais**: Receitas médicas online
- **Notificações**: Lembretes via WhatsApp e email

### 👨‍⚕️ Para Médicos
- **Dashboard Completo**: Visão geral de consultas e pacientes
- **Gestão de Agenda**: Controle de horários e disponibilidade
- **Plantão Online**: Sistema de fila para atendimentos urgentes
- **Prontuários**: Criação e gestão de documentos médicos
- **Prescrições**: Sistema integrado com Memed
- **Relatórios**: Analytics de atendimentos e receita
- **Notificações**: Alertas via WhatsApp para novos plantões

### 🏢 Para Hospitais
- **Gestão de Equipes**: Controle de médicos e secretárias
- **Departamentos**: Organização por especialidades
- **Agendas Centralizadas**: Secretárias gerenciam múltiplos médicos
- **Formulários Especializados**: Anamnese e pré-anestesia
- **Relatórios Administrativos**: Métricas e analytics
- **Integração WhatsApp**: Notificações para equipes

### 🔧 Para Administradores
- **Painel de Controle**: Gestão completa da plataforma
- **Usuários**: Controle de médicos, pacientes e hospitais
- **Pagamentos**: Monitoramento de transações e comissões
- **Relatórios**: Analytics detalhados da plataforma
- **Configurações**: Personalização do sistema

## 🛠 Tecnologias

### Frontend
- **Next.js 15** - Framework React com App Router
- **TypeScript** - Tipagem estática
- **Tailwind CSS** - Framework de CSS utilitário
- **Radix UI** - Componentes acessíveis
- **React Hook Form** - Gerenciamento de formulários
- **Zod** - Validação de schemas
- **tRPC** - API type-safe
- **React Query** - Gerenciamento de estado servidor

### Backend
- **tRPC** - API type-safe
- **Prisma** - ORM para banco de dados
- **PostgreSQL** - Banco de dados principal
- **NextAuth.js** - Autenticação
- **Zod** - Validação de dados

### Integrações
- **LiveKit** - Videochamadas em tempo real
- **Evolution API** - Integração WhatsApp
- **Asaas** - Gateway de pagamentos
- **Memed** - Prescrições digitais
- **Resend** - Serviço de email
- **AWS S3** - Armazenamento de arquivos

### DevOps
- **Docker** - Containerização
- **Railway** - Deploy e hospedagem
- **Turbo** - Monorepo build system
- **pnpm** - Gerenciador de pacotes

## 🏗 Arquitetura

O projeto utiliza uma arquitetura de monorepo com as seguintes estruturas:

```
zapvida-app/
├── apps/
│   └── web/                 # Aplicação Next.js principal
├── packages/
│   ├── api/                 # API tRPC e procedures
│   ├── auth/                # Sistema de autenticação
│   ├── database/            # Schema Prisma e migrations
│   ├── mail/                # Templates de email
│   ├── storage/             # Serviços de armazenamento
│   ├── utils/               # Utilitários compartilhados
│   ├── ai/                  # Integração com IA
│   ├── i18n/                # Internacionalização
│   └── logs/                # Sistema de logs
├── scripts/                 # Scripts de automação
├── docs/                    # Documentação
└── tooling/                 # Configurações de ferramentas
```

### Fluxo de Dados

1. **Frontend** (Next.js) → **API** (tRPC) → **Database** (PostgreSQL)
2. **Webhooks** (Asaas/Evolution) → **API** → **Database**
3. **Real-time** (LiveKit) → **Frontend** → **Database**

## 🚀 Instalação

### Pré-requisitos

- Node.js 18+
- pnpm 9+
- PostgreSQL 14+
- Docker (opcional)

### 1. Clone o repositório

```bash
git clone https://github.com/seu-usuario/zapvida-app.git
cd zapvida-app
```

### 2. Instale as dependências

```bash
pnpm install
```

### 3. Configure as variáveis de ambiente

```bash
cp .env.example .env.local
```

Edite o arquivo `.env.local` com suas configurações:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/zapvida"
DIRECT_URL="postgresql://username:password@localhost:5432/zapvida"

# NextAuth
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Asaas (Pagamentos)
ASAAS_API_KEY="your-asaas-api-key"
ASAAS_API_URL="https://sandbox.asaas.com/api/v3"
ASAAS_WEBHOOK_TOKEN="your-webhook-token"

# Evolution API (WhatsApp)
EVOLUTION_API_KEY="your-evolution-api-key"
EVOLUTION_INSTANCE="zapvida"
EVOLUTION_URL="https://your-evolution-server.com"
EVOLUTION_WEBHOOK_URL="https://your-domain.com/api/webhooks/evolution"

# LiveKit (Videochamadas)
LIVEKIT_API_KEY="your-livekit-api-key"
LIVEKIT_API_SECRET="your-livekit-api-secret"
NEXT_PUBLIC_LIVEKIT_URL="wss://your-livekit-server.com"

# Email
RESEND_API_KEY="your-resend-api-key"

# Storage
S3_ENDPOINT="your-s3-endpoint"
S3_ACCESS_KEY_ID="your-s3-access-key"
S3_SECRET_ACCESS_KEY="your-s3-secret-key"
NEXT_PUBLIC_S3_ENDPOINT="your-s3-endpoint"
NEXT_PUBLIC_UPLOADS_BUCKET_NAME="uploads"
NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME="prescriptions"
NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET="chat-attachments"

# Site
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
```

### 4. Configure o banco de dados

```bash
# Gerar o cliente Prisma
pnpm db:generate

# Executar migrations
pnpm db:push

# (Opcional) Popular com dados de exemplo
pnpm db:seed
```

### 5. Execute a aplicação

```bash
# Desenvolvimento
pnpm dev

# Build para produção
pnpm build
pnpm start
```

A aplicação estará disponível em `http://localhost:3000`

## ⚙️ Configuração

### Configuração do WhatsApp

1. **Configure a Evolution API**:
   - Instale e configure a Evolution API
   - Crie uma instância para o ZapVida
   - Configure as variáveis de ambiente

2. **Configure os grupos WhatsApp**:
   ```bash
   # Validar configuração
   pnpm run validate-groups

   # Criar grupos automaticamente
   pnpm run create-groups
   ```

3. **Teste as notificações**:
   ```bash
   pnpm run test-whatsapp
   ```

### Configuração do Asaas

1. **Crie uma conta no Asaas**
2. **Configure as chaves de API** no `.env`
3. **Configure os webhooks** para:
   - `https://your-domain.com/api/webhooks/asaas`
   - `https://your-domain.com/api/webhooks/plantao-payment-confirmed`

### Configuração do LiveKit

1. **Crie uma conta no LiveKit**
2. **Configure as chaves** no `.env`
3. **Teste a conexão**:
   ```bash
   curl http://localhost:3000/api/livekit/test-connection
   ```

## 📁 Estrutura do Projeto

### Apps
- **`apps/web/`** - Aplicação Next.js principal

### Packages
- **`packages/api/`** - API tRPC com procedures e rotas
- **`packages/auth/`** - Sistema de autenticação
- **`packages/database/`** - Schema Prisma e migrations
- **`packages/mail/`** - Templates de email
- **`packages/storage/`** - Serviços de armazenamento
- **`packages/utils/`** - Utilitários compartilhados
- **`packages/ai/`** - Integração com IA
- **`packages/i18n/`** - Internacionalização
- **`packages/logs/`** - Sistema de logs

### Scripts
- **`scripts/`** - Scripts de automação e utilitários

### Documentação
- **`docs/`** - Documentação técnica e guias

## 📜 Scripts Disponíveis

### Desenvolvimento
```bash
pnpm dev          # Inicia o servidor de desenvolvimento
pnpm build        # Build para produção
pnpm start        # Inicia o servidor de produção
pnpm lint         # Executa o linter
pnpm format       # Formata o código
```

### Banco de Dados
```bash
pnpm db:generate  # Gera o cliente Prisma
pnpm db:push      # Executa migrations
pnpm db:seed      # Popula com dados de exemplo
```

### WhatsApp
```bash
pnpm run validate-groups    # Valida grupos WhatsApp
pnpm run create-groups      # Cria grupos automaticamente
pnpm run test-whatsapp      # Testa notificações
pnpm run check:whatsapp     # Verifica configuração
```

### Testes
```bash
pnpm test         # Executa testes
pnpm test:e2e     # Executa testes E2E
```

## 🚀 Deploy

### Railway (Recomendado)

1. **Conecte o repositório** ao Railway
2. **Configure as variáveis de ambiente**
3. **Configure o banco PostgreSQL**
4. **Deploy automático** a cada push

### Docker

```bash
# Build da imagem
docker build -t zapvida-app .

# Executar container
docker run -p 3000:3000 zapvida-app
```

### Docker Compose

```bash
# Iniciar todos os serviços
docker-compose up -d

# Parar serviços
docker-compose down
```

## 🔧 Configurações Avançadas

### Criptografia de Cartões

O sistema implementa criptografia AES-256-GCM para dados de cartão:

```env
# Chave de 32 caracteres hexadecimais
CARD_ENCRYPTION_KEY="a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
```

### Configuração de Comissões

Configure as taxas de comissão no banco de dados:

```sql
INSERT INTO commission_settings (platformFeeRate, minFeeAmount, maxFeeAmount)
VALUES (0.05, 2.00, 50.00);
```

### Configuração de Assinaturas

Configure os planos de assinatura:

```typescript
// packages/api/constants/subscriptions.ts
export const SUBSCRIPTION_PLANS = {
  'zapvida-sempre': {
    name: 'ZapVida Sempre',
    price: 49.90,
    consultations: 2,
    cycle: 'MONTHLY'
  }
};
```

## 📊 Monitoramento

### Logs
- **Aplicação**: Logs estruturados com Winston
- **WhatsApp**: Logs de notificações
- **Pagamentos**: Logs de transações
- **Erros**: Logs de erros e exceções

### Métricas
- **Consultas**: Total de consultas realizadas
- **Pagamentos**: Volume de transações
- **Usuários**: Crescimento de usuários
- **Performance**: Tempo de resposta da API

## 🤝 Contribuição

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. **Push** para a branch (`git push origin feature/AmazingFeature`)
5. **Abra** um Pull Request

### Padrões de Código

- **TypeScript** para tipagem
- **ESLint** para linting
- **Prettier** para formatação
- **Conventional Commits** para commits
- **Testes** para novas funcionalidades

---

**Desenvolvido com ❤️ pela equipe ZapVida**
