#!/bin/bash

# ===========================================
# SCRIPT DE BUILD DOCKER PARA DIFERENTES AMBIENTES
# ===========================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para mostrar ajuda
show_help() {
    echo -e "${BLUE}Uso: $0 [AMBIENTE] [TAG]${NC}"
    echo ""
    echo "Ambientes disponíveis:"
    echo "  staging    - Build para staging.zapvida.com"
    echo "  production - Build para zapvida.com"
    echo "  local      - Build para localhost (desenvolvimento)"
    echo ""
    echo "Exemplos:"
    echo "  $0 staging v1.0.0"
    echo "  $0 production latest"
    echo "  $0 local dev"
    echo ""
    echo "Variáveis de ambiente necessárias:"
    echo "  - DATABASE_URL"
    echo "  - DIRECT_URL"
    echo "  - AUTH_SECRET"
    echo "  - SUPABASE_SERVICE_ROLE_KEY"
}

# Verificar se o ambiente foi fornecido
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

ENVIRONMENT=$1
TAG=${2:-latest}

echo -e "${BLUE}🐳 Building Docker image for ${ENVIRONMENT} environment...${NC}"

# Configurar variáveis baseadas no ambiente
case $ENVIRONMENT in
    "staging")
        echo -e "${YELLOW}📋 Configurando para STAGING (staging.zapvida.com)${NC}"
        export NEXT_PUBLIC_SITE_URL="https://staging.zapvida.com"
        IMAGE_TAG="zapvida-staging:${TAG}"
        ;;
    "production")
        echo -e "${YELLOW}📋 Configurando para PRODUCTION (zapvida.com)${NC}"
        export NEXT_PUBLIC_SITE_URL="https://zapvida.com"
        IMAGE_TAG="zapvida-production:${TAG}"
        ;;
    "local")
        echo -e "${YELLOW}📋 Configurando para LOCAL (localhost:3000)${NC}"
        export NEXT_PUBLIC_SITE_URL="http://localhost:3000"
        IMAGE_TAG="zapvida-local:${TAG}"
        ;;
    *)
        echo -e "${RED}❌ Ambiente inválido: $ENVIRONMENT${NC}"
        show_help
        exit 1
        ;;
esac

# Verificar se as variáveis obrigatórias estão definidas
echo -e "${BLUE}🔍 Verificando variáveis de ambiente...${NC}"

if [ -z "$DATABASE_URL" ]; then
    echo -e "${RED}❌ DATABASE_URL não definida${NC}"
    exit 1
fi

if [ -z "$AUTH_SECRET" ]; then
    echo -e "${RED}❌ AUTH_SECRET não definida${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Variáveis de ambiente OK${NC}"

# Build da imagem
echo -e "${BLUE}🔨 Building Docker image: ${IMAGE_TAG}${NC}"

docker build \
    --build-arg NEXT_PUBLIC_SITE_URL="$NEXT_PUBLIC_SITE_URL" \
    --build-arg NEXT_PUBLIC_SUPABASE_URL="$NEXT_PUBLIC_SUPABASE_URL" \
    --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY="$NEXT_PUBLIC_SUPABASE_ANON_KEY" \
    --build-arg NEXT_PUBLIC_S3_ENDPOINT="$NEXT_PUBLIC_S3_ENDPOINT" \
    --build-arg NEXT_PUBLIC_AVATARS_BUCKET_NAME="$NEXT_PUBLIC_AVATARS_BUCKET_NAME" \
    --build-arg NEXT_PUBLIC_UPLOADS_BUCKET_NAME="$NEXT_PUBLIC_UPLOADS_BUCKET_NAME" \
    --build-arg NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET="$NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET" \
    --build-arg NEXT_PUBLIC_LIVEKIT_URL="$NEXT_PUBLIC_LIVEKIT_URL" \
    --build-arg NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="$NEXT_PUBLIC_GOOGLE_ANALYTICS_ID" \
    --build-arg NEXT_PUBLIC_POSTHOG_KEY="$NEXT_PUBLIC_POSTHOG_KEY" \
    --build-arg NEXT_PUBLIC_POSTHOG_HOST="$NEXT_PUBLIC_POSTHOG_HOST" \
    --build-arg NEXT_PUBLIC_ZAPCHAT_V2_ENABLED="$NEXT_PUBLIC_ZAPCHAT_V2_ENABLED" \
    --build-arg NEXT_PUBLIC_ZAPCHAT_V2_BETA="$NEXT_PUBLIC_ZAPCHAT_V2_BETA" \
    -t "$IMAGE_TAG" \
    .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build concluído com sucesso!${NC}"
    echo -e "${BLUE}📦 Imagem: ${IMAGE_TAG}${NC}"
    echo -e "${BLUE}🌐 URL: ${NEXT_PUBLIC_SITE_URL}${NC}"
else
    echo -e "${RED}❌ Build falhou${NC}"
    exit 1
fi
