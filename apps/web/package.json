{"dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@aws-sdk/client-s3": "3.437.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hookform/resolvers": "^3.9.1", "@livekit/components-react": "^2.8.1", "@livekit/components-styles": "^1.1.4", "@next/third-parties": "^15.2.4", "@node-rs/argon2": "^2.0.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.59.16", "@tanstack/react-table": "^8.20.5", "@trpc/client": "11.0.0-rc.601", "@trpc/react-query": "11.0.0-rc.601", "@zapvida/ai": "workspace:*", "ai": "^4.3.16", "api": "workspace:*", "boring-avatars": "^1.11.2", "change-case": "^5.4.4", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "cleave.js": "^1.6.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cropperjs": "^2.0.0", "database": "workspace:*", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "i18n": "workspace:*", "jotai": "2.8.0", "js-cookie": "^3.0.5", "livekit-client": "^2.9.4", "livekit-server-sdk": "^2.10.2", "logs": "workspace:*", "lucide-react": "^0.454.0", "mail": "workspace:*", "next": "15.0.2", "next-intl": "3.23.5", "next-themes": "^0.3.0", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "oslo": "^1.2.1", "payment": "^2.4.7", "pg": "^8.14.0", "posthog-js": "^1.266.2", "react": "19.0.0-rc-65a56d0e-20241020", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.1", "react-dom": "19.0.0-rc-65a56d0e-20241020", "react-dropzone": "^14.2.10", "react-hook-form": "^7.53.1", "react-icons": "^5.5.0", "react-imask": "^7.6.1", "recharts": "^2.15.1", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.1", "storage": "workspace:*", "superjson": "^2.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "tailwindcss": "3.4.14", "utils": "workspace:*", "uuid": "^11.0.2", "zod": "^3.23.8", "@content-collections/next": "^0.2.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@content-collections/core": "^0.7.2", "@content-collections/mdx": "^0.2.0", "@mdx-js/mdx": "^3.1.0", "@shikijs/rehype": "^1.22.2", "@types/cookie": "^0.6.0", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "22.8.4", "@types/nprogress": "^0.2.3", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@types/uuid": "^10.0.0", "auth": "workspace:*", "autoprefixer": "10.4.20", "cypress": "^13.15.1", "dotenv-cli": "^7.4.2", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.4.47", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.8", "tailwind-config": "workspace:*", "tsconfig": "workspace:*"}, "name": "web", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "e2e": "dotenv -c -e ../../.env -- start-server-and-test dev http://localhost:3000 \"cypress open --e2e\"", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.1.0"}