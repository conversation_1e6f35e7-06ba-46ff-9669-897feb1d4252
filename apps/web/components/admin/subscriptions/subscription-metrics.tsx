"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { TrendingUp, TrendingDown, Users, DollarSign, UserX, UserPlus } from "lucide-react";

interface SubscriptionMetrics {
  mrr: number;
  churnRate: number;
  topDoctors: Array<{
    id: string;
    name: string;
    revenue: number;
    appointments: number;
  }>;
  mrrEvolution: Array<{
    month: string;
    mrr: number;
  }>;
  metrics: {
    activeSubscriptions: number;
    pastDueSubscriptions: number;
    canceledThisMonth: number;
    newThisMonth: number;
  };
}

export function SubscriptionMetrics() {
  const [data, setData] = useState<SubscriptionMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchMetrics();
  }, []);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/subscriptions/metrics');
      
      if (!response.ok) {
        throw new Error('Erro ao carregar métricas');
      }
      
      const metrics = await response.json();
      setData(metrics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getMrrTrend = () => {
    if (!data?.mrrEvolution || data.mrrEvolution.length < 2) return null;
    
    const current = data.mrrEvolution[data.mrrEvolution.length - 1].mrr;
    const previous = data.mrrEvolution[data.mrrEvolution.length - 2].mrr;
    const change = ((current - previous) / previous) * 100;
    
    return {
      value: change,
      isPositive: change >= 0
    };
  };

  if (loading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-24 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p>Erro ao carregar métricas: {error}</p>
            <button 
              onClick={fetchMetrics}
              className="mt-2 text-sm underline hover:no-underline"
            >
              Tentar novamente
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) return null;

  const mrrTrend = getMrrTrend();

  return (
    <div className="space-y-6">
      {/* Métricas principais */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">MRR</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(data.mrr)}</div>
            {mrrTrend && (
              <div className={`text-xs flex items-center ${
                mrrTrend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {mrrTrend.isPositive ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(Math.abs(mrrTrend.value))} vs mês anterior
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Churn</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(data.churnRate)}</div>
            <p className="text-xs text-muted-foreground">
              Últimos 30 dias
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assinaturas Ativas</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.metrics.activeSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              {data.metrics.pastDueSubscriptions} em atraso
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Novas Assinaturas</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              +{data.metrics.newThisMonth}
            </div>
            <p className="text-xs text-muted-foreground">
              {data.metrics.canceledThisMonth} canceladas este mês
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Top médicos */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Top 5 Médicos por Receita (30 dias)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.topDoctors.map((doctor, index) => (
              <div key={doctor.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center">
                    {index + 1}
                  </Badge>
                  <div>
                    <p className="font-medium">{doctor.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {doctor.appointments} consulta(s)
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-600">
                    {formatCurrency(doctor.revenue)}
                  </p>
                </div>
              </div>
            ))}
            {data.topDoctors.length === 0 && (
              <p className="text-center text-muted-foreground py-4">
                Nenhum dado disponível para o período
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
