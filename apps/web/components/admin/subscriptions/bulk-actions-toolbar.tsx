"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { Loader2, Play, Pause, X, Download } from "lucide-react";
import { toast } from "sonner";

interface BulkActionsToolbarProps {
  selectedIds: string[];
  onSelectionChange: (ids: string[]) => void;
  onActionComplete: () => void;
}

export function BulkActionsToolbar({ 
  selectedIds, 
  onSelectionChange, 
  onActionComplete 
}: BulkActionsToolbarProps) {
  const [selectedAction, setSelectedAction] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const handleBulkAction = async () => {
    if (!selectedAction || selectedIds.length === 0) {
      toast.error("Selecione uma ação e pelo menos uma assinatura");
      return;
    }

    const actionLabels = {
      pause: "pausar",
      reactivate: "reativar", 
      cancel: "cancelar"
    };

    const actionLabel = actionLabels[selectedAction as keyof typeof actionLabels] || selectedAction;

    const confirmed = confirm(
      `Tem certeza que deseja ${actionLabel} ${selectedIds.length} assinatura(s) selecionada(s)?`
    );

    if (!confirmed) return;

    try {
      setIsLoading(true);
      
      const response = await fetch('/api/admin/subscriptions/bulk-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: selectedAction,
          subscriptionIds: selectedIds
        })
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.invalidSubscriptions) {
          toast.error(
            `${result.error}. Assinaturas inválidas: ${result.invalidSubscriptions.map((s: any) => s.patientName).join(', ')}`
          );
        } else {
          toast.error(result.error || 'Erro ao executar ação');
        }
        return;
      }

      toast.success(result.message);
      onSelectionChange([]);
      setSelectedAction("");
      onActionComplete();

    } catch (error) {
      console.error('Erro ao executar ação em lote:', error);
      toast.error('Erro interno do servidor');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportCSV = async () => {
    try {
      setIsLoading(true);
      
      // Aqui você implementaria a lógica de exportação
      // Por enquanto, vamos simular
      toast.info("Funcionalidade de exportação será implementada em breve");
      
    } catch (error) {
      console.error('Erro ao exportar:', error);
      toast.error('Erro ao exportar dados');
    } finally {
      setIsLoading(false);
    }
  };

  const clearSelection = () => {
    onSelectionChange([]);
    setSelectedAction("");
  };

  if (selectedIds.length === 0) {
    return (
      <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
        <div className="text-sm text-muted-foreground">
          Selecione assinaturas para executar ações em lote
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportCSV}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          Exportar CSV
        </Button>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {selectedIds.length} selecionada(s)
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearSelection}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <Select value={selectedAction} onValueChange={setSelectedAction}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Selecionar ação" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="pause">
              <div className="flex items-center">
                <Pause className="h-4 w-4 mr-2" />
                Pausar assinaturas
              </div>
            </SelectItem>
            <SelectItem value="reactivate">
              <div className="flex items-center">
                <Play className="h-4 w-4 mr-2" />
                Reativar assinaturas
              </div>
            </SelectItem>
            <SelectItem value="cancel">
              <div className="flex items-center">
                <X className="h-4 w-4 mr-2" />
                Cancelar assinaturas
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportCSV}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Download className="h-4 w-4 mr-2" />
          )}
          Exportar Selecionadas
        </Button>

        <Button
          onClick={handleBulkAction}
          disabled={!selectedAction || isLoading}
          size="sm"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : null}
          Executar Ação
        </Button>
      </div>
    </div>
  );
}
