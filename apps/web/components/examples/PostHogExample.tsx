'use client';

import { useAnalytics } from '../../hooks/useAnalytics';
import { Button } from '@ui/components/button';

export function PostHogExample() {
  const { track, identify, setPersonProperties, isFeatureEnabled } = useAnalytics();

  const handleTrackEvent = () => {
    track('button_clicked', {
      button_name: 'example_button',
      page: 'example_page',
    });
  };

  const handleIdentifyUser = () => {
    identify('user_123', {
      email: '<EMAIL>',
      name: 'Example User',
      role: 'patient',
    });
  };

  const handleSetPersonProperties = () => {
    setPersonProperties({
      subscription_plan: 'premium',
      last_login: new Date().toISOString(),
    });
  };

  const isFeatureEnabledExample = isFeatureEnabled('new_feature');

  return (
    <div className="space-y-4 p-4 border rounded-lg">
      <h3 className="text-lg font-semibold">PostHog Example</h3>

      <div className="space-y-2">
        <Button onClick={handleTrackEvent}>
          Track Event
        </Button>

        <Button onClick={handleIdentifyUser}>
          Identify User
        </Button>

        <Button onClick={handleSetPersonProperties}>
          Set Person Properties
        </Button>

        <div className="text-sm text-gray-600">
          Feature Flag Status: {isFeatureEnabledExample ? 'Enabled' : 'Disabled'}
        </div>
      </div>
    </div>
  );
}
