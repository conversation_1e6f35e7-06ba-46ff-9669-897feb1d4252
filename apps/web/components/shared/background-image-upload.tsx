"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Card, CardContent } from "@ui/components/card";
import { ImageIcon, Upload, X, Check } from "lucide-react";
import { cn } from "@ui/lib";
import { useDropzone } from "react-dropzone";

interface BackgroundImageUploadProps {
  currentImage?: string | null;
  onImageChange: (imageUrl: string | null) => void;
  onImageUpload?: (file: File) => Promise<string>;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  showPreview?: boolean;
  aspectRatio?: "square" | "video" | "auto";
}

export function BackgroundImageUpload({
  currentImage,
  onImageChange,
  onImageUpload,
  className,
  placeholder = "Selecione uma imagem de fundo",
  disabled = false,
  showPreview = true,
  aspectRatio = "auto",
}: BackgroundImageUploadProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImage || null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update preview when currentImage changes
  useEffect(() => {
    setPreviewUrl(currentImage || null);
  }, [currentImage]);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;
      await handleImageUpload(acceptedFiles[0]);
    },
    accept: {
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
      "image/webp": [".webp"],
    },
    multiple: false,
    maxSize: 5242880, // 5MB
    disabled,
  });

  const handleImageUpload = async (file: File) => {
    if (!file) return;

    setError(null);
    setIsUploading(true);

    try {
      // Create preview URL
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);

      // If upload function is provided, use it
      if (onImageUpload) {
        const uploadedUrl = await onImageUpload(file);
        onImageChange(uploadedUrl);
        // Clean up preview URL and use uploaded URL
        URL.revokeObjectURL(preview);
        setPreviewUrl(uploadedUrl);
      } else {
        // Just use the preview URL
        onImageChange(preview);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erro ao fazer upload da imagem");
      setPreviewUrl(currentImage || null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  const handleRemoveImage = () => {
    if (previewUrl && previewUrl.startsWith("blob:")) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    onImageChange(null);
    setError(null);
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case "square":
        return "aspect-square";
      case "video":
        return "aspect-video";
      default:
        return "";
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Preview Area */}
      {showPreview && (
        <Card className="overflow-hidden">
          <CardContent className="p-0">
            <div
              className={cn(
                "relative w-full bg-muted/50",
                getAspectRatioClass(),
                previewUrl ? "min-h-[200px]" : "h-[200px]"
              )}
            >
              {previewUrl ? (
                <>
                  {/* Background Image */}
                  <div
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                    style={{
                      backgroundImage: `url(${previewUrl})`,
                    }}
                  />

                  {/* Overlay for better visibility */}
                  <div className="absolute inset-0 bg-black/20" />

                  {/* Remove button */}
                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={handleRemoveImage}
                    disabled={disabled || isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>

                  {/* Upload status */}
                  {isUploading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                      <div className="flex items-center gap-2 text-white">
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                        <span className="text-sm">Enviando...</span>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="flex h-full flex-col items-center justify-center text-muted-foreground">
                  <ImageIcon className="h-12 w-12 mb-2" />
                  <p className="text-sm">{placeholder}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Controls */}
      <div className="space-y-2">
        {/* Hidden file input */}
        <Input
          ref={fileInputRef}
          type="file"
          accept="image/png,image/jpeg,image/webp"
          onChange={handleFileInputChange}
          className="hidden"
          disabled={disabled}
        />

        {/* Upload button */}
        <div {...getRootProps()}>
          <Button
            type="button"
            variant="outline"
            className="w-full"
            onClick={handleButtonClick}
            disabled={disabled || isUploading}
          >
            <Upload className="h-4 w-4 mr-2" />
            {previewUrl ? "Alterar Imagem" : "Selecionar Imagem"}
          </Button>
        </div>

        {/* Error message */}
        {error && (
          <div className="flex items-center gap-2 text-sm text-destructive">
            <X className="h-4 w-4" />
            {error}
          </div>
        )}

        {/* Success message */}
        {previewUrl && !isUploading && !error && (
          <div className="flex items-center gap-2 text-sm text-green-600">
            <Check className="h-4 w-4" />
            Imagem selecionada com sucesso
          </div>
        )}
      </div>
    </div>
  );
}
