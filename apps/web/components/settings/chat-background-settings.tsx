"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { useToast } from "@ui/hooks/use-toast";
import { BackgroundImageUpload } from "../shared/background-image-upload";
import { apiClient } from "@shared/lib/api-client";
import { useUser } from "@saas/auth/hooks/use-user";
import { Settings, ImageIcon, RotateCcw } from "lucide-react";

interface ChatBackgroundSettingsProps {
  className?: string;
}

export function ChatBackgroundSettings({ className }: ChatBackgroundSettingsProps) {
  const { user, updateUser } = useUser();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [currentBackground, setCurrentBackground] = useState<string | null>(null);

  const getSignedUploadUrlMutation = apiClient.uploads.signedUploadUrl.useMutation();
  const updateUserMutation = apiClient.auth.update.useMutation();

  // Load current background from user settings
  useEffect(() => {
    if (user?.chatBackgroundUrl) {
      setCurrentBackground(user.chatBackgroundUrl);
    }
  }, [user?.chatBackgroundUrl]);

  const handleImageUpload = async (file: File): Promise<string> => {
    if (!user) throw new Error("Usuário não encontrado");

    setIsUploading(true);
    try {
      const path = `chat-backgrounds/${user.id}-${Date.now()}.${file.name.split('.').pop() || 'png'}`;
      const uploadUrl = await getSignedUploadUrlMutation.mutateAsync({
        path,
        bucket: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME as string,
      });

      const response = await fetch(uploadUrl, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (!response.ok) {
        throw new Error("Falha ao fazer upload da imagem");
      }

      // Get the public URL
      const publicUrl = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME}/${path}`;

      return publicUrl;
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageChange = async (imageUrl: string | null) => {
    if (!user) return;

    try {
      const updatedUser = await updateUserMutation.mutateAsync({
        chatBackgroundUrl: imageUrl,
      });

      updateUser({
        chatBackgroundUrl: updatedUser.chatBackgroundUrl,
      });

      setCurrentBackground(imageUrl);

      toast({
        title: "Fundo do chat atualizado",
        description: imageUrl
          ? "Sua imagem de fundo foi salva com sucesso"
          : "Fundo padrão restaurado",
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "Erro ao salvar configuração",
        description: "Não foi possível salvar a imagem de fundo",
        variant: "error",
      });
    }
  };

  const handleResetToDefault = () => {
    handleImageChange(null);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">Fundo do Chat</CardTitle>
        </div>
        <p className="text-sm text-muted-foreground">
          Personalize o fundo das suas conversas com uma imagem personalizada
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Background Preview */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Fundo Atual</h4>
          <div className="relative h-32 w-full rounded-lg border bg-muted/50 overflow-hidden">
            {currentBackground ? (
              <div
                className="h-full w-full bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: `url(${currentBackground})`,
                }}
              />
            ) : (
              <div className="flex h-full items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">Fundo padrão</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Upload Component */}
        <BackgroundImageUpload
          currentImage={currentBackground}
          onImageChange={handleImageChange}
          onImageUpload={handleImageUpload}
          placeholder="Selecione uma imagem para o fundo do chat"
          disabled={isUploading}
          aspectRatio="video"
        />

        {/* Reset Button */}
        {currentBackground && (
          <Button
            variant="outline"
            onClick={handleResetToDefault}
            disabled={isUploading}
            className="w-full"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Restaurar Fundo Padrão
          </Button>
        )}

        {/* Tips */}
        <div className="rounded-lg bg-muted/50 p-4">
          <h5 className="text-sm font-medium mb-2">Dicas para melhor resultado:</h5>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Use imagens com resolução alta (1920x1080 ou superior)</li>
            <li>• Formatos recomendados: JPG, PNG, WebP</li>
            <li>• Tamanho máximo: 5MB</li>
            <li>• Imagens muito claras podem dificultar a leitura das mensagens</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
