"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { BackgroundImageUpload } from "../shared/background-image-upload";
import { Button } from "@ui/components/button";
import { ImageIcon } from "lucide-react";

export function BackgroundImageDemo() {
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null);
  const [messages] = useState([
    { id: 1, text: "Olá! Como posso ajudar você hoje?", sender: "other" },
    { id: 2, text: "Preciso de ajuda com minha consulta", sender: "me" },
    { id: 3, text: "Claro! Vou te ajudar com isso.", sender: "other" },
  ]);

  const handleImageChange = (imageUrl: string | null) => {
    setBackgroundImage(imageUrl);
  };

  const handleImageUpload = async (file: File): Promise<string> => {
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Create a local URL for demo purposes
    return URL.createObjectURL(file);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Demo: Background Image Upload</h1>
        <p className="text-muted-foreground">
          Este componente demonstra como usar o BackgroundImageUpload para personalizar fundos de chat
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Upload Component */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Configurar Fundo
            </CardTitle>
          </CardHeader>
          <CardContent>
            <BackgroundImageUpload
              currentImage={backgroundImage}
              onImageChange={handleImageChange}
              onImageUpload={handleImageUpload}
              placeholder="Selecione uma imagem para o fundo do chat"
              aspectRatio="video"
            />
          </CardContent>
        </Card>

        {/* Chat Preview */}
        <Card>
          <CardHeader>
            <CardTitle>Preview do Chat</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative h-96 rounded-lg border overflow-hidden">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
                style={{
                  backgroundImage: backgroundImage
                    ? `url(${backgroundImage})`
                    : 'url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23000000" fill-opacity="0.1"%3E%3Cpath d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/%3E%3C/g%3E%3C/svg%3E")',
                }}
              />

              {/* Chat Messages */}
              <div className="relative z-10 p-4 space-y-3 h-full overflow-y-auto">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-3 py-2 ${
                        message.sender === 'me'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-900'
                      }`}
                    >
                      {message.text}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-4 text-sm text-muted-foreground">
              <p>• A imagem de fundo aparece com 20% de opacidade</p>
              <p>• As mensagens ficam legíveis sobre o fundo</p>
              <p>• O fundo padrão é um padrão SVG sutil</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Como Usar</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">1. Importar o Componente</h4>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`import { BackgroundImageUpload } from "@/components/shared/background-image-upload";`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">2. Usar no JSX</h4>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`<BackgroundImageUpload
  currentImage={backgroundImage}
  onImageChange={setBackgroundImage}
  onImageUpload={handleImageUpload}
  placeholder="Selecione uma imagem"
  aspectRatio="video"
/>`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium mb-2">3. Aplicar como Fundo</h4>
            <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`<div
  className="absolute inset-0 opacity-20"
  style={{
    backgroundImage: backgroundImage
      ? \`url(\${backgroundImage})\`
      : 'url("default-pattern.svg")',
  }}
/>`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
