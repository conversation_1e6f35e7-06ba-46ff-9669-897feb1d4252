"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { RefreshCw, Bug, Eye, EyeOff } from "lucide-react";

interface DebugPanelProps {
  consultations: any[];
  selectedConsultation: any;
  isLoading: boolean;
  error: string | null;
  onRefresh: () => void;
}

export function DebugPanel({
  consultations,
  selectedConsultation,
  isLoading,
  error,
  onRefresh
}: DebugPanelProps) {
  const [isVisible, setIsVisible] = useState(false);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-white shadow-lg"
        >
          <Bug className="w-4 h-4 mr-2" />
          Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80 max-h-96 overflow-y-auto">
      <Card className="bg-white shadow-lg">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Bug className="w-4 h-4" />
              Debug Panel
            </CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={onRefresh}
                size="sm"
                variant="outline"
                disabled={isLoading}
              >
                <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                onClick={() => setIsVisible(false)}
                size="sm"
                variant="ghost"
              >
                <EyeOff className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          <div>
            <strong>Status:</strong>
            <Badge variant={isLoading ? "secondary" : "default"} className="ml-2">
              {isLoading ? "Carregando" : "Pronto"}
            </Badge>
          </div>

          <div>
            <strong>Consultas:</strong> {consultations.length}
          </div>

          <div>
            <strong>Selecionada:</strong> {selectedConsultation?.id || "Nenhuma"}
          </div>

          {error && (
            <div className="text-red-600">
              <strong>Erro:</strong> {error}
            </div>
          )}

          <div>
            <strong>Consultas:</strong>
            <div className="mt-1 space-y-1 max-h-32 overflow-y-auto">
              {consultations.map((c, i) => (
                <div key={c.id} className="text-xs p-1 bg-gray-50 rounded">
                  <div>ID: {c.id}</div>
                  <div>Status: {c.status}</div>
                  <div>Paciente: {c.patient_name}</div>
                  <div>Plantão: {c.is_on_duty ? "Sim" : "Não"}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
