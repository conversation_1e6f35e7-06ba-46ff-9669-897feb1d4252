import Script from 'next/script';

export function MedicalBusinessSchema() {
  const schema = {
    "@context": "https://schema.org",
    "@type": "MedicalBusiness",
    "name": "ZapVida",
    "description": "Plataforma de consulta online via WhatsApp. Telemedicina 24h com médicos especialistas. Prescrição digital e atendimento imediato.",
    "url": "https://zapvida.com",
    "logo": "https://zapvida.com/images/logo.png",
    "image": "https://zapvida.com/images/og-image.png",
    "telephone": "+55-11-99999-9999",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "BR",
      "addressRegion": "São Paulo",
      "addressLocality": "São Paulo"
    },
    "openingHours": "Mo-Su 00:00-23:59",
    "priceRange": "R$ 80 - R$ 200",
    "paymentAccepted": "Credit Card, PIX, Boleto",
    "currenciesAccepted": "BRL",
    "medicalSpecialty": [
      "General Practice",
      "Dermatology",
      "Cardiology",
      "Pediatrics",
      "Gynecology",
      "Psychiatry",
      "Endocrinology"
    ],
    "serviceType": "Telemedicine",
    "areaServed": {
      "@type": "Country",
      "name": "Brazil"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Serviços de Telemedicina",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Consulta Online WhatsApp",
            "description": "Consulta médica online via WhatsApp 24h com especialistas"
          },
          "price": "80.00",
          "priceCurrency": "BRL",
          "availability": "https://schema.org/InStock"
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Prescrição Digital",
            "description": "Receitas médicas digitais válidas em farmácias"
          },
          "price": "0.00",
          "priceCurrency": "BRL",
          "availability": "https://schema.org/InStock"
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "30000",
      "bestRating": "5",
      "worstRating": "1"
    },
    "sameAs": [
      "https://www.instagram.com/zapvida",
      "https://www.facebook.com/zapvida",
      "https://www.linkedin.com/company/zapvida"
    ]
  };

  return (
    <Script
      id="medical-business-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(schema)
      }}
    />
  );
}
