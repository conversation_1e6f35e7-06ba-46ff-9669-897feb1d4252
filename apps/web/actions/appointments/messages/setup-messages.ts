"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";
import fs from "fs";
import path from "path";

export async function setupMessagesTable() {
  try {
    // Verificar se o usuário é administrador
    const session = await currentUser();
    if (!session?.user) {
      return { success: false, error: "Não autorizado" };
    }

    // Verificar se o usuário tem a role ADMIN
    if (!session.user.roles?.includes("ADMIN")) {
      return { success: false, error: "Apenas administradores podem executar esta ação" };
    }

    // Caminho para o arquivo SQL
    const sqlFilePath = path.join(process.cwd(), "actions/appointments/messages/create-messages-table.sql");

    // Ler o conteúdo do arquivo SQL
    let sqlContent;
    try {
      sqlContent = fs.readFileSync(sqlFilePath, "utf8");
    } catch (error) {
      console.error("Erro ao ler arquivo SQL:", error);
      return { success: false, error: "Erro ao ler arquivo SQL" };
    }

    // Executar o SQL usando o cliente admin do Supabase
    const adminSupabase = createAdminClient();
    const { error } = await adminSupabase.rpc("exec_sql", { sql: sqlContent });

    if (error) {
      console.error("Erro ao executar SQL:", error);
      return { success: false, error: `Erro ao executar SQL: ${error.message}` };
    }

    return { success: true };
  } catch (error) {
    console.error("Erro ao configurar tabela de mensagens:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido ao configurar tabela de mensagens"
    };
  }
}
