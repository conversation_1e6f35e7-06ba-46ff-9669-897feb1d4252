// actions/appointments/messages/messages.ts
"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";
import { v4 as uuidv4 } from "uuid";

// Função para adaptar do snake_case para camelCase
function toCamelCase(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    result[camelKey] = value;
  }
  return result;
}

// Função para adaptar de camelCase para snake_case
function toSnakeCase(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/([A-Z])/g, "_$1").toLowerCase();
    result[snakeKey] = value;
  }
  return result;
}

export async function getAppointmentMessages(appointmentId: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    const adminSupabase = createAdminClient();

    // Usar camelCase para compatibilidade com o banco
    const { data, error } = await adminSupabase
      .from("messages")
      .select("*")
      .eq("appointmentId", appointmentId)
      .order("createdAt", { ascending: true });

    if (error) throw error;

    // Não precisamos converter pois já está em camelCase
    return { messages: Array.isArray(data) ? data : [], error: null };
  } catch (error) {
    console.error("Error fetching messages:", error);
    return {
      messages: [],
      error: error instanceof Error ? error.message : "Erro ao buscar mensagens"
    };
  }
}

// Enviar mensagem de texto
export async function sendTextMessage(appointmentId: string, content: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Dados em camelCase para o banco
    const messageData = {
      id: uuidv4(),
      appointmentId,
      content,
      type: "TEXT",
      senderId: session.user.id,
      senderRole: session.user.role,
      createdAt: new Date().toISOString(),
    };

    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from("messages")
      .insert(messageData)
      .select();

    if (error) {
      throw error;
    }

    return { success: true, message: data?.[0] };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar mensagem"
    };
  }
}

// Upload de arquivo e envio de mensagem com anexo
export async function sendAttachment(
  appointmentId: string,
  file: File,
  fileType: string,
  fileName: string,
  fileSize: number,
  fileBuffer: ArrayBuffer
) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    const adminSupabase = createAdminClient();

    const path = `appointments/${appointmentId}/${Date.now()}_${fileName}`;
    const { data: uploadData, error: uploadError } = await adminSupabase.storage
      .from("chat_attachments")
      .upload(path, fileBuffer, {
        contentType: fileType,
        upsert: true,
      });

    if (uploadError) throw uploadError;

    const { data: { publicUrl } } = adminSupabase.storage
      .from("chat_attachments")
      .getPublicUrl(path);

    // Dados em camelCase para o banco
    const messageData = {
      id: uuidv4(),
      appointmentId,
      senderId: session.user.id,
      type: "FILE",
      content: publicUrl,
      senderRole: session.user.role,
      createdAt: new Date().toISOString(),
      metadata: {
        fileName,
        contentType: fileType,
        size: fileSize,
        path,
      },
    };

    const { data, error } = await adminSupabase
      .from("messages")
      .insert(messageData)
      .select()
      .single();

    if (error) throw error;

    return { success: true, message: data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar arquivo"
    };
  }
}

// Enviar mensagem de áudio
export async function sendAudioMessage(
  appointmentId: string,
  audioBuffer: ArrayBuffer
) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    const adminSupabase = createAdminClient();

    const fileName = `appointments/${appointmentId}/${Date.now()}.webm`;
    const { data: uploadData, error: uploadError } = await adminSupabase.storage
      .from("chat_audio")
      .upload(fileName, audioBuffer, {
        contentType: "audio/webm",
        upsert: true,
      });

    if (uploadError) throw uploadError;

    const { data: { publicUrl } } = adminSupabase.storage
      .from("chat_audio")
      .getPublicUrl(fileName);

    // Dados em camelCase para o banco
    const messageData = {
      id: uuidv4(),
      appointmentId,
      senderId: session.user.id,
      type: "AUDIO",
      content: publicUrl,
      senderRole: session.user.role,
      createdAt: new Date().toISOString(),
      metadata: {
        fileName,
        contentType: "audio/webm",
      },
    };

    const { data, error } = await adminSupabase
      .from("messages")
      .insert(messageData)
      .select()
      .single();

    if (error) throw error;

    return { success: true, message: data };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar mensagem de áudio"
    };
  }
}

// Finalizar consulta (encerrar)
export async function endConsultation(appointmentId: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário é médico
    if (session.user.role !== 'DOCTOR') {
      throw new Error("Apenas médicos podem encerrar consultas");
    }

    const adminSupabase = createAdminClient();

    // Atualizar o status da consulta para COMPLETED
    const { data, error } = await adminSupabase
      .from("appointments")
      .update({ status: "COMPLETED", updatedAt: new Date().toISOString() })
      .eq("id", appointmentId)
      .select()
      .single();

    if (error) throw error;

    // Enviar mensagem do sistema informando que a consulta foi encerrada
    const messageData = {
      id: uuidv4(),
      appointmentId,
      senderId: session.user.id,
      type: "SYSTEM",
      content: "Consulta encerrada pelo médico",
      createdAt: new Date().toISOString(),
    };

    await adminSupabase
      .from("messages")
      .insert(messageData);

    return {
      success: true,
      appointment: data
    };
  } catch (error) {
    console.error("Error ending consultation:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao finalizar consulta"
    };
  }
}

// Wrapper function para compatibilidade com chamadas antigas
export async function sendAttachmentWrapper(
  appointmentId: string,
  file: File,
  userId: string
) {
  try {
    const fileBuffer = await file.arrayBuffer();
    const result = await sendAttachment(
      appointmentId,
      file,
      file.type,
      file.name,
      file.size,
      fileBuffer
    );

    if (result.success && result.message) {
      return {
        success: true,
        attachment: {
          url: result.message.content,
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        }
      };
    } else {
      return {
        success: false,
        error: result.error || 'Erro ao enviar arquivo'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro ao enviar arquivo'
    };
  }
}
