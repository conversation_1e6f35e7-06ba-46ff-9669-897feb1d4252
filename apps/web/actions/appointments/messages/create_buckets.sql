-- Criar buckets para armazenar diferentes tipos de arquivos

-- 1. Bucket para avatares
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'Avatares de usuários', false)
ON CONFLICT (id) DO NOTHING;

-- 2. Bucket para anexos de chat
INSERT INTO storage.buckets (id, name, public)
VALUES ('chat_attachments', 'Anexos de mensagens do chat', false)
ON CONFLICT (id) DO NOTHING;

-- 3. Bucket para áudio de mensagens
INSERT INTO storage.buckets (id, name, public)
VALUES ('chat_audio', 'Áudios de mensagens', false)
ON CONFLICT (id) DO NOTHING;

-- 4. Bucket para documentos médicos
INSERT INTO storage.buckets (id, name, public)
VALUES ('medical_docs', 'Documentos médicos', false)
ON CONFLICT (id) DO NOTHING;

-- 5. Bucket para prescrições médicas
INSERT INTO storage.buckets (id, name, public)
VALUES ('prescriptions', 'Prescrições médicas', false)
ON CONFLICT (id) DO NOTHING;

-- 6. Bucket para documentos de verificação de médicos
INSERT INTO storage.buckets (id, name, public)
VALUES ('doctor_verification', 'Documentos de verificação de médicos', false)
ON CONFLICT (id) DO NOTHING;

-- 7. Bucket para formulários pré-anestésicos
INSERT INTO storage.buckets (id, name, public)
VALUES ('pre_anesthetic_forms', 'Formulários pré-anestésicos', false)
ON CONFLICT (id) DO NOTHING;
