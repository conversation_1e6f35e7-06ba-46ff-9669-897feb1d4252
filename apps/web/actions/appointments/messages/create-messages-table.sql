-- <PERSON><PERSON><PERSON> tabela de mensagens se não existir
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "appointmentId" UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  "senderId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL DEFAULT 'text',
  content TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT now(),
  metadata JSONB,
  "senderRole" TEXT
);

-- Criar índices para melhorar a performance
CREATE INDEX IF NOT EXISTS messages_appointmentId_idx ON messages("appointmentId");
CREATE INDEX IF NOT EXISTS messages_senderId_idx ON messages("senderId");
CREATE INDEX IF NOT EXISTS messages_createdAt_idx ON messages("createdAt");

-- Habilitar Row Level Security
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Políticas de segurança para leitura
CREATE POLICY IF NOT EXISTS "Usuários podem ler mensagens de suas consultas"
ON messages FOR SELECT
USING (
  "appointmentId" IN (
    SELECT id FROM appointments WHERE
    "patientId" = auth.uid() OR
    "doctorId" = auth.uid()
  )
);

-- Políticas de segurança para inserção
CREATE POLICY IF NOT EXISTS "Usuários podem enviar mensagens em suas consultas"
ON messages FOR INSERT
WITH CHECK (
  "senderId" = auth.uid() AND
  "appointmentId" IN (
    SELECT id FROM appointments WHERE
    "patientId" = auth.uid() OR
    "doctorId" = auth.uid()
  )
);

-- Criar buckets para armazenamento de arquivos
INSERT INTO storage.buckets (id, name, public)
VALUES ('chat_attachments', 'chat_attachments', false)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('chat_audio', 'chat_audio', false)
ON CONFLICT (id) DO NOTHING;

-- Políticas para acesso aos arquivos
CREATE POLICY IF NOT EXISTS "Usuários podem ler arquivos de suas consultas"
ON storage.objects FOR SELECT
USING (
  bucket_id IN ('chat_attachments', 'chat_audio') AND
  (storage.foldername(name))[1] IN (
    SELECT id::text FROM appointments WHERE
    "patientId" = auth.uid() OR
    "doctorId" = auth.uid()
  )
);

CREATE POLICY IF NOT EXISTS "Usuários podem enviar arquivos para suas consultas"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id IN ('chat_attachments', 'chat_audio') AND
  (storage.foldername(name))[1] IN (
    SELECT id::text FROM appointments WHERE
    "patientId" = auth.uid() OR
    "doctorId" = auth.uid()
  )
);
