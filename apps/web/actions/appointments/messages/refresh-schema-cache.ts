"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";

export async function refreshSchemaCache() {
  try {
    const session = await currentUser();
    if (!session?.user || session.user.role !== "ADMIN") {
      throw new Error("Apenas administradores podem executar esta ação");
    }

    // Executar o comando SQL para forçar o PostgREST a atualizar o cache de schema
    const adminSupabase = createAdminClient();

    // Tentar primeiro usar a função exec_sql, se já estiver criada
    const { error: rpcError } = await adminSupabase.rpc('exec_sql', {
      sql: "NOTIFY pgrst, 'reload schema';"
    }).catch(() => ({ error: { message: "RPC não disponível" } }));

    if (!rpcError) {
      return { success: true };
    }

    // Se não conseguir usar RPC, tentar uma alternativa - rodar uma consulta simples
    // que força o Supabase a atualizar seu cache
    const { error } = await adminSupabase
      .from('messages')
      .select('id')
      .limit(1);

    // Mesmo com erro, o cache deve ser atualizado
    return { success: true };
  } catch (error) {
    console.error("Error refreshing schema cache:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao atualizar cache de schema"
    };
  }
}
