"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { createAdminClient } from "@shared/lib/supabase/admin";

// Buscar status inicial da consulta
export async function getAppointmentStatus(appointmentId: string) {
  try {
    console.log(`Getting appointment status for ID: ${appointmentId}`);
    const session = await currentUser();

    if (!session?.user) {
      console.error("No authenticated user found");
      throw new Error("Não autorizado");
    }

    console.log(`User ${session.user.id} requesting appointment status`);
    const adminSupabase = createAdminClient();

    const { data, error } = await adminSupabase
      .from("appointments")
      .select("status")
      .eq("id", appointmentId)
      .single();

    if (error) {
      console.error("Supabase error fetching appointment status:", error);
      throw error;
    }

    if (!data) {
      console.error("No data returned for appointment status");
      throw new Error("Consulta não encontrada");
    }

    console.log(`Appointment ${appointmentId} status:`, data.status);
    return {
      status: data.status,
      isActive: data.status === "IN_PROGRESS"
    };
  } catch (error) {
    console.error("Error fetching appointment status:", error);
    // Return more detailed error information
    return {
      status: null,
      isActive: false,
      error: error instanceof Error ? error.message : "Erro ao buscar status da consulta"
    };
  }
}

// Atualizar status da consulta
export async function updateAppointmentStatus(
  appointmentId: string,
  active: boolean | string
) {
  try {
    console.log(`Updating appointment ${appointmentId} status to ${typeof active === 'string' ? active : (active ? "IN_PROGRESS" : "SCHEDULED")}`);

    const session = await currentUser();
    if (!session?.user) {
      console.error("No authenticated user found for status update");
      throw new Error("Não autorizado");
    }

    console.log(`User ${session.user.id} (role: ${session.user.role}) requesting status update`);

    // Verificar se o usuário é médico
    if (session.user.role !== "DOCTOR" && session.user.role !== "ADMIN") {
      console.error(`User ${session.user.id} with role ${session.user.role} attempted to update appointment status`);
      throw new Error("Apenas médicos podem atualizar o status da consulta");
    }

    let status: string;

    // Determinar o status com base no parâmetro active
    if (typeof active === "string") {
      // Se recebemos diretamente o status como string
      status = active;
    } else {
      // Comportamento anterior (compatibilidade)
      status = active ? "IN_PROGRESS" : "SCHEDULED";
    }

    // Validar se é um status válido
    if (!["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELED", "NO_SHOW"].includes(status)) {
      console.error(`Invalid status requested: ${status}`);
      throw new Error("Status inválido");
    }

    const adminSupabase = createAdminClient();
    console.log(`Updating appointment ${appointmentId} to status ${status} in database`);

    const { data, error } = await adminSupabase
      .from("appointments")
      .update({
        status,
      })
      .eq("id", appointmentId)
      .select();

    if (error) {
      console.error("Supabase error updating appointment status:", error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.error("No data returned after appointment update");
      throw new Error("Falha ao atualizar consulta");
    }

    console.log(`Appointment ${appointmentId} successfully updated to ${data[0].status}`);
    return {
      success: true,
      status: data[0].status,
      isActive: data[0].status === "IN_PROGRESS"
    };
  } catch (error) {
    console.error("Error updating appointment status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao atualizar status da consulta"
    };
  }
}
