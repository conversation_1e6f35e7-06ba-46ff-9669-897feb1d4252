"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { auth } from "@/lib/auth";
import { createClient } from "@/lib/supabase/server";

const scheduleBlockSchema = z.object({
  startTime: z.string().datetime(),
  endTime: z.string().datetime(),
  reason: z.string().optional(),
  type: z.enum(["VACATION", "HOLIDAY", "LUNCH", "PERSONAL", "OTHER"]),
});

export async function createScheduleBlock(
  doctorId: string,
  data: z.infer<typeof scheduleBlockSchema>,
) {
  try {
    const session = await auth();
    if (!session?.user) throw new Error("Não autorizado");

    const supabase = createClient();
    const validated = scheduleBlockSchema.parse(data);

    // Verificar se há conflitos
    const { data: conflicts } = await supabase
      .from("appointments")
      .select("id")
      .eq("doctorId", doctorId)
      .gte("scheduledAt", validated.startTime)
      .lte("scheduledAt", validated.endTime)
      .single();

    if (conflicts) {
      throw new Error("Existem consultas agendadas neste período");
    }

    // Criar bloqueio
    const { data: block, error } = await supabase
      .from("schedule_blocks")
      .insert({
        doctorId,
        ...validated,
      })
      .select()
      .single();

    if (error) throw error;

    revalidatePath(`/doctor/${doctorId}/schedule`);
    return { status: "success", data: block };
  } catch (error) {
    console.error("Error creating schedule block:", error);
    return { status: "error", message: error.message };
  }
}

export async function updateDoctorSchedule(
  doctorId: string,
  schedules: {
    weekDay: number;
    startTime: string;
    endTime: string;
    isEnabled: boolean;
  }[],
) {
  try {
    const session = await auth();
    if (!session?.user) throw new Error("Não autorizado");

    const supabase = createClient();

    // Primeiro, remover horários existentes
    await supabase.from("doctor_schedules").delete().eq("doctorId", doctorId);

    // Inserir novos horários
    const { data: newSchedules, error } = await supabase
      .from("doctor_schedules")
      .insert(
        schedules.map((schedule) => ({
          doctorId,
          ...schedule,
        })),
      )
      .select();

    if (error) throw error;

    revalidatePath(`/doctor/${doctorId}/schedule`);
    return { status: "success", data: newSchedules };
  } catch (error) {
    console.error("Error updating doctor schedule:", error);
    return { status: "error", message: error.message };
  }
}

export async function getAvailableSlots(doctorId: string, date: string) {
  try {
    const supabase = createClient();
    const targetDate = new Date(date);
    const weekDay = targetDate.getDay();

    // Buscar configuração do dia da semana
    const { data: schedule } = await supabase
      .from("doctor_schedules")
      .select("*")
      .eq("doctorId", doctorId)
      .eq("weekDay", weekDay)
      .eq("isEnabled", true)
      .single();

    if (!schedule) {
      return { status: "error", message: "Não há atendimento neste dia" };
    }

    // Buscar consultas existentes
    const { data: appointments } = await supabase
      .from("appointments")
      .select("scheduledAt, duration")
      .eq("doctorId", doctorId)
      .gte("scheduledAt", `${date}T00:00:00`)
      .lte("scheduledAt", `${date}T23:59:59`)
      .not("status", "eq", "CANCELED");

    // Buscar bloqueios
    const { data: blocks } = await supabase
      .from("schedule_blocks")
      .select("*")
      .eq("doctorId", doctorId)
      .overlaps("startTime", `${date}T00:00:00`, `${date}T23:59:59`);

    // Buscar time slots existentes
    const { data: existingSlots } = await supabase
      .from("time_slots")
      .select("*")
      .eq("doctorId", doctorId)
      .gte("startTime", `${date}T00:00:00`)
      .lte("startTime", `${date}T23:59:59`)
      .eq("isAvailable", true);

    // Gerar slots disponíveis
    const slots = generateTimeSlots(
      schedule.startTime,
      schedule.endTime,
      30, // duração padrão em minutos
      appointments || [],
      blocks || [],
      existingSlots || [],
      targetDate
    );

    return { status: "success", data: slots };
  } catch (error) {
    console.error("Error getting available slots:", error);
    return { status: "error", message: error.message };
  }
}

function generateTimeSlots(
  startTime: string,
  endTime: string,
  duration: number,
  appointments: any[],
  blocks: any[],
  existingSlots: any[],
  targetDate: Date
) {
  const slots = [];
  const start = new Date(targetDate);
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  start.setHours(startHour, startMinute, 0, 0);
  const end = new Date(targetDate);
  end.setHours(endHour, endMinute, 0, 0);

  while (start < end) {
    const slotEnd = new Date(start.getTime() + duration * 60000);

    // Verificar se o slot está disponível
    const isBlocked = blocks.some((block) => {
      const blockStart = new Date(block.startTime);
      const blockEnd = new Date(block.endTime);
      return (
        (start >= blockStart && start < blockEnd) ||
        (slotEnd > blockStart && slotEnd <= blockEnd)
      );
    });

    const isBooked = appointments.some((apt) => {
      const aptStart = new Date(apt.scheduledAt);
      const aptEnd = new Date(aptStart.getTime() + apt.duration * 60000);
      return (
        (start >= aptStart && start < aptEnd) ||
        (slotEnd > aptStart && slotEnd <= aptEnd)
      );
    });

    const existingSlot = existingSlots.find(
      (slot) =>
        new Date(slot.startTime).getTime() === start.getTime() &&
        new Date(slot.endTime).getTime() === slotEnd.getTime()
    );

    if (!isBlocked && !isBooked && existingSlot) {
      slots.push({
        id: existingSlot.id,
        startTime: start.toISOString(),
        endTime: slotEnd.toISOString(),
        available: true,
      });
    }

    start.setTime(slotEnd.getTime());
  }

  return slots;
}
