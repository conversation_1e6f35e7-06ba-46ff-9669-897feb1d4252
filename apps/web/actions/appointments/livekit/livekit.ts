"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { AccessToken } from "livekit-server-sdk";

const LIVEKIT_API_KEY = process.env.LIVEKIT_API_KEY;
const LIVEKIT_API_SECRET = process.env.LIVEKIT_API_SECRET;
const LIVEKIT_URL = process.env.NEXT_PUBLIC_LIVEKIT_URL;

export async function getLiveKitToken(appointmentId: string) {
	try {
		console.log("Starting token generation for appointment:", appointmentId);

		if (!LIVEKIT_API_KEY || !LIVEKIT_API_SECRET || !LIVEKIT_URL) {
			throw new Error("LiveKit configuration missing");
		}

		const session = await currentUser();
		if (!session?.user) {
			throw new Error("Não autorizado");
		}

		// Buscar a consulta usando Prisma
		const appointment = await db.appointment.findUnique({
			where: { id: appointmentId },
			select: {
				id: true,
				doctor: {
					select: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				},
				patient: {
					select: {
						user: {
							select: {
								id: true,
								name: true,
								email: true
							}
						}
					}
				}
			}
		});

		if (!appointment) {
			console.error("Appointment not found:", appointmentId);
			throw new Error("Consulta não encontrada");
		}

		console.log("Appointment found:", {
			id: appointment.id,
			doctorUserId: appointment.doctor.user.id,
			patientUserId: appointment.patient.user.id
		});

		const isDoctor = appointment.doctor.user.id === session.user.id;
		const isPatient = appointment.patient.user.id === session.user.id;
		const isAdmin = session.user.role === "ADMIN";

		// Verificar se o usuário tem permissão para acessar a consulta
		if (!isDoctor && !isPatient && !isAdmin) {
			throw new Error("Acesso não autorizado");
		}

		const roomName = `appointment-${appointmentId}`;

		// Criar token como string
		const at = new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
			identity: session.user.id,
			name: session.user.name ?? "Usuário",
		});

		// Adicionar permissões
		at.addGrant({
			room: roomName,
			roomJoin: true,
			canPublish: true,
			canSubscribe: true,
			canPublishData: true,
		});

		// Gerar token como string
		const token = await at.toJwt();

		// Preparar URL do servidor
		const serverUrl = LIVEKIT_URL.startsWith("wss://")
			? LIVEKIT_URL
			: `wss://${LIVEKIT_URL}`;

		console.log("Token generation completed:", {
			room: roomName,
			identity: session.user.id,
			role: isDoctor ? "DOCTOR" : "PATIENT",
			hasToken: typeof token === "string",
			tokenLength: token.length,
			url: serverUrl,
		});

		return {
			token,
			url: serverUrl,
			room: roomName,
		};
	} catch (error) {
		console.error("Error generating token:", error);
		throw error;
	}
}
