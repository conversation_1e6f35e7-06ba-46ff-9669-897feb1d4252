'use server';

import { db } from "database";

type AppointmentStatus = 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED';

export async function updateAppointmentStatus(
  appointmentId: string,
  status: AppointmentStatus,
  userId?: string // Opcional por enquanto para testes
) {
  try {
    console.log(`[updateAppointmentStatus] Updating appointment ${appointmentId} to status ${status}`);

    // Por enquanto, vamos permitir a atualização sem validação rigorosa para testes
    // TODO: Implementar validação completa quando o sistema estiver funcionando

    // Atualizar o status
    const updatedAppointment = await db.appointment.update({
      where: { id: appointmentId },
      data: {
        status,
        updatedAt: new Date()
      }
    });

    console.log(`[updateAppointmentStatus] Status updated successfully to ${status}`);

    // Criar mensagem de sistema se necessário
    if (userId) {
      try {
        if (status === 'IN_PROGRESS') {
          await db.message.create({
            data: {
              appointmentId,
              senderId: userId,
              type: "SYSTEM",
              content: 'Consulta iniciada',
              createdAt: new Date()
            }
          });
        } else if (status === 'COMPLETED') {
          await db.message.create({
            data: {
              appointmentId,
              senderId: userId,
              type: "SYSTEM",
              content: 'Consulta finalizada',
              createdAt: new Date()
            }
          });
        }
      } catch (messageError) {
        console.warn("[updateAppointmentStatus] Could not create system message:", messageError);
        // Não falhar se não conseguir criar a mensagem
      }
    }

    return { success: true, appointment: updatedAppointment };
  } catch (error) {
    console.error("[updateAppointmentStatus] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao atualizar status da consulta"
    };
  }
}
