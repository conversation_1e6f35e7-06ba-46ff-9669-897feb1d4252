// src/actions/appointments/get-doctor-appointments.ts
"use server";

import { AppointmentStatus } from "@prisma/client";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { endOfDay, startOfDay } from "date-fns";


interface GetDoctorAppointmentsParams {
  status?: AppointmentStatus[];
  date?: Date;
}

export async function getDoctorAppointments({
  status,
  date,
}: GetDoctorAppointmentsParams = {}) {
  try {
    const session = await currentUser();
    if (!session?.user) throw new Error("Não autorizado");

    // Build where clause
    const where = {
      payment_status: "PAID",
      doctor: {
        user_id: session.user.id,
      },
      ...(status && { status: { in: status } }),
      ...(date && {
        scheduled_at: {
          gte: startOfDay(date),
          lte: endOfDay(date),
        },
      }),
    };

    const appointments = await db.appointment.findMany({
      where,
      include: {
        patient: {
          include: {
            user: {
              select: {
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
        prescription: {
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        scheduledAt: "desc",
      },
    });

    return { appointments };
  } catch (error) {
    console.error("[GET_DOCTOR_APPOINTMENTS]", error);
    throw new Error("Erro ao buscar consultas");
  }
}
