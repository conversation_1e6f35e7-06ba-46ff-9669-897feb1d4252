// src/actions/patients/get-patient-appointments.ts
"use server";

import { AppointmentStatus } from "@prisma/client";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { endOfDay, startOfDay } from "date-fns";



interface GetPatientAppointmentsParams {
  status?: AppointmentStatus[];
  date?: Date;
  page?: number;
  perPage?: number;
}

export async function getPatientAppointments({
  status,
  date,
  page = 1,
  perPage = 10,
}: GetPatientAppointmentsParams = {}) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    const patient = await db.patient.findFirst({
      where: {
        userId: session.user.id,
      },
    });

    if (!patient) {
      throw new Error("Perfil de paciente não encontrado");
    }

    // Build where clause
    const where = {
      patient_id: patient.id,
      ...(status && { status: { in: status } }),
      ...(date && {
        scheduled_at: {
          gte: startOfDay(date),
          lte: endOfDay(date),
        },
      }),
    };

    // Get appointments
    const appointments = await db.appointment.findMany({
      where,
      include: {
        doctor: {
          include: {
            user: true,
            specialties: true,
          },
        },
        prescription: true,
      },
      orderBy: [
        {
          status: "asc",
        },
        {
          scheduledAt: "desc",
        },
      ],
      skip: (page - 1) * perPage,
      take: perPage,
    });

    const total = await db.appointment.count({ where });

    return {
      appointments,
      total,
      pages: Math.ceil(total / perPage),
    };
  } catch (error) {
    console.error("Error fetching appointments:", error);
    throw new Error("Erro ao buscar agendamentos");
  }
}
