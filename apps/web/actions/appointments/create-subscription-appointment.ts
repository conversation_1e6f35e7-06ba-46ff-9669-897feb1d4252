import { db } from 'database';
import { currentUser } from '@saas/auth/lib/current-user';
import { checkSubscriptionConsultationRights, recordConsultationUsage } from '../../lib/subscription-utils';

interface CreateSubscriptionAppointmentParams {
  doctorId: string;
  timeSlotId?: string; // Opcional agora
  scheduledAt: Date;
  duration?: number;
  consultType?: 'VIDEO' | 'AUDIO' | 'CHAT';
  symptoms?: string;
}

export async function createSubscriptionAppointment(params: CreateSubscriptionAppointmentParams) {
  const session = await currentUser();
  if (!session?.user) {
    throw new Error('Usuário não autenticado');
  }

  // Buscar paciente
  const patient = await db.patient.findFirst({
    where: { userId: session.user.id },
  });

  if (!patient) {
    throw new Error('Paciente não encontrado');
  }

  // Verificar direitos de consulta na assinatura
  const subscriptionCheck = await checkSubscriptionConsultationRights(patient.id);

  if (!subscriptionCheck.hasActiveSubscription) {
    throw new Error('Você não possui uma assinatura ativa. Assine o plano ZapVida Sempre para agendar consultas.');
  }

  if (!subscriptionCheck.canUseConsultation) {
    throw new Error(
      `Você esgotou suas 2 consultas mensais. Para atendimentos adicionais, você pode agendar uma consulta avulsa ou usar o plantão médico.`
    );
  }

  // Verificar se médico existe e está disponível
  const doctor = await db.doctor.findUnique({
    where: { id: params.doctorId },
    include: { user: true },
  });

  if (!doctor) {
    throw new Error('Médico não encontrado');
  }

  // Verificar se time slot está disponível (apenas se fornecido)
  let timeSlot = null;
  if (params.timeSlotId) {
    timeSlot = await db.timeSlot.findUnique({
      where: { id: params.timeSlotId },
    });

    if (!timeSlot || !timeSlot.isAvailable || timeSlot.appointmentId) {
      throw new Error('Horário não está mais disponível');
    }
  }

  try {
    // Criar appointment em transação (apenas operações críticas)
    const result = await db.$transaction(async (tx) => {
      // Criar appointment
      const appointment = await tx.appointment.create({
        data: {
          doctorId: params.doctorId,
          patientId: patient.id,
          scheduledAt: params.scheduledAt,
          duration: params.duration || doctor.consultationDuration || 30,
          status: 'SCHEDULED',
          paymentStatus: 'PAID', // Pagamento via subscription
          consultType: params.consultType || 'VIDEO',
          appointmentType: 'TELEMEDICINE',
          amount: 0, // Sem custo adicional para subscription
          symptoms: params.symptoms,
        },
      });

      // Marcar time slot como ocupado (apenas se existe)
      if (timeSlot) {
        await tx.timeSlot.update({
          where: { id: params.timeSlotId! },
          data: {
            appointmentId: appointment.id,
            isAvailable: false,
          },
        });
      }

      // Registrar uso da consulta (dentro da transação)
      try {
        await recordConsultationUsage(appointment.id, patient.id, 'SUBSCRIPTION');
      } catch (error) {
        console.error('Erro ao registrar uso da consulta:', error);
        throw error;
      }

      return appointment;
    }, {
      timeout: 10000, // Aumentar timeout para 10 segundos
    });

    // Criar notificações (fora da transação)
    await Promise.all([
      db.notification.create({
        data: {
          userId: doctor.userId,
          appointmentId: result.id,
          type: 'APPOINTMENT_CREATED',
          title: 'Nova Consulta Agendada (Assinatura)',
          message: `Nova consulta via assinatura agendada para ${params.scheduledAt.toLocaleDateString('pt-BR')} às ${params.scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}`,
        },
      }),
      db.notification.create({
        data: {
          userId: session.user.id,
          appointmentId: result.id,
          type: 'APPOINTMENT_CREATED',
          title: 'Consulta Agendada com Sucesso',
          message: `Sua consulta foi agendada para ${params.scheduledAt.toLocaleDateString('pt-BR')} às ${params.scheduledAt.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })} com Dr. ${doctor.user.name}`,
        },
      })
    ]);

    // Buscar dados atualizados da subscription
    try {
      const updatedCheck = await checkSubscriptionConsultationRights(patient.id);

      return {
        success: true,
        appointmentId: result.id,
        remainingConsultations: updatedCheck.remainingConsultations,
        subscriptionInfo: updatedCheck.subscriptionInfo,
      };
    } catch (error) {
      console.error('Erro ao buscar dados atualizados da subscription:', error);
      // Retornar sucesso mesmo se não conseguir buscar dados atualizados
      return {
        success: true,
        appointmentId: result.id,
        remainingConsultations: 0,
        subscriptionInfo: null,
      };
    }

  } catch (error: any) {
    console.error('[CREATE_SUBSCRIPTION_APPOINTMENT] Error:', error);
    throw new Error(error.message || 'Erro ao agendar consulta via assinatura');
  }
}

/**
 * Cancelar appointment via subscription e liberar consulta
 */
export async function cancelSubscriptionAppointment(appointmentId: string) {
  const session = await currentUser();
  if (!session?.user) {
    throw new Error('Usuário não autenticado');
  }

  const appointment = await db.appointment.findUnique({
    where: { id: appointmentId },
    include: {
      patient: { include: { user: true } },
      timeSlot: true,
    },
  });

  if (!appointment) {
    throw new Error('Consulta não encontrada');
  }

  // Verificar se é do usuário atual
  if (appointment.patient.userId !== session.user.id) {
    throw new Error('Você não tem permissão para cancelar esta consulta');
  }

  // Verificar se pode ser cancelada (pelo menos 24h de antecedência)
  const now = new Date();
  const appointmentTime = new Date(appointment.scheduledAt);
  const hoursUntilAppointment = (appointmentTime.getTime() - now.getTime()) / (1000 * 60 * 60);

  if (hoursUntilAppointment < 24) {
    throw new Error('Consultas só podem ser canceladas com pelo menos 24 horas de antecedência');
  }

  try {
    await db.$transaction(async (tx) => {
      // Cancelar appointment
      await tx.appointment.update({
        where: { id: appointmentId },
        data: { status: 'CANCELED' },
      });

      // Liberar time slot
      if (appointment.timeSlot) {
        await tx.timeSlot.update({
          where: { id: appointment.timeSlot.id },
          data: {
            appointmentId: null,
            isAvailable: true,
          },
        });
      }

      // Liberar consulta da subscription (função já criada)
      // A função releaseConsultationUsage vai decrementar o contador
    });

    return {
      success: true,
      message: 'Consulta cancelada com sucesso. Sua consulta foi devolvida ao seu plano.',
    };

  } catch (error: any) {
    console.error('[CANCEL_SUBSCRIPTION_APPOINTMENT] Error:', error);
    throw new Error(error.message || 'Erro ao cancelar consulta');
  }
}
