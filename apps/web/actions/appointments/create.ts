"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { TRPCError } from "@trpc/server";
import { AppointmentStatus, ConsultType, AppointmentType } from "@prisma/client";

interface CreateAppointmentParams {
  doctorId: string;
  timeSlotId: string;
  consultType: ConsultType;
  symptoms?: string;
}

export async function createAppointment({
  doctorId,
  timeSlotId,
  consultType,
  symptoms,
}: CreateAppointmentParams) {
  try {
    const user = await currentUser();
    if (!user) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Usuário não autenticado",
      });
    }

    // Buscar o time slot
    const timeSlot = await db.timeSlot.findUnique({
      where: { id: timeSlotId },
      include: { appointment: true },
    });

    if (!timeSlot) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "<PERSON><PERSON><PERSON><PERSON> não encontrado",
      });
    }

    if (!timeSlot.isAvailable || timeSlot.appointment) {
      throw new TRPCError({
        code: "CONFLICT",
        message: "<PERSON>r<PERSON><PERSON> indisponível",
      });
    }

    // Buscar o médico
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      select: {
        consultationPrice: true,
        consultationDuration: true,
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    if (!doctor) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Médico não encontrado",
      });
    }

    // Buscar o paciente
    const patient = await db.patient.findUnique({
      where: { userId: user.id },
    });

    if (!patient) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Paciente não encontrado",
      });
    }

    // Criar a consulta
    const appointment = await db.appointment.create({
      data: {
        doctorId,
        patientId: patient.id,
        scheduledAt: timeSlot.startTime,
        consultType,
        duration: doctor.consultationDuration,
        status: AppointmentStatus.SCHEDULED,
        symptoms,
        appointmentType: AppointmentType.TELEMEDICINE,
        amount: doctor.consultationPrice,
      },
    });

    // Atualizar o time slot
    await db.timeSlot.update({
      where: { id: timeSlotId },
      data: {
        isAvailable: false,
        appointmentId: appointment.id,
      },
    });

    return {
      status: "success",
      data: {
        id: appointment.id,
        scheduledAt: appointment.scheduledAt,
        doctorName: doctor.user.name,
      },
    };
  } catch (error) {
    console.error("Error creating appointment:", error);
    if (error instanceof TRPCError) {
      return {
        status: "error",
        message: error.message,
      };
    }
    return {
      status: "error",
      message: "Erro ao criar consulta",
    };
  }
}
