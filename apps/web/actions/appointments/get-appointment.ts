// src/actions/appointments/get-appointment.ts
"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

interface GetAppointmentOptions {
	includeDoctor?: boolean;
	includePrescription?: boolean;
	includeAttachments?: boolean;
}

export async function getAppointment(
	id: string,

) {
	try {


		console.log("id ====>>>> getAppointment ===>>>", id);

		const session = await currentUser();


		console.log("session ====>>>> getAppointment ===>>>", session);


		if (!session?.user) throw new Error("Não autorizado");

		return await db.appointment.findUnique({
			where: {
				id,
				// OR: [
				// 	{ patient: { userId: session.user.id } },
				// 	{ doctor: { userId: session.user.id } },
				// ],
			},
			include: {
				doctor: {
					include: {
						user: true,
						specialties: true,
					},
				},
				patient: {
					select: {
						user: true,
						birthDate: true,
						gender: true,
						allergies: true,
						chronicConditions: true,
					},
				},
				prescription: true,
				attachments: true,
			},
		});
	} catch (error) {
		console.error("[GET_APPOINTMENT]", error);
		throw new Error("Erro ao buscar consulta");
	}
}
