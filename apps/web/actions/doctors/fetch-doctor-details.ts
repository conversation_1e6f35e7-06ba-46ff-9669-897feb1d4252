"use server";

import { db } from "database";
import { DoctorStatusType } from "database";
import { Doctor } from "../../types/doctor";

/**
 * Serializa objetos Decimal do Prisma para números JavaScript
 * Evita erro: "Only plain objects can be passed to Client Components from Server Components"
 */
function serializeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(serializeObject) as unknown as T;
  }

  const result: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj)) {
    // Checa se é um Decimal (tem método toString mas não é um tipo nativo JavaScript)
    if (value !== null &&
        typeof value === 'object' &&
        'toString' in value &&
        !Array.isArray(value) &&
        Object.prototype.toString.call(value) !== '[object Date]' &&
        !(value instanceof Map) &&
        !(value instanceof Set)) {
      // Provavelmente um Decimal, serializa para número
      result[key] = Number(value);
    } else if (value !== null && typeof value === 'object') {
      // Processa objetos aninhados recursivamente
      result[key] = serializeObject(value);
    } else {
      // Mantém outros valores como estão
      result[key] = value;
    }
  }

  return result as T;
}

interface FetchDoctorDetailsResponse {
  status: "success" | "error";
  data?: Doctor;
  message?: string;
}

export async function fetchDoctorDetails(doctorId: string): Promise<FetchDoctorDetailsResponse> {
  try {
    // Busca o médico pelo ID
    const doctor = await db.doctor.findUnique({
      where: {
        id: doctorId,
        documentStatus: "APPROVED" as DoctorStatusType,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            avatarUrl: true,
          },
        },
        specialties: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        doctorSchedules: true,
        scheduleBlocks: {
          where: {
            endTime: {
              gte: new Date(),
            },
          },
          select: {
            id: true,
            startTime: true,
            endTime: true,
            description: true,
          },
        },
        timeSlots: {
          where: {
            startTime: {
              gte: new Date(),
            },
            isAvailable: true,
          },
          take: 10,
          orderBy: {
            startTime: "asc",
          },
        },
      },
    });

    if (!doctor) {
      return {
        status: "error",
        message: "Médico não encontrado",
      };
    }

    // Serializa os dados para evitar problemas com Decimal
    const serializedDoctor = serializeObject(doctor) as any;

    // Mapeia os dados para o formato esperado pelo componente
    // Usando type assertion para evitar erros de tipo
    const formattedDoctor = {
      id: serializedDoctor.id,
      createdAt: serializedDoctor.createdAt,
      updatedAt: serializedDoctor.updatedAt,
      user: {
        name: serializedDoctor.user?.name || "Médico sem nome",
        email: serializedDoctor.user?.email,
        avatarUrl: serializedDoctor.user?.avatarUrl,
      },
      crm: serializedDoctor.crm,
      crmState: serializedDoctor.crmState,
      biography: serializedDoctor.biography || "",
      consultationPrice: Number(serializedDoctor.consultationPrice),
      consultationDuration: serializedDoctor.consultationDuration,
      returnPeriod: serializedDoctor.returnPeriod,
      rating: serializedDoctor.rating || 0,
      totalRatings: serializedDoctor.totalRatings || 0,
      onlineStatus: serializedDoctor.onlineStatus as "ONLINE" | "OFFLINE" | "BUSY",
      specialties: serializedDoctor.specialties?.map((specialty: any) => ({
        id: specialty.id,
        name: specialty.name,
        description: specialty.description,
      })) || [],
      doctorSchedules: serializedDoctor.doctorSchedules?.map((schedule: any) => ({
        id: schedule.id,
        week_day: schedule.weekDay,
        start_time: schedule.startTime,
        end_time: schedule.endTime,
        is_enabled: schedule.isEnabled,
      })) || [],
      scheduleBlocks: serializedDoctor.scheduleBlocks?.map((block: any) => ({
        id: block.id,
        start_time: block.startTime.toISOString(),
        end_time: block.endTime.toISOString(),
        description: block.description,
      })) || [],
      timeSlots: serializedDoctor.timeSlots?.map((slot: any) => ({
        id: slot.id,
        startTime: slot.startTime.toISOString(),
        endTime: slot.endTime.toISOString(),
        isAvailable: slot.isAvailable,
      })) || [],
      // Add missing properties required by the Doctor interface
      documentStatus: serializedDoctor.documentStatus || "APPROVED",
      consultTypes: serializedDoctor.consultTypes || ["VIDEO"],
      userId: serializedDoctor.userId,
      evaluations: [],
    } as Doctor;

    return {
      status: "success",
      data: formattedDoctor,
    };
  } catch (error) {
    console.error("Error fetching doctor details:", error);
    return {
      status: "error",
      message: "Falha ao buscar detalhes do médico. Por favor, tente novamente.",
    };
  }
}
