"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

interface DoctorSchedule {
  weekDay: number;
  startTime: string;
  endTime: string;
  isEnabled: boolean;
}

interface UpdateScheduleParams {
  doctorId: string;
  schedule: DoctorSchedule[];
}

export async function getSchedule(params: { doctorId: string }) {
  try {
    const session = await currentUser();
    if (!session?.user) throw new Error("Não autorizado");

    const schedules = await db.doctorSchedule.findMany({
      where: {
        doctorId: params.doctorId,
      },
      orderBy: {
        weekDay: "asc",
      },
    });

    return schedules;
  } catch (error) {
    console.error("[GET_DOCTOR_SCHEDULE]", error);
    throw new Error("Erro ao buscar horários do médico");
  }
}

export async function updateSchedule({ doctorId, schedule }: UpdateScheduleParams) {
  try {
    const session = await currentUser();
    if (!session?.user) throw new Error("Não autorizado");

    // Delete existing schedules
    await db.doctorSchedule.deleteMany({
      where: {
        doctorId,
      },
    });

    // Create new schedules
    await db.doctorSchedule.createMany({
      data: schedule.map((item) => ({
        doctorId,
        weekDay: item.weekDay,
        startTime: item.startTime,
        endTime: item.endTime,
        isEnabled: item.isEnabled,
      })),
    });

    return { success: true };
  } catch (error) {
    console.error("[UPDATE_DOCTOR_SCHEDULE]", error);
    throw new Error("Erro ao atualizar horários do médico");
  }
}