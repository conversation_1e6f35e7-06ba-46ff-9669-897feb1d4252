"use server";

import { db } from "database";
import { z } from "zod";
import { <PERSON><PERSON>ilt<PERSON> } from "./types";




export async function getDoctors(filters?: DoctorFilt<PERSON>) {
  // Construir os filtros da query
  const where: any = {};

  // Filtro por termo de busca
  if (filters?.searchTerm) {
    where.OR = [
      {
        user: {
          name: {
            contains: filters.searchTerm,
            mode: "insensitive",
          },
        },
      },
      {
        user: {
          email: {
            contains: filters.searchTerm,
            mode: "insensitive",
          },
        },
      },
      {
        crm: {
          contains: filters.searchTerm,
        },
      },
      {
        specialties: {
          some: {
            name: {
              contains: filters.searchTerm,
              mode: "insensitive",
            },
          },
        },
      },
    ];
  }

  // Filtro por hospital
  if (filters?.hospitalId) {
    where.hospitals = {
      some: {
        hospitalId: filters.hospitalId,
        isActive: true,
      },
    };
  }

  // Filtro por status do documento
  if (filters?.documentStatus) {
    where.documentStatus = filters.documentStatus;
  }

  try {
    // Executar a query
    const doctors = await db.doctor.findMany({
      where,
      include: {
        user: {
          select: {
            name: true,
            email: true,
            avatarUrl: true, // Alterado de 'image' para 'avatarUrl' conforme esquema
            phone: true,
          },
        },
        specialties: {
          select: {
            name: true,
          },
          take: 1, // Pegar apenas a primeira especialidade para o cartão
        },
        _count: {
          select: {
            appointments: {
              where: {
                status: "COMPLETED", // Contar apenas consultas concluídas
              },
            },
          },
        },
      },
      orderBy: {
        user: {
          name: "asc",
        },
      },
    });

    // Formatar os dados para o DoctorCard
    const formattedDoctors = doctors.map((doctor) => ({
      id: doctor.id,
      user: {
        name: doctor.user?.name ?? "Médico sem nome", // Fallback para null
        email: doctor.user?.email ?? "", // Email é obrigatório, mas fallback para segurança
        image: doctor.user?.avatarUrl ?? null, // Usando avatarUrl conforme esquema
        phone: doctor.user?.phone ?? null,
      },
      crm: doctor.crm,
      crmState: doctor.crmState,
      specialty: doctor.specialties[0]?.name ?? null, // Primeira especialidade ou null
      consultationPrice: Number(doctor.consultationPrice), // Converter Decimal para número
      consultationDuration: doctor.consultationDuration,
      biography: doctor.biography ?? null,
      documentStatus: doctor.documentStatus,
      rating: doctor.rating ?? null,
      totalRatings: doctor.totalRatings,
      appointments: Array(doctor._count.appointments).fill(null), // Array com comprimento da contagem
    }));

    return formattedDoctors;
  } catch (error) {
    console.error("Erro ao buscar médicos:", error);
    throw new Error("Falha ao carregar a lista de médicos");
  }
}
