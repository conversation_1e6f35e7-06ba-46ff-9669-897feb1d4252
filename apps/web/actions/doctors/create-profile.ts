'use server';

import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";
import { revalidatePath } from "next/cache";

export async function createDoctorProfile() {
  try {
    const { user } = await currentUser();

    if (!user) {
      return { success: false, error: "Usuário não autenticado" };
    }

    if (user.role !== "DOCTOR") {
      return { success: false, error: "Apenas usuários médicos podem criar perfil médico" };
    }

    // Verificar se o perfil já existe
    const existingDoctor = await db.doctor.findUnique({
      where: { userId: user.id },
    });

    if (existingDoctor) {
      return {
        success: true,
        message: "Perfil médico já existe",
        doctorId: existingDoctor.id
      };
    }

    // Criar um perfil médico básico para testes
    const doctor = await db.doctor.create({
      data: {
        userId: user.id,
        crm: "123456", // CRM fictício para teste
        crmState: "SP",
        consultationPrice: 100.00,
        consultationDuration: 30,
        biography: "Médico de teste",
        documentStatus: "APPROVED",
        onlineStatus: "ONLINE",
      },
    });

    console.log(`[createDoctorProfile] Created doctor profile: ${doctor.id}`);

    // Revalidar a página de consultas
    revalidatePath('/app/appointments');

    return {
      success: true,
      message: "Perfil médico criado com sucesso",
      doctorId: doctor.id
    };
  } catch (error) {
    console.error("[createDoctorProfile] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao criar perfil médico"
    };
  }
}
