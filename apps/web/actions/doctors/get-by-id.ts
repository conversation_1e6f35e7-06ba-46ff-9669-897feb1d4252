import { db } from "database";



export async function getById(params: { id: string }) {
  try {
    const doctor = await db.doctor.findUnique({
      where: {
        id: params.id,
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            avatarUrl: true,
            phone: true,
          },
        },
        specialties: true,
        doctorSchedules: true,
        scheduleBlocks: true
      },
    });

    if (!doctor) {
      throw new Error("Médico não encontrado");
    }

    return {
      ...doctor,
      consultationPrice: doctor.consultationPrice ? Number(doctor.consultationPrice) : null,
      consultationDuration: doctor.consultationDuration ? Number(doctor.consultationDuration) : null
    };
  } catch (error) {
    console.error("[GET_DOCTOR_BY_ID]", error);
    throw new Error("Erro ao buscar médico");
  }
}
