"use server";

import { createApiCaller } from "api/trpc/caller";

interface FetchDoctorsParams {
  query?: string;
  specialtyId?: string;
  onlineOnly?: boolean;
  page?: number;
}

export async function fetchDoctors(params: FetchDoctorsParams) {
  try {
    console.log("[FETCH_DOCTORS] Iniciando com parâmetros:", params);

    const apiCaller = await createApiCaller();
    console.log("[FETCH_DOCTORS] API caller criado");

    // Usar o novo endpoint publicList
    console.log("[FETCH_DOCTORS] Chamando doctors.publicList");
    const result = await apiCaller.doctors.publicList({
      query: params.query,
      specialtyId: params.specialtyId,
      onlineOnly: params.onlineOnly,
      page: params.page || 1,
    });

    console.log("[FETCH_DOCTORS] Resultado obtido com sucesso");
    console.log("[FETCH_DOCTORS] Total de médicos:", result.pagination.total);

    return {
      status: "success",
      data: {
        doctors: result.doctors,
        specialties: result.specialties,
        total: result.pagination.total,
        onlineCount: result.onlineCount,
      },
    };
  } catch (error) {
    console.error("[FETCH_DOCTORS] Erro:", error);

    return {
      status: "error",
      message: error instanceof Error ? error.message : "Erro ao buscar médicos",
    };
  }
}
