"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { UserRole } from "@prisma/client";

export interface ActiveConsultation {
  id: string;
  doctor_name: string;
  patient_name: string;
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED';
  scheduled_at: string;
  created_at: string;
  updated_at: string;
  unread_count: number;
  last_message?: {
    content: string;
    type: string;
    created_at: string;
  };
  is_on_duty?: boolean;
  urgency_level?: string | null;
}

export async function getActiveConsultations(): Promise<{
  success: boolean;
  consultations?: ActiveConsultation[];
  error?: string;
}> {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    console.log(`[getActiveConsultations] Buscando consultas para usuário: ${session.user.id}, role: ${session.user.role}`);

    let consultations: ActiveConsultation[] = [];

    if (session.user.role === UserRole.DOCTOR) {
      // Buscar consultas do médico
      const doctor = await db.doctor.findUnique({
        where: { userId: session.user.id },
        select: { id: true }
      });

      if (!doctor) {
        throw new Error("Médico não encontrado");
      }

      const appointments = await db.appointment.findMany({
        where: {
          OR: [
            { doctorId: doctor.id },
            { acceptedByDoctorId: doctor.id } // Para plantões aceitos
          ],
          chatEnabled: true,
          status: {
            in: ["SCHEDULED", "IN_PROGRESS", "COMPLETED"]
          }
        },
        orderBy: [
          { isOnDuty: "desc" }, // Plantões primeiro
          { urgencyLevel: "desc" }, // Urgência alta primeiro
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          isOnDuty: true,
          urgencyLevel: true,
          patient: {
            select: {
              user: {
                select: {
                  name: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          },
          _count: {
            select: {
              messages: {
                where: {
                  createdAt: {
                    gt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
                  }
                }
              }
            }
          }
        }
      });

      consultations = appointments.map(appointment => ({
        id: appointment.id,
        doctor_name: session.user.name || "Médico",
        patient_name: appointment.patient.user.name || "Paciente",
        status: appointment.status as 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED',
        scheduled_at: appointment.scheduledAt?.toISOString() || new Date().toISOString(),
        created_at: appointment.createdAt.toISOString(),
        updated_at: appointment.updatedAt.toISOString(),
        unread_count: appointment._count.messages,
        is_on_duty: appointment.isOnDuty || false,
        urgency_level: appointment.urgencyLevel,
        last_message: appointment.messages[0] ? {
          content: appointment.messages[0].content,
          type: appointment.messages[0].type,
          created_at: appointment.messages[0].createdAt.toISOString()
        } : undefined
      }));

    } else if (session.user.role === UserRole.PATIENT) {
      // Buscar consultas do paciente
      const patient = await db.patient.findUnique({
        where: { userId: session.user.id },
        select: { id: true }
      });

      if (!patient) {
        throw new Error("Paciente não encontrado");
      }

      const appointments = await db.appointment.findMany({
        where: {
          patientId: patient.id,
          chatEnabled: true,
          status: {
            in: ["SCHEDULED", "IN_PROGRESS", "COMPLETED"]
          }
        },
        orderBy: [
          { isOnDuty: "desc" },
          { urgencyLevel: "desc" },
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          isOnDuty: true,
          urgencyLevel: true,
          doctor: {
            select: {
              user: {
                select: {
                  name: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          },
          _count: {
            select: {
              messages: {
                where: {
                  createdAt: {
                    gt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
                  }
                }
              }
            }
          }
        }
      });

      consultations = appointments.map(appointment => ({
        id: appointment.id,
        doctor_name: appointment.doctor.user.name || "Médico",
        patient_name: session.user.name || "Paciente",
        status: appointment.status as 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELED',
        scheduled_at: appointment.scheduledAt?.toISOString() || new Date().toISOString(),
        created_at: appointment.createdAt.toISOString(),
        updated_at: appointment.updatedAt.toISOString(),
        unread_count: appointment._count.messages,
        is_on_duty: appointment.isOnDuty || false,
        urgency_level: appointment.urgencyLevel,
        last_message: appointment.messages[0] ? {
          content: appointment.messages[0].content,
          type: appointment.messages[0].type,
          created_at: appointment.messages[0].createdAt.toISOString()
        } : undefined
      }));
    }

    console.log(`[getActiveConsultations] ${consultations.length} consultas encontradas`);
    return { success: true, consultations };

  } catch (error) {
    console.error('[getActiveConsultations] Erro:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro ao buscar consultas'
    };
  }
}
