"use server";

import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { v4 as uuidv4 } from "uuid";
import { UserRole } from "@prisma/client";
import { createAdminClient } from "@shared/lib/supabase/admin";
import {  getSignedUploadUrl, getSignedUrl  } from "../../../../packages/storage";

const ATTACHMENTS_BUCKET = process.env.NEXT_PUBLIC_CHAT_ATTACHMENTS_BUCKET || "chat_attachments";

// Get all chats for current user (doctor or patient)
export async function getUserChats() {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Se for médico, buscar todas as consultas (appointments) do médico agrupadas por paciente
    if (session.user.role === UserRole.DOCTOR) {
      const doctorId = await db.doctor.findUnique({
        where: { userId: session.user.id },
        select: { id: true }
      });

      if (!doctorId) {
        throw new Error("Médico não encontrado");
      }

      // Buscar appointments com mensagens, agrupados por paciente
      const appointments = await db.appointment.findMany({
        where: {
          doctorId: doctorId.id,
          chatEnabled: true,
          OR: [
            { status: "SCHEDULED" },
            { status: "IN_PROGRESS" },
            { status: "COMPLETED" },
          ]
        },
        orderBy: [
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          updatedAt: true,
          // Campos específicos para plantão
          isOnDuty: true,
          urgencyLevel: true,
          acceptedAt: true,
          acceptedByDoctorId: true,
          patient: {
            select: {
              user: {
                select: {
                  id: true,
                  name: true,
                  avatarUrl: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          }
        }
      });

      return { success: true, chats: appointments };
    }
    // Se for paciente, buscar as consultas do paciente
    else if (session.user.role === UserRole.PATIENT) {
      const patientId = await db.patient.findUnique({
        where: { userId: session.user.id },
        select: { id: true }
      });

      if (!patientId) {
        throw new Error("Paciente não encontrado");
      }

      const appointments = await db.appointment.findMany({
        where: {
          patientId: patientId.id,
          chatEnabled: true,
          OR: [
            { status: "SCHEDULED" },
            { status: "IN_PROGRESS" },
            { status: "COMPLETED" },
          ]
        },
        orderBy: [
          { updatedAt: "desc" }
        ],
        select: {
          id: true,
          scheduledAt: true,
          status: true,
          updatedAt: true,
          // Campos específicos para plantão
          isOnDuty: true,
          urgencyLevel: true,
          acceptedAt: true,
          acceptedByDoctorId: true,
          doctor: {
            select: {
              user: {
                select: {
                  id: true,
                  name: true,
                  avatarUrl: true
                }
              }
            }
          },
          messages: {
            orderBy: { createdAt: "desc" },
            take: 1
          }
        }
      });

      return { success: true, chats: appointments };
    }

    return { success: false, error: "Função não suportada para este tipo de usuário" };
  } catch (error) {
    console.error("Error fetching chats:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao buscar chats"
    };
  }
}

// Get messages for a specific appointment
export async function getAppointmentMessages(appointmentId: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário tem acesso a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        doctorId: true,
        patientId: true,
        doctor: {
          select: {
            userId: true
          }
        },
        patient: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    // Verificar se o usuário é o médico ou o paciente da consulta
    const isDoctor = appointment.doctor.userId === session.user.id;
    const isPatient = appointment.patient.userId === session.user.id;

    if (!isDoctor && !isPatient) {
      throw new Error("Você não tem permissão para acessar esta conversa");
    }

    // Buscar as mensagens
    const messages = await db.message.findMany({
      where: {
        appointmentId
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
            role: true
          }
        }
      },
      orderBy: {
        createdAt: "asc"
      }
    });

    return { success: true, messages };
  } catch (error) {
    console.error("Error fetching messages:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao buscar mensagens"
    };
  }
}

// Send a text message
export async function sendTextMessage(appointmentId: string, content: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário tem acesso a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        id: true,
        doctorId: true,
        patientId: true,
        doctor: {
          select: {
            userId: true
          }
        },
        patient: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    // Verificar se o usuário é o médico ou o paciente da consulta
    const isDoctor = appointment.doctor.userId === session.user.id;
    const isPatient = appointment.patient.userId === session.user.id;

    if (!isDoctor && !isPatient) {
      throw new Error("Você não tem permissão para enviar mensagens nesta conversa");
    }

    // Criar a mensagem
    const message = await db.message.create({
      data: {
        appointmentId,
        senderId: session.user.id,
        type: "TEXT",
        content,
        senderRole: session.user.role
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
            role: true
          }
        }
      }
    });

    // Atualizar o timestamp do appointment
    await db.appointment.update({
      where: { id: appointmentId },
      data: { updatedAt: new Date() }
    });

    return { success: true, message };
  } catch (error) {
    console.error("Error sending message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar mensagem"
    };
  }
}

// Get signed URL for file upload
export async function getAttachmentUploadUrl(appointmentId: string, fileName: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário tem acesso a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        doctorId: true,
        patientId: true,
        doctor: {
          select: {
            userId: true
          }
        },
        patient: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    // Verificar se o usuário é o médico ou o paciente da consulta
    const isDoctor = appointment.doctor.userId === session.user.id;
    const isPatient = appointment.patient.userId === session.user.id;

    if (!isDoctor && !isPatient) {
      throw new Error("Você não tem permissão para enviar arquivos nesta conversa");
    }

    // Gerar um nome de arquivo único
    const uniqueFileName = `${appointmentId}/${Date.now()}_${fileName}`;

    // Obter URL assinada para upload
    const uploadUrl = await getSignedUploadUrl(uniqueFileName, {
      bucket: ATTACHMENTS_BUCKET,
    });

    return {
      success: true,
      uploadUrl,
      filePath: uniqueFileName
    };
  } catch (error) {
    console.error("Error getting upload URL:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao gerar URL para upload"
    };
  }
}

// Send a file message after upload
export async function sendFileMessage(
  appointmentId: string,
  filePath: string,
  fileName: string,
  fileType: string,
  fileSize: number
) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário tem acesso a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        doctorId: true,
        patientId: true,
        doctor: {
          select: {
            userId: true
          }
        },
        patient: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    // Verificar se o usuário é o médico ou o paciente da consulta
    const isDoctor = appointment.doctor.userId === session.user.id;
    const isPatient = appointment.patient.userId === session.user.id;

    if (!isDoctor && !isPatient) {
      throw new Error("Você não tem permissão para enviar arquivos nesta conversa");
    }

    // Obter URL assinada para visualização
    const fileUrl = await getSignedUrl(filePath, {
      bucket: ATTACHMENTS_BUCKET,
      expiresIn: 3600 * 24 // 24 horas
    });

    // Criar a mensagem com o arquivo
    const message = await db.message.create({
      data: {
        appointmentId,
        senderId: session.user.id,
        type: "FILE",
        content: fileUrl,
        senderRole: session.user.role,
        metadata: {
          fileName,
          contentType: fileType,
          size: fileSize,
          path: filePath,
        }
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
            role: true
          }
        }
      }
    });

    // Atualizar o timestamp do appointment
    await db.appointment.update({
      where: { id: appointmentId },
      data: { updatedAt: new Date() }
    });

    return { success: true, message };
  } catch (error) {
    console.error("Error sending file message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar arquivo"
    };
  }
}

// Enviar mensagem de áudio
export async function sendAudioMessage(appointmentId: string, audioBlob: Blob) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário tem acesso a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        doctorId: true,
        patientId: true,
        doctor: {
          select: {
            userId: true
          }
        },
        patient: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    // Verificar se o usuário é o médico ou o paciente da consulta
    const isDoctor = appointment.doctor.userId === session.user.id;
    const isPatient = appointment.patient.userId === session.user.id;

    if (!isDoctor && !isPatient) {
      throw new Error("Você não tem permissão para enviar mensagens nesta conversa");
    }

    // Criar um cliente Supabase com privilégios de admin
    const adminSupabase = createAdminClient();

    // Fazer upload do áudio para o Supabase Storage
    const fileName = `${appointmentId}/audio_${Date.now()}.webm`;
    const { data: uploadData, error: uploadError } = await adminSupabase.storage
      .from(ATTACHMENTS_BUCKET)
      .upload(fileName, audioBlob, {
        contentType: 'audio/webm',
        upsert: true,
      });

    if (uploadError) throw uploadError;

    // Obter a URL pública do áudio
    const { data: { publicUrl } } = adminSupabase.storage
      .from(ATTACHMENTS_BUCKET)
      .getPublicUrl(fileName);

    // Criar a mensagem de áudio
    const message = await db.message.create({
      data: {
        appointmentId,
        senderId: session.user.id,
        type: "AUDIO",
        content: publicUrl,
        senderRole: session.user.role,
        metadata: {
          fileName,
          contentType: 'audio/webm',
          path: fileName,
        }
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            avatarUrl: true,
            role: true
          }
        }
      }
    });

    // Atualizar o timestamp do appointment
    await db.appointment.update({
      where: { id: appointmentId },
      data: { updatedAt: new Date() }
    });

    return { success: true, message };
  } catch (error) {
    console.error("Error sending audio message:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao enviar mensagem de áudio"
    };
  }
}

// Iniciar uma chamada de vídeo (somente médicos)
export async function initiateVideoCall(appointmentId: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário é médico
    if (session.user.role !== UserRole.DOCTOR) {
      throw new Error("Apenas médicos podem iniciar chamadas de vídeo");
    }

    // Verificar se o médico pertence a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        id: true,
        doctor: {
          select: {
            userId: true
          }
        },
        roomId: true
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    if (appointment.doctor.userId !== session.user.id) {
      throw new Error("Você não tem permissão para iniciar uma chamada nesta consulta");
    }

    // Se não tiver um roomId, gerar um
    let roomId = appointment.roomId;
    if (!roomId) {
      roomId = `room_${uuidv4()}`;
      await db.appointment.update({
        where: { id: appointmentId },
        data: { roomId }
      });
    }

    // Criar uma mensagem de sistema informando sobre a chamada
    await db.message.create({
      data: {
        appointmentId,
        senderId: session.user.id,
        type: "SYSTEM",
        content: "Iniciou uma chamada de vídeo",
        metadata: {
          action: "VIDEO_CALL_STARTED",
          roomId
        }
      }
    });

    // Atualizar o timestamp do appointment
    await db.appointment.update({
      where: { id: appointmentId },
      data: { updatedAt: new Date() }
    });

    return {
      success: true,
      roomId,
      redirectUrl: `/app/chats/${roomId}`
    };
  } catch (error) {
    console.error("Error initiating video call:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao iniciar chamada de vídeo"
    };
  }
}

// Iniciar uma chamada de áudio (somente médicos)
export async function initiateAudioCall(appointmentId: string) {
  try {
    const session = await currentUser();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // Verificar se o usuário é médico
    if (session.user.role !== UserRole.DOCTOR) {
      throw new Error("Apenas médicos podem iniciar chamadas de áudio");
    }

    // Verificar se o médico pertence a este appointment
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      select: {
        id: true,
        doctor: {
          select: {
            userId: true
          }
        },
        roomId: true
      }
    });

    if (!appointment) {
      throw new Error("Consulta não encontrada");
    }

    if (appointment.doctor.userId !== session.user.id) {
      throw new Error("Você não tem permissão para iniciar uma chamada nesta consulta");
    }

    // Se não tiver um roomId, gerar um
    let roomId = appointment.roomId;
    if (!roomId) {
      roomId = `room_${uuidv4()}`;
      await db.appointment.update({
        where: { id: appointmentId },
        data: { roomId }
      });
    }

    // Criar uma mensagem de sistema informando sobre a chamada
    await db.message.create({
      data: {
        appointmentId,
        senderId: session.user.id,
        type: "SYSTEM",
        content: "Iniciou uma chamada de áudio",
        metadata: {
          action: "AUDIO_CALL_STARTED",
          roomId
        }
      }
    });

    // Atualizar o timestamp do appointment
    await db.appointment.update({
      where: { id: appointmentId },
      data: { updatedAt: new Date() }
    });

    return {
      success: true,
      roomId,
      redirectUrl: `/app/chats/${roomId}?audioOnly=true`
    };
  } catch (error) {
    console.error("Error initiating audio call:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Erro ao iniciar chamada de áudio"
    };
  }
}
