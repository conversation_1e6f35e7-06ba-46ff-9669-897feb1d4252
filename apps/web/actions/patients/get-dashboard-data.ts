// apps/web/actions/patients/get-dashboard-data.ts
"use server";

import { db } from "database";
import { currentUser } from "@saas/auth/lib/current-user";

export interface PatientDashboardData {
  patient: {
    id: string;
    weight: number | null;
    height: number | null;
    bmi: number | null;
  } | null;
  subscription: {
    hasActive: boolean;
    planName: string | null;
    nextBillingDate: Date | null;
    consultationsIncluded: number | null;
    consultationsUsed: number | null;
    remainingConsultations: number | null;
  };
  upcomingAppointments: Array<{
    id: string;
    scheduledAt: Date | null;
    duration: number | null;
    consultType: string | null;
    status: string;
    isOnDuty: boolean;
    urgencyLevel: string | null;
    paymentStatus: string | null;
    createdAt: Date;
    acceptedAt: Date | null;
    doctor: {
      name: string | null;
      avatarUrl: string | null;
      specialties: Array<{ name: string }>;
    } | null;
  }>;
  totals: {
    totalConsultations: number;
    activePrescriptions: number;
    lastConsultationAt: Date | null;
  };
}

export async function getPatientDashboardData(): Promise<PatientDashboardData> {
  const session = await currentUser();
  const user = session?.user;

  if (!user) {
    return {
      patient: null,
      subscription: {
        hasActive: false,
        planName: null,
        nextBillingDate: null,
        consultationsIncluded: null,
        consultationsUsed: null,
        remainingConsultations: null,
      },
      upcomingAppointments: [],
      totals: {
        totalConsultations: 0,
        activePrescriptions: 0,
        lastConsultationAt: null,
      },
    };
  }

  // Fetch patient profile
  const patient = await db.patient.findFirst({
    where: { userId: user.id },
    select: {
      id: true,
      weight: true,
      height: true,
    },
  });

  let bmi: number | null = null;
  if (patient?.weight && patient?.height) {
    bmi = Number(((patient.weight / Math.pow(patient.height / 100, 2)).toFixed(1)));
  }

  // Subscription summary
  const activeSubscription = await db.patientSubscription.findFirst({
    where: {
      patient: { userId: user.id },
      status: "ACTIVE",
    },
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      planName: true,
      nextBillingDate: true,
      consultationsIncluded: true,
      consultationsUsed: true,
    },
  });

  const remainingConsultations = activeSubscription
    ? Math.max(
        0,
        (activeSubscription.consultationsIncluded ?? 0) - (activeSubscription.consultationsUsed ?? 0)
      )
    : null;

  // Upcoming and active appointments (including on-duty)
  const upcomingAppointments = patient
    ? await db.appointment.findMany({
        where: {
          patientId: patient.id,
          OR: [
            {
              status: "SCHEDULED",
              scheduledAt: { gte: new Date() },
            },
            {
              status: { in: ["IN_PROGRESS"] },
            },
            {
              isOnDuty: true,
              status: { in: ["SCHEDULED", "IN_PROGRESS"] },
            },
          ],
        },
        select: {
          id: true,
          scheduledAt: true,
          duration: true,
          consultType: true,
          status: true,
          isOnDuty: true,
          urgencyLevel: true,
          paymentStatus: true,
          createdAt: true,
          acceptedAt: true,
          doctor: {
            select: {
              user: { select: { name: true, avatarUrl: true } },
              specialties: { select: { name: true } },
            },
          },
        },
        orderBy: [
          { isOnDuty: "desc" },
          { status: "asc" },
          { scheduledAt: "asc" },
        ],
        take: 5,
      })
    : [];

  // Totals and last consultation
  const [totalConsultations, activePrescriptions, lastConsultation] = patient
    ? await Promise.all([
        db.appointment.count({ where: { patientId: patient.id, status: "COMPLETED" } }),
        db.prescription.count({
          where: { appointment: { patientId: patient.id }, status: "active" },
        }),
        db.appointment.findFirst({
          where: { patientId: patient.id, status: "COMPLETED" },
          orderBy: { scheduledAt: "desc" },
          select: { scheduledAt: true },
        }),
      ])
    : [0, 0, null as { scheduledAt: Date } | null];

  return {
    patient: patient
      ? {
          id: patient.id,
          weight: patient.weight,
          height: patient.height,
          bmi,
        }
      : null,
    subscription: {
      hasActive: Boolean(activeSubscription),
      planName: activeSubscription?.planName ?? null,
      nextBillingDate: activeSubscription?.nextBillingDate ?? null,
      consultationsIncluded: activeSubscription?.consultationsIncluded ?? null,
      consultationsUsed: activeSubscription?.consultationsUsed ?? null,
      remainingConsultations,
    },
    upcomingAppointments: upcomingAppointments.map((a) => ({
      id: a.id,
      scheduledAt: a.scheduledAt,
      duration: a.duration,
      consultType: a.consultType as any,
      status: a.status as any,
      isOnDuty: a.isOnDuty,
      urgencyLevel: a.urgencyLevel as any,
      paymentStatus: a.paymentStatus as any,
      createdAt: a.createdAt,
      acceptedAt: a.acceptedAt,
      doctor: a.doctor
        ? {
            name: a.doctor.user?.name ?? null,
            avatarUrl: a.doctor.user?.avatarUrl ?? null,
            specialties: a.doctor.specialties?.map((s) => ({ name: s.name })) ?? [],
          }
        : null,
    })),
    totals: {
      totalConsultations,
      activePrescriptions,
      lastConsultationAt: lastConsultation?.scheduledAt ?? null,
    },
  };
}


