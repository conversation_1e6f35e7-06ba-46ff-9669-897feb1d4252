"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";

import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

const patientProfileSchema = z.object({
  // Dad<PERSON> Pessoais
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido"),
  cpf: z.string().length(11, "CPF inválido"),
  birthDate: z.string(),
  gender: z.enum(["M", "F", "O", "N"]),
  maritalStatus: z.string().optional(),
  occupation: z.string().optional(),

  // Endereço
  address: z.object({
    street: z.string().min(1, "Rua é obrigatória"),
    number: z.string().min(1, "Número é obrigatório"),
    complement: z.string().optional(),
    neighborhood: z.string().min(1, "Bairro é obrigatório"),
    city: z.string().min(1, "Cidade é obrigatória"),
    state: z.string().length(2, "Estado inválido"),
    zipCode: z.string().length(8, "CEP inválido")
  }),

  // Informações de Saúde
  bloodType: z.string().optional(),
  height: z.number().positive().optional(),
  weight: z.number().positive().optional(),
  allergies: z.string(),
  chronicConditions: z.string(),
  medications: z.string(),
  surgeries: z.string(),
  familyHistory: z.string(),

  // Contato de Emergência
  emergencyContact: z.object({
    name: z.string().min(1, "Nome do contato é obrigatório"),
    relationship: z.string().min(1, "Tipo de relação é obrigatório"),
    phone: z.string().min(10, "Telefone inválido"),
    alternativePhone: z.string().optional()
  })
});

export type PatientProfileFormData = z.infer<typeof patientProfileSchema>;

export async function updatePatientProfile(formData: PatientProfileFormData) {
  try {
    // Validar autenticação
    const session = await auth();
    if (!session?.user) {
      return { status: "error", message: "Não autorizado" };
    }

    // Validar dados
    const validatedData = patientProfileSchema.parse(formData);

    // Buscar paciente
    const patient = await prisma.patient.findFirst({
      where: { user_id: session.user.id },
      include: { user: true }
    });

    if (!patient) {
      return { status: "error", message: "Paciente não encontrado" };
    }

    // Atualizar usuário base
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        name: validatedData.name,
        phone: validatedData.phone
      }
    });

    // Atualizar paciente
    const updatedPatient = await prisma.patient.update({
      where: { id: patient.id },
      data: {
        birth_date: new Date(validatedData.birthDate),
        gender: validatedData.gender,
        occupation: validatedData.occupation,
        marital_status: validatedData.maritalStatus,
        address: validatedData.address,
        blood_type: validatedData.bloodType,
        height: validatedData.height,
        weight: validatedData.weight,
        allergies: validatedData.allergies.split(",").map(s => s.trim()).filter(Boolean),
        chronic_conditions: validatedData.chronicConditions.split(",").map(s => s.trim()).filter(Boolean),
        medications: validatedData.medications.split(",").map(s => s.trim()).filter(Boolean),
        surgeries: validatedData.surgeries.split(",").map(s => s.trim()).filter(Boolean),
        family_history: validatedData.familyHistory.split(",").map(s => s.trim()).filter(Boolean),
        emergency_contact: validatedData.emergencyContact
      }
    });

    // Revalidar página do perfil
    revalidatePath("/patient/profile");

    return {
      status: "success",
      data: updatedPatient
    };

  } catch (error) {
    console.error("[UPDATE_PATIENT_PROFILE_ERROR]", error);
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Erro ao atualizar perfil"
    };
  }
}
