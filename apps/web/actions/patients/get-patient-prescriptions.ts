// src/actions/appointments/get-patient-appointments.ts
"use server";

import { auth } from "@/lib/auth";
import { prisma } from "@/lib/db";

export async function getPrescriptions() {
  try {
    // 1. Validação de autenticação
    const session = await auth();
    if (!session?.user) {
      throw new Error("Não autorizado");
    }

    // 2. Buscar o perfil do paciente associado ao usuário
    const patient = await prisma.patient.findFirst({
      where: {
        user_id: session.user.id,
      },
    });

    if (!patient) {
      throw new Error("Perfil de paciente não encontrado");
    }

    // 3. Buscar prescrições usando o ID correto do paciente
    const prescriptions = await prisma.prescription.findMany({
      where: {
        appointment: {
          patient_id: patient.id,
        },
      },
      include: {
        appointment: {
          include: {
            doctor: {
              include: {
                user: true,
                specialties: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    return prescriptions;
  } catch (error) {
    console.error("Error fetching prescriptions:", error);
    throw new Error("Erro ao buscar prescrições");
  }
}
