"use server";

import { db } from "database";
import { revalidatePath } from "next/cache";
import { z } from "zod";


const patientSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(10, "Telefone inválido").optional(),
  cpf: z.string().length(11, "CPF inválido"),
  birthDate: z.string(),
  gender: z.enum(["M", "F", "O", "N"]),
  address: z.object({}).optional(), // Add address field as optional JSON
});

type PatientData = z.infer<typeof patientSchema>;

export async function createPatient(data: PatientData) {
  try {
    const validation = patientSchema.safeParse(data);
    if (!validation.success) {
      return {
        status: "error",
        message: validation.error.errors[0].message,
      };
    }

    const existingUser = await db.user.findFirst({
      where: {
        OR: [{ email: data.email }],
      },
    });

    if (existingUser) {
      return {
        status: "error",
        message: "Email já cadastrado",
      };
    }

    const existingPatient = await db.patient.findFirst({
      where: {
        cpf: data.cpf,
      },
    });

    if (existingPatient) {
      return {
        status: "error",
        message: "CPF já cadastrado",
      };
    }

    await db.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          id: crypto.randomUUID(),
          name: data.name,
          email: data.email,
          phone: data.phone || null,
          role: "PATIENT",
        },
      });

      // Create patient
      await tx.patient.create({
        data: {
          id: crypto.randomUUID(),
          userId: user.id,
          cpf: data.cpf,
          birthDate: new Date(data.birthDate),
          gender: data.gender,
          address: {}, // Add empty JSON object for address
        },
      });
    });

    revalidatePath("/admin/patients");
    return { status: "success" };
  } catch (error) {
    console.error("Create patient error:", error);
    return {
      status: "error",
      message: "Erro ao criar paciente",
    };
  }
}

export async function updatePatient(id: string, data: PatientData) {
  try {
    const validation = patientSchema.safeParse(data);
    if (!validation.success) {
      return {
        status: "error",
        message: validation.error.errors[0].message,
      };
    }

    const patient = await db.patient.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!patient) {
      return {
        status: "error",
        message: "Paciente não encontrado",
      };
    }

    // Check if email is already in use by another user
    const existingUser = await db.user.findFirst({
      where: {
        AND: [
          { email: data.email },
          { id: { not: patient.userId } }
        ],
      },
    });

    if (existingUser) {
      return {
        status: "error",
        message: "Email já cadastrado",
      };
    }

    // Check if CPF is already in use by another patient
    const existingPatient = await db.patient.findFirst({
      where: {
        AND: [
          { cpf: data.cpf },
          { id: { not: id } }
        ],
      },
    });

    if (existingPatient) {
      return {
        status: "error",
        message: "CPF já cadastrado",
      };
    }

    await db.$transaction(async (tx) => {
      // Update user
      await tx.user.update({
        where: { id: patient.userId },
        data: {
          name: data.name,
          email: data.email,
          phone: data.phone || null,
        },
      });

      // Update patient
      await tx.patient.update({
        where: { id },
        data: {
          cpf: data.cpf,
          birthDate: new Date(data.birthDate),
          gender: data.gender,
          address: data.address || {}, // Add address field with fallback
        },
      });
    });

    revalidatePath("/admin/patients");
    return { status: "success" };
  } catch (error) {
    console.error("Update patient error:", error);
    return {
      status: "error",
      message: "Erro ao atualizar paciente",
    };
  }
}

export async function deletePatient(id: string) {
  try {
    const patient = await db.patient.findUnique({
      where: { id },
      include: { user: true },
    });

    if (!patient) {
      return {
        status: "error",
        message: "Paciente não encontrado",
      };
    }

    await db.$transaction(async (tx) => {
      // Delete patient first
      await tx.patient.delete({
        where: { id },
      });

      // Then delete user
      await tx.user.delete({
        where: { id: patient.userId },
      });
    });

    revalidatePath("/admin/patients");
    return { status: "success" };
  } catch (error) {
    return {
      status: "error",
      message: "Erro ao excluir paciente",
    };
  }
}
