// src/actions/appointments/get-recent-prescriptions.ts
"use server";

import { prisma } from "@/lib/db";

export async function getRecentPrescriptions({ userId }: { userId: string }) {
  try {
    // 2. Buscar o perfil do paciente associado ao usuário
    const patient = await prisma.patient.findFirst({
      where: {
        user_id: userId,
      },
    });

    if (!patient) {
      throw new Error("Perfil de paciente não encontrado");
    }

    // 3. Buscar prescrições usando o ID correto do paciente
    const prescriptions = await prisma.prescription.findMany({
      where: {
        appointment: {
          patient_id: patient.id,
        },
      },
      include: {
        appointment: {
          include: {
            doctor: {
              include: {
                user: true,
                specialties: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 5,
    });

    return prescriptions;
  } catch (error) {
    console.error("Error fetching prescriptions:", error);
    throw new Error("Erro ao buscar prescrições");
  }
}
