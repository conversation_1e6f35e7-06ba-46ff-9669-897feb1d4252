import { db } from "database";

export type DashboardMetrics = {
	// User metrics
	users: {
		total: number;
		doctors: number;
		patients: number;
		hospitals: number;
	};

	// Doctor metrics
	doctors: {
		total: number;
		pending: number;
		approved: number;
		bySpecialty: Array<{ specialty: string; count: number }>;
	};

	// Patient metrics
	patients: { total: number; newThisMonth: number };

	// Appointment metrics
	appointments: {
		total: number;
		scheduled: number;
		completed: number;
		canceled: number;
		noShow: number;
		byType: Array<{ type: string; count: number }>;
		byDay: Array<{ date: string; count: number }>;
	};

	// Transaction metrics
	transactions: {
		total: number;
		revenue: number;
		platformFee: number;
		pendingAmount: number;
		byStatus: Array<{ status: string; count: number; amount: number }>;
		byPaymentMethod: Array<{ method: string; count: number; amount: number }>;
		recent: Array<{
			id: string;
			amount: number;
			status: string;
			patientName: string;
			doctorName: string;
		}>;
	};

	// Hospital metrics
	hospitals: {
		total: number;
		activeDepartments: number;
	};

	// Top performers
	topDoctors: Array<{ name: string; revenue: number; appointments: number }>;
	topSpecialties: Array<{ name: string; count: number; revenue: number }>;
};

// Helper types to improve type safety
type DoctorStatusCount = { documentStatus: string; _count: { id: number } };
type AppointmentCount = { status: string; _count: { id: number } };
type AppointmentType = { consultType: string; _count: { id: number } };
type AppointmentDay = { scheduledAt: Date | string; _count: { id: number } };
type TransactionCount = { status: string; _count: { id: number }; _sum: { amount: number | null } };
type TransactionByMethod = { paymentMethod: string; _count: { id: number }; _sum: { amount: number | null } };
type DoctorSpecialty = { name: string; _count: { doctors: number } };
type TopDoctor = {
	user: { name: string } | null;
	_count: { appointments: number };
	appointments: Array<{ transaction: { amount: number } | null }> | null;
};

export async function getDashboardMetrics(): Promise<DashboardMetrics> {
	try {
		// Calculate date ranges
		const today = new Date();
		const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
		const thirtyDaysAgo = new Date(today);
		thirtyDaysAgo.setDate(today.getDate() - 30);

		// Create query promises
		const [
			totalUsers,
			doctorUsers,
			patientUsers,
			hospitalUsers,
			totalDoctors,
			doctorStatusCounts,
			totalPatients,
			newPatientsThisMonth,
			totalHospitals,
			activeDepartments,
			appointmentCounts,
			appointmentsByType,
			appointmentsByDay,
			transactionCounts,
			transactionsByMethod,
			recentTransactions,
			doctorsBySpecialty,
			// Commenting out topDoctorsRaw since it appears unused or not properly initialized
			// topDoctorsRaw
		] = await Promise.all([
			// User counts
			db.user.count(),
			db.user.count({ where: { role: "DOCTOR" } }),
			db.user.count({ where: { role: "PATIENT" } }),
			db.user.count({ where: { role: "HOSPITAL" } }),

			// Doctor metrics
			db.doctor.count(),
			db.doctor.groupBy({
				by: ['documentStatus'],
				_count: { id: true }
			}),

			// Patient metrics
			db.patient.count(),
			db.patient.count({
				where: {
					createdAt: { gte: firstDayOfMonth }
				}
			}),

			// Hospital metrics
			db.hospital.count(),
			db.department.count({ where: { isActive: true } }),

			// Appointment metrics
			db.appointment.groupBy({
				by: ['status'],
				_count: { id: true }
			}),
			db.appointment.groupBy({
				by: ['consultType'],
				_count: { id: true }
			}),
			db.appointment.groupBy({
				by: ['scheduledAt'],
				_count: { id: true },
				where: {
					scheduledAt: { gte: thirtyDaysAgo }
				}
			}),

			// Transaction metrics
			db.transaction.groupBy({
				by: ['status'],
				_count: { id: true },
				_sum: { amount: true }
			}),
			db.transaction.groupBy({
				by: ['paymentMethod'],
				_count: { id: true },
				_sum: { amount: true }
			}),
			// Simplified transaction query
			db.transaction.findMany({
				take: 5,
				orderBy: { createdAt: 'desc' },
				select: {
					id: true,
					amount: true,
					status: true
				}
			}),

			// Specialty metrics
			db.specialty.findMany({
				include: {
					_count: {
						select: { doctors: true }
					}
				}
			}),

			// Commented out due to missing initialization in the original code
			// Missing topDoctorsRaw query
		]);

		// Process doctor status counts
		const pendingDoctors = doctorStatusCounts.find(item => item?.documentStatus === "PENDING")?._count?.id || 0;
		const approvedDoctors = doctorStatusCounts.find(item => item?.documentStatus === "APPROVED")?._count?.id || 0;

		// Process appointment counts
		const scheduledAppointments = appointmentCounts.find(item => item?.status === "SCHEDULED")?._count?.id || 0;
		const completedAppointments = appointmentCounts.find(item => item?.status === "COMPLETED")?._count?.id || 0;
		const canceledAppointments = appointmentCounts.find(item => item?.status === "CANCELED")?._count?.id || 0;
		const noShowAppointments = appointmentCounts.find(item => item?.status === "NO_SHOW")?._count?.id || 0;

		// Process appointment types
		const appointmentTypeData = appointmentsByType.map(item => ({
			type: item?.consultType as string || "Unknown",
			count: item?._count?.id || 0
		}));

		// Process appointments by day
		const appointmentByDayData = appointmentsByDay.map(item => {
			if (!item || !item.scheduledAt) {
				return { date: "Unknown", count: 0 };
			}
			return {
				date: item.scheduledAt instanceof Date
					? item.scheduledAt.toISOString().split('T')[0]
					: new Date(item.scheduledAt as unknown as string).toISOString().split('T')[0],
				count: item._count?.id || 0
			};
		});

		// Process transaction status with Portuguese translations
		const transactionStatusData = transactionCounts.map(item => {
			// Traduzir status para português
			let statusPt = "Desconhecido";
			switch(item?.status) {
				case "PENDING": statusPt = "Pendente"; break;
				case "PAID": statusPt = "Pago"; break;
				case "REFUNDED": statusPt = "Reembolsado"; break;
				case "FAILED": statusPt = "Falhou"; break;
				default: statusPt = item?.status || "Desconhecido";
			}
			
			return {
				status: statusPt,
				count: item?._count?.id || 0,
				amount: Number(item?._sum?.amount || 0)
			};
		});

		// Calculate pending amount
		const pendingAmount = transactionStatusData.find(item => item.status === "Pendente")?.amount || 0;

		// Process payment methods with Portuguese translations
		const paymentMethodData = transactionsByMethod.map(item => {
			// Traduzir métodos de pagamento para português
			let methodPt = "Desconhecido";
			switch(item?.paymentMethod) {
				case "CREDIT_CARD": methodPt = "Cartão de Crédito"; break;
				case "BOLETO": methodPt = "Boleto"; break;
				case "PIX": methodPt = "PIX"; break;
				default: methodPt = item?.paymentMethod || "Desconhecido";
			}
			
			return {
				method: methodPt,
				count: item?._count?.id || 0,
				amount: Number(item?._sum?.amount || 0)
			};
		});

		// Process recent transactions with better defaults
		const processedRecentTransactions = recentTransactions
			.filter(tx => tx !== null)
			.map((tx) => {
				// Traduzir status para português
				let statusPt = "Desconhecido";
				switch(tx.status) {
					case "PENDING": statusPt = "Pendente"; break;
					case "PAID": statusPt = "Pago"; break;
					case "REFUNDED": statusPt = "Reembolsado"; break;
					case "FAILED": statusPt = "Falhou"; break;
					default: statusPt = tx.status || "Desconhecido";
				}
				
				return {
					id: tx.id || "unknown",
					amount: Number(tx.amount || 0),
					status: statusPt,
					patientName: "Paciente", // Melhor texto padrão
					doctorName: "Médico", // Melhor texto padrão
				};
			})
			.slice(0, 5);

		// Process specialties
		const specialtyDistribution = doctorsBySpecialty.map(specialty => ({
			specialty: specialty?.name || "Unknown",
			count: specialty?._count?.doctors || 0
		}));

		// Since topDoctorsRaw is missing in original code, use empty array
		const topDoctors: Array<{ name: string; revenue: number; appointments: number }> = [];

		// Calculate revenue
		const totalRevenue = transactionStatusData.reduce(
			(sum, status) => status.status === "PAID" ? sum + status.amount : sum,
			0
		);

		// Calculate platform fee (assuming 10% of paid revenue)
		const platformFee = totalRevenue * 0.1;

		// Prepare final metrics object
		return {
			users: {
				total: totalUsers || 0,
				doctors: doctorUsers || 0,
				patients: patientUsers || 0,
				hospitals: hospitalUsers || 0
			},
			doctors: {
				total: totalDoctors || 0,
				pending: pendingDoctors || 0,
				approved: approvedDoctors || 0,
				bySpecialty: specialtyDistribution || []
			},
			patients: {
				total: totalPatients || 0,
				newThisMonth: newPatientsThisMonth || 0
			},
			hospitals: {
				total: totalHospitals || 0,
				activeDepartments: activeDepartments || 0
			},
			appointments: {
				total: (scheduledAppointments || 0) + (completedAppointments || 0) + (canceledAppointments || 0) + (noShowAppointments || 0),
				scheduled: scheduledAppointments || 0,
				completed: completedAppointments || 0,
				canceled: canceledAppointments || 0,
				noShow: noShowAppointments || 0,
				byType: appointmentTypeData || [],
				byDay: appointmentByDayData || []
			},
			transactions: {
				total: transactionCounts.reduce((sum, item) => sum + (item._count?.id || 0), 0),
				revenue: totalRevenue || 0,
				platformFee: platformFee || 0,
				pendingAmount: pendingAmount || 0,
				byStatus: transactionStatusData || [],
				byPaymentMethod: paymentMethodData || [],
				recent: processedRecentTransactions || []
			},
			topDoctors: topDoctors || [],
			topSpecialties: specialtyDistribution.map(spec => ({
				name: spec.specialty || "Unknown",
				count: spec.count || 0,
				revenue: 0 // Default value for revenue
			})).sort((a, b) => b.count - a.count).slice(0, 5)
		};
	} catch (error) {
		console.error("Error fetching dashboard metrics:", error);

		// Return a default empty structure to prevent UI errors
		return {
			users: { total: 0, doctors: 0, patients: 0, hospitals: 0 },
			doctors: { total: 0, pending: 0, approved: 0, bySpecialty: [] },
			patients: { total: 0, newThisMonth: 0 },
			hospitals: { total: 0, activeDepartments: 0 },
			appointments: {
				total: 0, scheduled: 0, completed: 0, canceled: 0, noShow: 0,
				byType: [], byDay: []
			},
			transactions: {
				total: 0, revenue: 0, platformFee: 0, pendingAmount: 0,
				byStatus: [], byPaymentMethod: [], recent: []
			},
			topDoctors: [],
			topSpecialties: []
		};
	}
}
