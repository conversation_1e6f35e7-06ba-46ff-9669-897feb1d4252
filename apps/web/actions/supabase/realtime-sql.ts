"use server";

import { createAdminClient } from "@shared/lib/supabase/admin";

/**
 * Verifica se a tabela 'messages' está configurada para real-time no Supabase
 */
export async function checkRealtimeConfiguration() {
  try {
    const adminSupabase = createAdminClient();

    // Verificar se a publicação real-time existe e inclui a tabela messages
    const { data, error } = await adminSupabase.rpc('pg_get_publication_tables', {
      publication_name: 'supabase_realtime'
    });

    if (error) {
      console.error("Error checking realtime publication:", error);
      return {
        success: false,
        error: error.message,
        hasMessagesTable: false
      };
    }

    // Verificar se a tabela 'messages' está incluída
    const messagesIncluded = Array.isArray(data) &&
      data.some(item =>
        typeof item === 'object' &&
        item !== null &&
        'schemaname' in item &&
        'tablename' in item &&
        item.schemaname === 'public' &&
        item.tablename === 'messages'
      );

    return {
      success: true,
      hasMessagesTable: messagesIncluded,
      tables: data
    };
  } catch (error) {
    console.error("Error in checkRealtimeConfiguration:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      hasMessagesTable: false
    };
  }
}

/**
 * Verifica se a publicação supabase_realtime tem o modo de segurança correto
 */
export async function checkRealtimeSecurityMode() {
  try {
    const adminSupabase = createAdminClient();

    // Verificar o modo de segurança da publicação
    const { data, error } = await adminSupabase.rpc('execute_sql', {
      sql_query: `
        SELECT pubname, puballtables, pubinsert, pubupdate, pubdelete, pubtruncate
        FROM pg_publication
        WHERE pubname = 'supabase_realtime';
      `
    });

    if (error) {
      console.error("Error checking realtime security mode:", error);
      return {
        success: false,
        error: error.message,
        securityInfo: null
      };
    }

    return {
      success: true,
      securityInfo: data,
    };
  } catch (error) {
    console.error("Error in checkRealtimeSecurityMode:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      securityInfo: null
    };
  }
}

/**
 * Adiciona a tabela 'messages' à publicação real-time do Supabase
 */
export async function enableRealtimeForMessages() {
  try {
    const adminSupabase = createAdminClient();

    // Executar SQL para adicionar a tabela à publicação
    const { data, error } = await adminSupabase.rpc('execute_sql', {
      sql_query: `
        -- Verificar se a publicação existe
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime'
          ) THEN
            -- Criar a publicação se não existir
            EXECUTE 'CREATE PUBLICATION supabase_realtime FOR ALL TABLES';
          END IF;

          -- Adicionar a tabela 'messages' à publicação
          -- Primeiro removemos para evitar erros se já estiver adicionada
          ALTER PUBLICATION supabase_realtime DROP TABLE IF EXISTS messages;
          ALTER PUBLICATION supabase_realtime ADD TABLE messages;

          -- Garantir que os eventos INSERT sejam enviados pela publicação
          ALTER PUBLICATION supabase_realtime SET (publish = 'insert');

          -- Verificar se há regras de RLS que podem estar bloqueando
          -- Temporariamente desativar RLS para a tabela messages (opcional, pode remover se causar problemas)
          -- ALTER TABLE messages DISABLE ROW LEVEL SECURITY;

          -- Forçar o recarregamento do schema para o PostgREST
          NOTIFY pgrst, 'reload schema';
        END
        $$;
      `
    });

    if (error) {
      console.error("Error enabling realtime for messages:", error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      message: "Realtime enabled for messages table"
    };
  } catch (error) {
    console.error("Error in enableRealtimeForMessages:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Modifica a configuração dos canais Realtime para garantir que todas as mensagens sejam entregues
 */
export async function resetRealtimeSettings() {
  try {
    const adminSupabase = createAdminClient();

    // Executar SQL para redefinir as configurações do realtime
    const { data, error } = await adminSupabase.rpc('execute_sql', {
      sql_query: `
        -- Recriar a publicação do zero
        DROP PUBLICATION IF EXISTS supabase_realtime;
        CREATE PUBLICATION supabase_realtime FOR TABLE messages WITH (publish = 'insert');

        -- Atualizar o trigger do Realtime
        NOTIFY pgrst, 'reload schema';
      `
    });

    if (error) {
      console.error("Error resetting realtime settings:", error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      message: "Realtime settings reset successfully"
    };
  } catch (error) {
    console.error("Error in resetRealtimeSettings:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
