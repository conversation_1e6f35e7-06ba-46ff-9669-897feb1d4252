"use server";

import { getSignedUrl } from "storage";

/**
 * Server action to get a signed URL for an image
 *
 * @param path The path of the image in the bucket
 * @param bucket The bucket containing the image
 * @returns A signed URL for the image
 */
export async function getImageSignedUrl(path: string, bucket: string) {
  try {
    // Validate parameters
    if (!path) {
      throw new Error("Path is required");
    }
    if (!bucket) {
      throw new Error("Bucket is required");
    }

    // Security check to prevent accessing unauthorized buckets
    const allowedBuckets = [
      process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME,
      process.env.NEXT_PUBLIC_UPLOADS_BUCKET_NAME
    ];

    if (!allowedBuckets.includes(bucket)) {
      throw new Error("Access to this bucket is not allowed");
    }

    // Generate a signed URL
    const signedUrl = await getSignedUrl(path, {
      bucket,
      expiresIn: 3600 // 1 hour
    });

    return { url: signedUrl };
  } catch (error) {
    console.error("Error generating signed URL:", error);
    throw error;
  }
}
