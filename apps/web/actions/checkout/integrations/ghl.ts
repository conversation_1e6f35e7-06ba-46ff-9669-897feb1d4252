// src/lib/integrations/ghl.ts

import { db } from "database";



interface AppointmentNotificationData {
  appointmentId: string;
  doctor: {
    name: string;
    email: string;
    phone: string;
  };
  patient: {
    name: string;
    email: string;
    phone: string;
  };
  scheduledAt: Date;
  appointmentUrl: string;
}

export async function triggerAppointmentWorkflows(
  data: AppointmentNotificationData,
) {
  try {
    const GHL_WEBHOOK_URL = process.env.GHL_WEBHOOK_URL;

    if (!GHL_WEBHOOK_URL) {
      throw new Error("Missing GHL webhook URL");
    }

    // Prepare data for the webhook
    const webhookData = {
      type: "APPOINTMENT_CONFIRMED",
      appointmentId: data.appointmentId,
      appointmentUrl: data.appointmentUrl,
      scheduledAt: data.scheduledAt.toISOString(),
      formattedDate: data.scheduledAt.toLocaleDateString("pt-BR"),
      formattedTime: data.scheduledAt.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      }),
      doctor: {
        name: data.doctor.name,
        email: data.doctor.email,
        phone: formatPhone<PERSON>umber(data.doctor.phone),
        firstName: data.doctor.name.split(" ")[0],
        lastName: data.doctor.name.split(" ").slice(1).join(" "),
      },
      patient: {
        name: data.patient.name,
        email: data.patient.email,
        phone: formatPhoneNumber(data.patient.phone) || data?.patient?.phone,
        firstName: data.patient.name.split(" ")[0],
        lastName: data.patient.name.split(" ").slice(1).join(" "),
      },
    };

    console.log("Send to GHL webhook ==>>>webhookData", webhookData);

    // Send to GHL webhook
    const response = await fetch(GHL_WEBHOOK_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(webhookData),
    });

    if (!response.ok) {
      console.error("GHL Webhook Error:", await response.text());
      throw new Error("Failed to trigger GHL webhook");
    }

    console.log("[GHL_WEBHOOK] Successfully triggered");
    return true;
  } catch (error) {
    console.error("[GHL_WEBHOOK_ERROR]", error);
    // Não lançamos o erro para não interromper o fluxo principal
    return false;
  }
}

// Função auxiliar para formatação do número de telefone
function formatPhoneNumber(phone: string): string {



  console.log("formatPhoneNumber =======>>>",phone);


  // Remove todos os caracteres não numéricos
  const cleaned = phone?.replace(/\D/g, "");

  console.log(phone);
  console.log(cleaned);
  console.log(cleaned?.length);

  // Adiciona o código do país (Brasil) se não existir
  if (cleaned?.length === 11 || cleaned?.length === 10) {
    return `55${cleaned}`;
  }

  return cleaned;
}

// Função específica para processar pagamento confirmado
export async function handlePaymentConfirmed(appointmentId: string) {
  try {
    // Buscar dados da consulta
    const appointment = await db.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: { user: true },
        },
        patient: {
          include: { user: true },
        },
      },
    });

    if (!appointment) {
      throw new Error("Appointment not found");
    }

    // Gerar URL da consulta
    // const appointmentUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/appointments/${appointmentId}`;

    const appointmentUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/login`;


    const dataToTrigger = {
      appointmentId,
      doctor: {
        name: appointment.doctor.user.name!,
        email: appointment.doctor.user.email!,
        phone: appointment.doctor.user.phone!,
      },
      patient: {
        name: appointment.patient.user.name!,
        email: appointment.patient.user.email!,
        phone: appointment.patient.user.phone!,
      },
      scheduledAt: appointment.scheduledAt,
      appointmentUrl,
    }


    console.log("dataToTrigger", dataToTrigger);

    // Acionar webhook
    await triggerAppointmentWorkflows({
      appointmentId,
      doctor: {
        name: appointment.doctor.user.name!,
        email: appointment.doctor.user.email!,
        phone: appointment.doctor.user.phone!,
      },
      patient: {
        name: appointment.patient.user.name!,
        email: appointment.patient.user.email!,
        phone: appointment.patient.user.phone!,
      },
      scheduledAt: appointment.scheduledAt,
      appointmentUrl,
    });

    return true;
  } catch (error) {
    console.error("[PAYMENT_CONFIRMED_NOTIFICATION_ERROR]", error);
    return false;
  }
}
