// src/lib/types/evolution.ts

export interface EvolutionWebhookBase {
	event: string;
	instance: string;
	data: any;
	destination: string;
	date_time: string;
	sender: string;
	server_url: string;
	apikey: string;
}

export interface EvolutionSendMessage {
	number: string;
	text: string;
	delay?: number;
	linkPreview?: boolean;
}

export interface EvolutionSendButtons {
	number: string;
	title: string;
	description: string;
	footer: string;
	buttons: Array<{
		type: 'reply' | 'copy' | 'url' | 'call' | 'pix';
		displayText?: string;
		id?: string;
		copyCode?: string;
		url?: string;
		phoneNumber?: string;
		currency?: string;
		name?: string;
		keyType?: string;
		key?: string;
	}>;
	delay?: number;
	quoted?: {
		key: {
			id: string;
		};
		message: {
			conversation: string;
		};
	};
	mentionsEveryOne?: boolean;
	mentioned?: string[];
}

export interface EvolutionResponse {
	key: {
		remoteJid: string;
		fromMe: boolean;
		id: string;
	};
	message: {
		conversation: string;
	};
	messageTimestamp: string;
	status: string;
}
