// src/lib/services/evolution.service.ts
import {
	EvolutionSendMessage,
	EvolutionResponse,
	EvolutionSendButtons,
} from './types-evolution';
import { formatPhoneForWhatsApp as formatPhoneUtil } from '../../../../lib/utils/format-phone';

export class EvolutionService {
	private readonly apiKey: string;
	private readonly instance: string;
	private readonly baseUrl: string;

	constructor() {
		this.apiKey = process.env.EVOLUTION_API_KEY!;
		this.instance = process.env.EVOLUTION_INSTANCE!;
		this.baseUrl =
			process.env.EVOLUTION_URL || 'https://evo2.clouds3.nextrusti.com';
	}

	// Format phone number correctly for WhatsApp API
	private formatPhoneForWhatsApp(phone: string): string {
		// Use our utility function for consistent formatting
		const formattedPhone = formatPhoneUtil(phone);

		// Add extra validation and logging
		console.log("[EVOLUTION_API] Phone number formatted for WhatsApp:", {
			original: phone,
			formatted: formattedPhone,
			length: formattedPhone.length
		});

		return formattedPhone;
	}

	async sendMessage(message: EvolutionSendMessage): Promise<EvolutionResponse> {
		// Format the phone number correctly before sending
		const formattedMessage = {
			...message,
			number: this.formatPhoneForWhatsApp(message.number)
		};

		console.log("[EVOLUTION_API] Sending message to formatted number:", formattedMessage.number);

		const response = await fetch(
			`${this.baseUrl}/message/sendText/${this.instance}`,
			{
				method: 'POST',
				headers: {
					apikey: this.apiKey,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formattedMessage),
			}
		);

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Evolution API Error: ${response.status} - ${errorText}`);
		}

		return response.json();
	}

	// send buttons
	async sendButtons(message: EvolutionSendButtons): Promise<EvolutionResponse> {
		// Format the phone number correctly before sending
		const formattedMessage = {
			...message,
			number: this.formatPhoneForWhatsApp(message.number)
		};

		console.log("[EVOLUTION_API] Sending buttons to formatted number:", formattedMessage.number);

		const response = await fetch(
			`${this.baseUrl}/message/sendButtons/${this.instance}`,
			{
				method: 'POST',
				headers: {
					apikey: this.apiKey,
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(formattedMessage),
			}
		);

		console.log('sendButtons response', response);

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Evolution API Error: ${response.status} - ${errorText}`);
		}

		return response.json();
	}

	/**
	 * Envia uma única mensagem para grupos (sem delay)
	 */
	async sendGroupMessage(message: string, groupId: string): Promise<EvolutionResponse> {
		console.log("[EVOLUTION_API] Enviando mensagem única para grupo:", {
			groupId,
			messageLength: message.length
		});

		const messageData: EvolutionSendMessage = {
			number: groupId,
			text: message,
			delay: 0, // Sem delay para grupos
			linkPreview: false
		};

		return this.sendMessage(messageData);
	}

	async sendMessagesWithDelay(
		messages: string[],
		number: string
	): Promise<EvolutionResponse[]> {
		const responses: EvolutionResponse[] = [];
		const formattedNumber = this.formatPhoneForWhatsApp(number);
		const MAX_RETRIES = 2;

		console.log("[EVOLUTION_API] Iniciando envio de múltiplas mensagens:", {
			phoneFormatted: formattedNumber,
			messageCount: messages.length,
			messageChars: messages.map(m => m.length)
		});

		for (let i = 0; i < messages.length; i++) {
			// Simplified text to avoid potential encoding issues
			const text = messages[i].trim();

			// Skip empty messages
			if (!text) {
				console.warn("[EVOLUTION_API] Mensagem vazia detectada e ignorada no índice:", i);
				continue;
			}

			// Calculate delays - use longer delays for more stability
			const typingDelay = Math.min(text.length * 40, 2000); // Slower typing simulation
			const betweenMessageDelay = 1500; // Longer delay between messages
			const totalDelay = typingDelay + (i > 0 ? betweenMessageDelay : 0);

			const message: EvolutionSendMessage = {
				number: formattedNumber,
				text: text,
				delay: totalDelay,
				linkPreview: false, // Disable link preview for stability
			};

			console.log(`[EVOLUTION_API] Enviando mensagem ${i+1}/${messages.length}:`, {
				charLength: text.length,
				delay: totalDelay
			});

			// Try to send with retries
			let retryCount = 0;
			let success = false;

			while (!success && retryCount <= MAX_RETRIES) {
				try {
					// Add a delay before retry attempts
					if (retryCount > 0) {
						console.log(`[EVOLUTION_API] Tentativa ${retryCount} para mensagem ${i+1}`);
						// Wait before retry
						await new Promise(resolve => setTimeout(resolve, 2000));
					}

					const response = await this.sendMessage(message);
					responses.push(response);
					success = true;

					console.log(`[EVOLUTION_API] Mensagem ${i+1} enviada com sucesso:`, {
						messageId: response?.key?.id,
						status: response?.status
					});

					// Add delay after each successful message to ensure WhatsApp API isn't overloaded
					await new Promise(resolve => setTimeout(resolve, 1000));
				} catch (error) {
					retryCount++;
					console.error(`[EVOLUTION_API] Erro no envio da mensagem ${i+1} (tentativa ${retryCount}):`, error);

					if (retryCount > MAX_RETRIES) {
						console.error(`[EVOLUTION_API] Falha após ${MAX_RETRIES} tentativas para mensagem ${i+1}`);
						// Add empty response to maintain index alignment
						responses.push({} as EvolutionResponse);
					}
				}
			}
		}

		console.log("[EVOLUTION_API] Envio de múltiplas mensagens finalizado:", {
			sent: responses.filter(r => r?.key?.id).length,
			total: messages.length
		});

		return responses;
	}

	/**
	 * Enviar múltiplas mensagens para um grupo WhatsApp
	 */
	async sendMessagesToGroup(
		messages: string[],
		groupId: string
	): Promise<EvolutionResponse[]> {
		const responses: EvolutionResponse[] = [];
		const MAX_RETRIES = 2;

		console.log("[EVOLUTION_API] Enviando mensagens para grupo:", {
			groupId: groupId.slice(-8) + '...',
			messageCount: messages.length
		});

		for (let i = 0; i < messages.length; i++) {
			const text = messages[i].trim();

			if (!text) {
				console.warn("[EVOLUTION_API] Mensagem vazia ignorada no grupo:", i);
				continue;
			}

			const betweenMessageDelay = i > 0 ? 2000 : 500; // Delay between group messages

			const message: EvolutionSendMessage = {
				number: groupId, // Para grupos, usar o groupId diretamente
				text: text,
				delay: betweenMessageDelay,
				linkPreview: false,
			};

			let retryCount = 0;
			let success = false;

			while (!success && retryCount <= MAX_RETRIES) {
				try {
					const response = await this.sendMessage(message);
					responses.push(response);
					success = true;

					console.log(`[EVOLUTION_API] Mensagem ${i+1} enviada para grupo:`, {
						responseId: response?.key?.id || 'ID não disponível'
					});

				} catch (error) {
					retryCount++;
					console.error(`[EVOLUTION_API] Erro no envio para grupo (tentativa ${retryCount}):`, error);

					if (retryCount <= MAX_RETRIES) {
						await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
					} else {
						responses.push({} as EvolutionResponse);
					}
				}
			}
		}

		return responses;
	}

	/**
	 * Enviar mensagem única para grupo
	 */
	async sendMessageToGroup(text: string, groupId: string): Promise<EvolutionResponse> {
		const message: EvolutionSendMessage = {
			number: groupId,
			text: text.trim(),
			delay: 500,
			linkPreview: false,
		};

		console.log("[EVOLUTION_API] Enviando mensagem única para grupo:", {
			groupId: groupId.slice(-8) + '...',
			textLength: text.length
		});

		return this.sendMessage(message);
	}
}
