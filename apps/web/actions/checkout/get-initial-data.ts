// src/actions/checkout/get-initial-data.ts
"use server";

import { notFound } from "next/navigation";
import { prisma } from "../../lib/db";
import { ON_DUTY_CONFIG, PARTNER_CONFIG } from "api/constants/on-duty";

interface CheckoutParams {
  doctorId: string;
  date?: string;
  time?: string;
  isOnDuty?: boolean; // Indica se é plantão
  urgencyLevel?: "high" | "medium" | "low"; // Nível de urgência para plantão
  partner?: string; // Novo parâmetro para identificar parceiros
}

export async function getCheckoutInitialData(params: CheckoutParams) {
  try {
    const { doctorId, date, time, isOnDuty = false, urgencyLevel, partner } = params;

    // Buscar dados do médico com suas especialidades usando Prisma
    const doctor = await prisma.doctor.findUnique({
      where: { id: doctorId },
      include: {
        user: true,
        specialties: true,
      },
    });

    if (!doctor) {
      console.error("[CHECKOUT_INITIAL_DATA_ERROR] Doctor not found");
      notFound();
    }

    // Definir valor da taxa de serviço (pode ser configurável no futuro)
    const serviceFee = 0; // Por enquanto, grátis

    // Valores base para cada tipo de consulta
    let price = Number(doctor.consultationPrice);
    let duration = doctor.consultationDuration;
    let scheduledAt: Date | null = null;

    // VERIFICAÇÃO DE PARCEIRO TEM PRIORIDADE ABSOLUTA
    // Verificar se é uma consulta de parceiro - sempre tem precedência sobre isOnDuty
    if (partner && partner !== "undefined" && partner.trim() !== "") {
      console.log(`[CHECKOUT] Partner detected (${partner}). Ignoring other parameters.`);

      const partnerUpperCase = partner.toUpperCase();
      // Verificar se o parceiro está configurado em PARTNER_CONFIG
      if (partnerUpperCase === 'FARMACIA' && PARTNER_CONFIG.FARMACIA) {
        price = PARTNER_CONFIG.FARMACIA.price;
        duration = PARTNER_CONFIG.FARMACIA.duration;
        console.log(`[CHECKOUT] Farmacia pricing applied: Price: ${price}`);
      } else if (partnerUpperCase === 'LOOPMAIS' && PARTNER_CONFIG.LOOPMAIS) {
        price = PARTNER_CONFIG.LOOPMAIS.price;
        duration = PARTNER_CONFIG.LOOPMAIS.duration;
        console.log(`[CHECKOUT] LoopMais pricing applied: Price: ${price}`);
      } else {
        // Se o parceiro não estiver configurado, aplicar preço padrão de parceiros
        price = PARTNER_CONFIG.DEFAULT_PARTNER_PRICE;
        console.log(`[CHECKOUT] Default partner pricing applied: ${PARTNER_CONFIG.DEFAULT_PARTNER_PRICE}`);
      }
    }
    // Se não for parceiro mas for plantão, aplicar preço por urgência
    else if (isOnDuty) {
      if (urgencyLevel === "high") {
        price = ON_DUTY_CONFIG.HIGH.price;
        duration = ON_DUTY_CONFIG.HIGH.duration;
        console.log(`[CHECKOUT] High urgency pricing applied: ${price}`);
      } else if (urgencyLevel === "medium") {
        price = ON_DUTY_CONFIG.MEDIUM.price;
        duration = ON_DUTY_CONFIG.MEDIUM.duration;
        console.log(`[CHECKOUT] Medium urgency pricing applied: ${price}`);
      } else { // low
        price = ON_DUTY_CONFIG.LOW.price;
        duration = ON_DUTY_CONFIG.LOW.duration;
        console.log(`[CHECKOUT] Low urgency pricing applied: ${price}`);
      }
    } else {
      // Consulta agendada normal - combinar data e hora
      if (!date || !time) {
        console.error("[CHECKOUT_INITIAL_DATA_ERROR] Missing date or time for scheduled appointment");
        notFound();
      }

      const [hours, minutes] = time.split(":");
      scheduledAt = new Date(date);
      scheduledAt.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      console.log(`[CHECKOUT] Regular appointment pricing (doctor's rate): ${price}`);
    }

    // Formata os dados para retorno de acordo com o schema atualizado
    return {
      doctor: {
        id: doctor.id,
        name: doctor.user.name,
        profileImage: doctor.user.avatarUrl,
        specialty: doctor.specialties[0]?.name,
        crm: doctor.crm,
        crmState: doctor.crmState,
      },
      price,
      duration,
      scheduledAt,
      isOnDuty,
      urgencyLevel,
      isOnline: true, // Por padrão, todas são consultas online
      serviceFee, // Taxa de serviço
      partner: partner && partner !== "undefined" ? partner : undefined, // Incluir a informação do parceiro no retorno, apenas se válido
    };
  } catch (error) {
    console.error("[CHECKOUT_INITIAL_DATA_ERROR]", error);
    throw new Error("Erro ao carregar dados do checkout");
  }
}
