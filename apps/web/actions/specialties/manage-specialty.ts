"use server";

import { db } from "database";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const specialtySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
});

type SpecialtyData = z.infer<typeof specialtySchema>;

export async function createSpecialty(data: SpecialtyData) {
  try {
    const validation = specialtySchema.safeParse(data);
    if (!validation.success) {
      return {
        status: "error",
        message: validation.error.errors[0].message,
      };
    }

    const existingSpecialty = await db.specialty.findFirst({
      where: {
        name: data.name,
      },
    });

    if (existingSpecialty) {
      return {
        status: "error",
        message: "Especialidade já cadastrada",
      };
    }

    const specialty = await db.specialty.create({
      data: {
        id: crypto.randomUUID(),
        name: data.name,
      },
    });

    revalidatePath("/admin/specialties");
    return { status: "success", data: specialty };
  } catch (error) {
    console.error("Create specialty error:", error);
    return {
      status: "error",
      message: "Erro ao criar especialidade",
    };
  }
}

export async function updateSpecialty(id: string, data: SpecialtyData) {
  try {
    const validation = specialtySchema.safeParse(data);
    if (!validation.success) {
      return {
        status: "error",
        message: validation.error.errors[0].message,
      };
    }

    const specialty = await db.specialty.findUnique({
      where: { id },
    });

    if (!specialty) {
      return {
        status: "error",
        message: "Especialidade não encontrada",
      };
    }

    // Check if name is already in use by another specialty
    const existingSpecialty = await db.specialty.findFirst({
      where: {
        AND: [
          { name: data.name },
          { id: { not: id } }
        ],
      },
    });

    if (existingSpecialty) {
      return {
        status: "error",
        message: "Nome já está em uso por outra especialidade",
      };
    }

    const updatedSpecialty = await db.specialty.update({
      where: { id },
      data: {
        name: data.name,
      },
    });

    revalidatePath("/admin/specialties");
    return { status: "success", data: updatedSpecialty };
  } catch (error) {
    console.error("Update specialty error:", error);
    return {
      status: "error",
      message: "Erro ao atualizar especialidade",
    };
  }
}

export async function deleteSpecialty(id: string) {
  try {
    const specialty = await db.specialty.findUnique({
      where: { id },
    });

    if (!specialty) {
      return {
        status: "error",
        message: "Especialidade não encontrada",
      };
    }

    await db.specialty.delete({
      where: { id },
    });

    revalidatePath("/admin/specialties");
    return { status: "success" };
  } catch (error) {
    console.error("Delete specialty error:", error);
    return {
      status: "error",
      message: "Erro ao excluir especialidade",
    };
  }
}