"use server";

export async function checkEnvironmentVars() {
  return {
    asaasApiUrl: process.env.ASAAS_API_URL || 'not set',
    asaasApiKeyExists: <PERSON><PERSON><PERSON>(process.env.ASAAS_API_KEY),
    asaasApiKeyLength: process.env.ASAAS_API_KEY?.length || 0,
    asaasWebhookTokenExists: <PERSON><PERSON><PERSON>(process.env.ASAAS_WEBHOOK_TOKEN),
    envKeys: Object.keys(process.env).filter(key => key.includes('ASAAS'))
  };
}
