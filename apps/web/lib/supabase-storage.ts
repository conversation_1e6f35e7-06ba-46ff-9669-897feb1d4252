import { createClient } from '@supabase/supabase-js';

// Create a Supabase client for storage operations
function getSupabaseStorageClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase configuration for storage operations');
  }

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export interface UploadResult {
  url: string;
  key: string;
}

export async function uploadPrescriptionPDF(
  file: ArrayBuffer | Buffer,
  filename: string,
  metadata: {
    appointmentId: string;
    originalName: string;
    uploadedAt: string;
    uploadedBy?: string;
  }
): Promise<UploadResult> {
  try {
    console.log('🔄 Starting prescription PDF upload:', { filename, metadata });

    const supabase = getSupabaseStorageClient();
    const bucketName = process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || 'prescriptions';

    console.log('📦 Using bucket:', bucketName);

    // Convert ArrayBuffer to Uint8Array if needed
    const fileData = file instanceof ArrayBuffer ? new Uint8Array(file) : file;
    console.log('📄 File size:', fileData.length, 'bytes');

    // Check if bucket exists first
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    if (bucketsError) {
      console.error('❌ Error listing buckets:', bucketsError);
      throw new Error(`Cannot access storage buckets: ${bucketsError.message}`);
    }

    const bucketExists = buckets?.some(b => b.name === bucketName);
    if (!bucketExists) {
      console.error('❌ Bucket not found:', bucketName);
      console.log('📋 Available buckets:', buckets?.map(b => b.name));
      throw new Error(`Bucket '${bucketName}' not found. Available buckets: ${buckets?.map(b => b.name).join(', ')}`);
    }

    console.log('✅ Bucket exists, proceeding with upload');

    // Upload file to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filename, fileData, {
        contentType: 'application/pdf',
        metadata: metadata,
        upsert: true
      });

    if (error) {
      console.error('❌ Supabase storage upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }

    console.log('✅ File uploaded successfully:', data);

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filename);

    console.log('🔗 Generated public URL:', publicUrl);

    return {
      url: publicUrl,
      key: filename
    };
  } catch (error) {
    console.error('💥 Error uploading prescription PDF:', error);
    throw error;
  }
}

export async function deletePrescriptionPDF(filename: string): Promise<void> {
  try {
    const supabase = getSupabaseStorageClient();
    const bucketName = process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || 'prescriptions';

    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filename]);

    if (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  } catch (error) {
    console.error('Error deleting prescription PDF:', error);
    throw error;
  }
}

export async function getSignedPrescriptionURL(
  filename: string,
  expiresIn: number = 3600
): Promise<string> {
  try {
    const supabase = getSupabaseStorageClient();
    const bucketName = process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || 'prescriptions';

    const { data, error } = await supabase.storage
      .from(bucketName)
      .createSignedUrl(filename, expiresIn);

    if (error) {
      console.error('Error creating signed URL:', error);
      throw new Error(`Failed to create signed URL: ${error.message}`);
    }

    return data.signedUrl;
  } catch (error) {
    console.error('Error getting signed prescription URL:', error);
    throw error;
  }
}
