// Utility to check storage configuration
export function checkStorageConfig() {
  const config = {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
    supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    prescriptionsBucket: process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || 'prescriptions',
    s3Endpoint: process.env.S3_ENDPOINT,
    s3AccessKey: process.env.S3_ACCESS_KEY_ID,
    s3SecretKey: process.env.S3_SECRET_ACCESS_KEY,
  };

  const issues = [];

  // Check Supabase configuration
  if (!config.supabaseUrl) {
    issues.push('Missing NEXT_PUBLIC_SUPABASE_URL');
  }
  if (!config.supabaseServiceKey) {
    issues.push('Missing SUPABASE_SERVICE_ROLE_KEY');
  }

  // Check if using S3 or Supabase storage
  const usingS3 = config.s3Endpoint && config.s3AccessKey && config.s3SecretKey;
  const usingSupabase = config.supabaseUrl && config.supabaseServiceKey;

  console.log('🔧 Storage Configuration Check:');
  console.log('  Supabase URL:', config.supabaseUrl ? '✓' : '✗');
  console.log('  Supabase Service Key:', config.supabaseServiceKey ? '✓' : '✗');
  console.log('  Prescriptions Bucket:', config.prescriptionsBucket);
  console.log('  S3 Endpoint:', config.s3Endpoint ? '✓' : '✗');
  console.log('  S3 Access Key:', config.s3AccessKey ? '✓' : '✗');
  console.log('  S3 Secret Key:', config.s3SecretKey ? '✓' : '✗');
  console.log('  Using S3:', usingS3 ? '✓' : '✗');
  console.log('  Using Supabase:', usingSupabase ? '✓' : '✗');

  if (issues.length > 0) {
    console.warn('⚠️ Storage configuration issues:', issues);
  }

  return {
    config,
    issues,
    usingS3,
    usingSupabase,
    isValid: issues.length === 0 && (usingS3 || usingSupabase)
  };
}
