'use client';

import { useAnalytics } from './useAnalytics';

export function useTelemedicineAnalytics() {
  const { track, setGroupProperties } = useAnalytics();

  const trackAppointmentScheduled = (appointmentData: {
    appointmentId: string;
    doctorId: string;
    patientId: string;
    specialty: string;
    scheduledFor: string;
    duration: number;
  }) => {
    track('appointment_scheduled', {
      appointment_id: appointmentData.appointmentId,
      doctor_id: appointmentData.doctorId,
      patient_id: appointmentData.patientId,
      specialty: appointmentData.specialty,
      scheduled_for: appointmentData.scheduledFor,
      duration_minutes: appointmentData.duration,
      timestamp: new Date().toISOString(),
    });
  };

  const trackAppointmentCancelled = (appointmentData: {
    appointmentId: string;
    reason: string;
    cancelledBy: 'patient' | 'doctor' | 'system';
  }) => {
    track('appointment_cancelled', {
      appointment_id: appointmentData.appointmentId,
      reason: appointmentData.reason,
      cancelled_by: appointmentData.cancelledBy,
      timestamp: new Date().toISOString(),
    });
  };

  const trackVideoCallStarted = (callData: {
    appointmentId: string;
    roomId: string;
    participants: number;
  }) => {
    track('video_call_started', {
      appointment_id: callData.appointmentId,
      room_id: callData.roomId,
      participants_count: callData.participants,
      timestamp: new Date().toISOString(),
    });
  };

  const trackVideoCallEnded = (callData: {
    appointmentId: string;
    roomId: string;
    duration: number;
    endReason: 'completed' | 'disconnected' | 'error';
  }) => {
    track('video_call_ended', {
      appointment_id: callData.appointmentId,
      room_id: callData.roomId,
      duration_seconds: callData.duration,
      end_reason: callData.endReason,
      timestamp: new Date().toISOString(),
    });
  };

  const trackPrescriptionCreated = (prescriptionData: {
    prescriptionId: string;
    appointmentId: string;
    doctorId: string;
    patientId: string;
    medicationsCount: number;
  }) => {
    track('prescription_created', {
      prescription_id: prescriptionData.prescriptionId,
      appointment_id: prescriptionData.appointmentId,
      doctor_id: prescriptionData.doctorId,
      patient_id: prescriptionData.patientId,
      medications_count: prescriptionData.medicationsCount,
      timestamp: new Date().toISOString(),
    });
  };

  const trackPrescriptionShared = (prescriptionData: {
    prescriptionId: string;
    method: 'email' | 'whatsapp' | 'download';
  }) => {
    track('prescription_shared', {
      prescription_id: prescriptionData.prescriptionId,
      sharing_method: prescriptionData.method,
      timestamp: new Date().toISOString(),
    });
  };

  const trackPaymentInitiated = (paymentData: {
    appointmentId: string;
    amount: number;
    currency: string;
    paymentMethod: string;
  }) => {
    track('payment_initiated', {
      appointment_id: paymentData.appointmentId,
      amount: paymentData.amount,
      currency: paymentData.currency,
      payment_method: paymentData.paymentMethod,
      timestamp: new Date().toISOString(),
    });
  };

  const trackPaymentCompleted = (paymentData: {
    appointmentId: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    transactionId: string;
  }) => {
    track('payment_completed', {
      appointment_id: paymentData.appointmentId,
      amount: paymentData.amount,
      currency: paymentData.currency,
      payment_method: paymentData.paymentMethod,
      transaction_id: paymentData.transactionId,
      timestamp: new Date().toISOString(),
    });
  };

  const trackWhatsAppMessageSent = (messageData: {
    appointmentId: string;
    messageType: 'appointment_reminder' | 'prescription_ready' | 'follow_up';
    recipientType: 'patient' | 'doctor';
  }) => {
    track('whatsapp_message_sent', {
      appointment_id: messageData.appointmentId,
      message_type: messageData.messageType,
      recipient_type: messageData.recipientType,
      timestamp: new Date().toISOString(),
    });
  };

  return {
    trackAppointmentScheduled,
    trackAppointmentCancelled,
    trackVideoCallStarted,
    trackVideoCallEnded,
    trackPrescriptionCreated,
    trackPrescriptionShared,
    trackPaymentInitiated,
    trackPaymentCompleted,
    trackWhatsAppMessageSent,
  };
}
