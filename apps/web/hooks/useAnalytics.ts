'use client';

import { usePostHog } from './usePostHog';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

export function useAnalytics() {
  const { posthog, isLoaded } = usePostHog();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  useEffect(() => {
    if (isLoaded && posthog) {
      const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '');
      posthog.capture('$pageview', {
        $current_url: url,
      });
    }
  }, [pathname, searchParams, isLoaded, posthog]);

  const track = (event: string, properties?: Record<string, any>) => {
    if (isLoaded && posthog) {
      posthog.capture(event, properties);
    }
  };

  const identify = (userId: string, properties?: Record<string, any>) => {
    if (isLoaded && posthog) {
      posthog.identify(userId, properties);
    }
  };

  const reset = () => {
    if (isLoaded && posthog) {
      posthog.reset();
    }
  };

  const setPersonProperties = (properties: Record<string, any>) => {
    if (isLoaded && posthog) {
      posthog.setPersonProperties(properties);
    }
  };

  const setGroupProperties = (groupType: string, groupKey: string, properties: Record<string, any>) => {
    if (isLoaded && posthog) {
      posthog.group(groupType, groupKey, properties);
    }
  };

  const getFeatureFlag = (key: string) => {
    if (isLoaded && posthog) {
      return posthog.getFeatureFlag(key);
    }
    return undefined;
  };

  const isFeatureEnabled = (key: string) => {
    if (isLoaded && posthog) {
      return posthog.isFeatureEnabled(key);
    }
    return false;
  };

  return {
    track,
    identify,
    reset,
    setPersonProperties,
    setGroupProperties,
    getFeatureFlag,
    isFeatureEnabled,
    isLoaded,
  };
}
