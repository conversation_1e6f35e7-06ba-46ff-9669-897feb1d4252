'use client';

import React from 'react';
import { useAnalytics } from './useAnalytics';
import { useUser } from '@saas/auth/hooks/use-user';

export function useAuthAnalytics() {
  const { track, identify, setPersonProperties } = useAnalytics();
  const { user, loaded } = useUser();

  // Track user identification when user loads
  React.useEffect(() => {
    if (loaded && user) {
      identify(user.id, {
        email: user.email,
        name: user.name,
        role: user.role,
        created_at: user.createdAt,
        last_login: new Date().toISOString(),
      });

      // Set additional person properties
      setPersonProperties({
        user_id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        is_verified: user.emailVerified,
        subscription_status: user.subscriptionStatus || 'free',
      });
    }
  }, [loaded, user, identify, setPersonProperties]);

  const trackLogin = (method: 'password' | 'magic_link' | 'oauth', provider?: string) => {
    track('user_logged_in', {
      method,
      provider,
      timestamp: new Date().toISOString(),
    });
  };

  const trackLogout = () => {
    track('user_logged_out', {
      timestamp: new Date().toISOString(),
    });
  };

  const trackSignUp = (method: 'email' | 'oauth', provider?: string) => {
    track('user_signed_up', {
      method,
      provider,
      timestamp: new Date().toISOString(),
    });
  };

  const trackPasswordReset = () => {
    track('password_reset_requested', {
      timestamp: new Date().toISOString(),
    });
  };

  const trackEmailVerification = (success: boolean) => {
    track('email_verification', {
      success,
      timestamp: new Date().toISOString(),
    });
  };

  const trackOnboardingComplete = (step: string) => {
    track('onboarding_step_completed', {
      step,
      timestamp: new Date().toISOString(),
    });
  };

  return {
    trackLogin,
    trackLogout,
    trackSignUp,
    trackPasswordReset,
    trackEmailVerification,
    trackOnboardingComplete,
  };
}
