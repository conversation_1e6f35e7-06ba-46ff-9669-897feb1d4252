import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Verificar se é médico ou admin
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search");

    const skip = (page - 1) * limit;

    // Construir filtros
    let whereClause: any = {
      // Apenas consultas concluídas ou em andamento
      status: {
        in: ['COMPLETED', 'IN_PROGRESS']
      },
      // Que não tenham prescrição
      prescription: null
    };

    if (user.role === "DOCTOR") {
      // Médicos só veem suas próprias consultas
      whereClause.doctor = {
        userId: user.id
      };
    }

    if (search) {
      whereClause.OR = [
        {
          patient: {
            user: {
              name: {
                contains: search,
                mode: 'insensitive'
              }
            }
          }
        },
        {
          id: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // Buscar consultas sem prescrição
    const [appointments, total] = await Promise.all([
      db.appointment.findMany({
        where: whereClause,
        include: {
          patient: {
            include: {
              user: true
            }
          },
          doctor: {
            include: {
              user: true
            }
          }
        },
        orderBy: {
          scheduledAt: 'desc'
        },
        skip,
        take: limit
      }),
      db.appointment.count({
        where: whereClause
      })
    ]);

    // Formatar dados para o frontend
    const formattedAppointments = appointments.map(appointment => ({
      id: appointment.id,
      patientName: appointment.patient.user.name,
      patientId: appointment.patient.id,
      doctorName: appointment.doctor?.user.name || "Médico",
      scheduledAt: appointment.scheduledAt.toISOString(),
      status: appointment.status,
      symptoms: appointment.symptoms,
      consultType: appointment.consultType,
      duration: appointment.duration
    }));

    return NextResponse.json({
      appointments: formattedAppointments,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error("Erro ao buscar consultas sem prescrição:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
