import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { PaymentStatusSchema, PaymentMethodSchema, type PaymentStatusType, type PaymentMethodType } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || '30days';
    const doctorId = searchParams.get('doctorId');
    const status = searchParams.get('status');
    const method = searchParams.get('method');

    // Calcular data de início baseada no período
    const now = new Date();
    let startDate = new Date();

    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case '7days':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30days':
        startDate.setDate(now.getDate() - 30);
        break;
      case '3months':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case '6months':
        startDate.setMonth(now.getMonth() - 6);
        break;
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Construir filtros
    const where: any = {
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (doctorId && doctorId !== 'all') {
      where.doctorId = doctorId;
    }

    if (status && status !== 'all') {
      where.status = status as PaymentStatusType;
    }

    if (method && method !== 'all') {
      where.paymentMethod = method as PaymentMethodType;
    }

    // Buscar transações
    const transactions = await db.transaction.findMany({
      where,
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
        appointment: {
          include: {
            patient: {
              include: {
                user: {
                  select: {
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Calcular métricas globais
    const totalRevenue = transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.amount), 0);

    const platformFee = transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.platformFee), 0);

    const doctorRevenue = transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

    const totalTransactions = transactions.length;

    // Top 10 médicos por receita
    const doctorStats = new Map();
    transactions
      .filter(t => t.status === 'PAID' && t.doctor)
      .forEach(t => {
        const doctorId = t.doctorId!;
        const doctorName = t.doctor?.user?.name || 'Médico';

        if (!doctorStats.has(doctorId)) {
          doctorStats.set(doctorId, {
            doctorId,
            doctorName,
            revenue: 0,
            appointments: 0,
          });
        }

        const stats = doctorStats.get(doctorId);
        stats.revenue += Number(t.doctorAmount);
        stats.appointments += 1;
      });

    const topDoctors = Array.from(doctorStats.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Dados para gráfico de evolução (últimos 30 dias)
    const chartData = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      const dayTransactions = transactions.filter(t => {
        const tDate = new Date(t.createdAt);
        return tDate >= dayStart && tDate <= dayEnd && t.status === 'PAID';
      });

      const dayRevenue = dayTransactions.reduce((sum, t) => sum + Number(t.amount), 0);
      const dayPlatformFee = dayTransactions.reduce((sum, t) => sum + Number(t.platformFee), 0);
      const dayDoctorRevenue = dayTransactions.reduce((sum, t) => sum + Number(t.doctorAmount), 0);

      chartData.push({
        date: date.toISOString().split('T')[0],
        revenue: dayRevenue,
        platformFee: dayPlatformFee,
        doctorRevenue: dayDoctorRevenue,
      });
    }

    // Distribuição por status
    const statusDistribution = ['PENDING', 'PAID', 'REFUNDED', 'FAILED'].map(status => {
      const statusTransactions = transactions.filter(t => t.status === status);
      return {
        status,
        count: statusTransactions.length,
        amount: statusTransactions.reduce((sum, t) => sum + Number(t.amount), 0),
      };
    }).filter(s => s.count > 0);

    // Distribuição por método de pagamento
    const paymentMethods = ['CREDIT_CARD', 'PIX', 'BOLETO'].map(method => {
      const methodTransactions = transactions.filter(t =>
        t.paymentMethod === method && t.status === 'PAID'
      );
      return {
        method,
        count: methodTransactions.length,
        amount: methodTransactions.reduce((sum, t) => sum + Number(t.amount), 0),
      };
    }).filter(pm => pm.count > 0);

    // Formatar transações para resposta
    const formattedTransactions = transactions.slice(0, 50).map(t => ({
      id: t.id,
      amount: Number(t.amount),
      doctorAmount: Number(t.doctorAmount),
      platformFee: Number(t.platformFee),
      status: t.status,
      paymentMethod: t.paymentMethod,
      paidAt: t.paidAt?.toISOString(),
      createdAt: t.createdAt.toISOString(),
      doctorName: t.doctor?.user?.name || 'Médico',
      patientName: t.appointment?.patient?.user?.name || 'Paciente',
      appointmentDate: t.appointment?.scheduledAt?.toISOString(),
    }));

    return NextResponse.json({
      globalMetrics: {
        totalRevenue,
        platformFee,
        doctorRevenue,
        totalTransactions,
      },
      topDoctors,
      chartData,
      statusDistribution,
      paymentMethods,
      transactions: formattedTransactions,
    });

  } catch (error) {
    console.error('Error fetching admin financial data:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
