import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: {
        id: true,
        bankAccount: true,
      },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Verificar se o médico tem conta bancária configurada
    if (!doctor.bankAccount) {
      return NextResponse.json({
        error: 'Conta bancária não configurada. Configure sua conta bancária para solicitar saques.',
        code: 'BANK_ACCOUNT_NOT_CONFIGURED'
      }, { status: 400 });
    }

    // Verificar se a conta bancária está aprovada
    const bankAccount = doctor.bankAccount as any;
    if (bankAccount.status !== 'APPROVED') {
      return NextResponse.json({
        error: 'Conta bancária ainda não foi aprovada. Aguarde a verificação para solicitar saques.',
        code: 'BANK_ACCOUNT_NOT_APPROVED',
        status: bankAccount.status
      }, { status: 400 });
    }

    const body = await request.json();
    const { amount, bankAccountId, notes } = body;

    // Validações
    if (!amount || amount < 50) {
      return NextResponse.json({
        error: 'Valor mínimo para saque é R$ 50,00'
      }, { status: 400 });
    }

    if (amount > 10000) {
      return NextResponse.json({
        error: 'Valor máximo para saque é R$ 10.000,00'
      }, { status: 400 });
    }

    if (!bankAccountId) {
      return NextResponse.json({
        error: 'Conta bancária é obrigatória'
      }, { status: 400 });
    }

    // Calcular taxa e valor líquido
    const fee = amount * 0.02; // 2% de taxa
    const netAmount = amount - fee;

    // Simular verificação de saldo disponível
    // Em um sistema real, você consultaria o saldo real do médico
    const availableBalance = 5000; // Mock value

    if (amount > availableBalance) {
      return NextResponse.json({
        error: 'Saldo insuficiente para saque'
      }, { status: 400 });
    }

    // Em um sistema real, você salvaria a solicitação de saque no banco de dados
    // Por enquanto, vamos simular uma resposta de sucesso
    const withdrawalRequest = {
      id: `withdrawal-${Date.now()}`,
      doctorId: doctor.id,
      amount,
      netAmount,
      fee,
      bankAccountId,
      notes,
      status: 'pending',
      requestedAt: new Date().toISOString(),
      // Em produção, você salvaria isso no banco:
      // await db.withdrawal.create({
      //   data: {
      //     doctorId: doctor.id,
      //     amount: new Prisma.Decimal(amount),
      //     netAmount: new Prisma.Decimal(netAmount),
      //     fee: new Prisma.Decimal(fee),
      //     bankAccountId,
      //     notes,
      //     status: 'PENDING',
      //   }
      // });
    };

    // Simular integração com gateway de pagamento
    // Em produção, você integraria com Asaas, Stripe, etc.
    console.log('Withdrawal request created:', withdrawalRequest);

    return NextResponse.json({
      success: true,
      withdrawal: withdrawalRequest,
      message: 'Solicitação de saque criada com sucesso',
    });

  } catch (error) {
    console.error('Error processing withdrawal request:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Em um sistema real, você buscaria as solicitações de saque do banco de dados
    // Por enquanto, retornamos dados mock
    const withdrawals = [
      {
        id: 'withdrawal-1',
        amount: 1000,
        netAmount: 980,
        fee: 20,
        status: 'completed',
        requestedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        processedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        bankAccount: {
          bankName: 'Banco do Brasil',
          accountType: 'Conta Corrente',
          accountNumber: '********',
          agency: '1234',
          holderName: 'Dr. João Silva',
        },
        notes: 'Saque mensal',
        transactionId: 'TXN-001',
      },
      {
        id: 'withdrawal-2',
        amount: 500,
        netAmount: 490,
        fee: 10,
        status: 'processing',
        requestedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        bankAccount: {
          bankName: 'Itaú',
          accountType: 'Conta Poupança',
          accountNumber: '********',
          agency: '5678',
          holderName: 'Dr. João Silva',
        },
        notes: 'Saque emergencial',
      },
    ];

    return NextResponse.json({
      withdrawals,
    });

  } catch (error) {
    console.error('Error fetching withdrawal history:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
