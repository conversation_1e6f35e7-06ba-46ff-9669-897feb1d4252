import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

// Esta é uma API de administração para aprovar contas bancárias
// Em produção, isso seria feito através de um painel administrativo
export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Em produção, você verificaria se o usuário é admin
    // Por enquanto, permitimos qualquer usuário para fins de teste
    if (user.role !== 'ADMIN' && user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const body = await request.json();
    const { doctorId, status = 'APPROVED' } = body;

    if (!doctorId) {
      return NextResponse.json({ error: 'ID do médico é obrigatório' }, { status: 400 });
    }

    // Buscar o médico
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      select: { id: true, bankAccount: true, user: { select: { email: true, name: true } } },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    if (!doctor.bankAccount) {
      return NextResponse.json({ error: 'Médico não possui conta bancária configurada' }, { status: 400 });
    }

    // Atualizar status da conta bancária
    const currentBankAccount = doctor.bankAccount as any;
    const updatedBankAccount = {
      ...currentBankAccount,
      status: status,
      updatedAt: new Date().toISOString(),
      approvedAt: status === 'APPROVED' ? new Date().toISOString() : undefined,
      approvedBy: status === 'APPROVED' ? user.id : undefined,
    };

    await db.doctor.update({
      where: { id: doctorId },
      data: {
        bankAccount: updatedBankAccount,
      },
    });

    // Em produção, você enviaria um email de notificação
    console.log(`Bank account ${status.toLowerCase()} for doctor ${doctorId}:`, {
      doctorEmail: doctor.user.email,
      doctorName: doctor.user.name,
      status: status,
      approvedBy: user.id,
    });

    return NextResponse.json({
      success: true,
      message: `Conta bancária ${status === 'APPROVED' ? 'aprovada' : 'rejeitada'} com sucesso`,
      bankAccount: updatedBankAccount,
    });

  } catch (error) {
    console.error('Error updating bank account status:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

// API para listar contas bancárias pendentes (para administradores)
export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Em produção, apenas administradores poderiam acessar
    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') || 'PENDING_VERIFICATION';

    // Buscar médicos com contas bancárias no status especificado
    const doctors = await db.doctor.findMany({
      where: {
        bankAccount: {
          path: ['status'],
          equals: status,
        },
      },
      select: {
        id: true,
        crm: true,
        crmState: true,
        bankAccount: true,
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    const formattedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      crm: doctor.crm,
      crmState: doctor.crmState,
      name: doctor.user.name,
      email: doctor.user.email,
      bankAccount: doctor.bankAccount,
    }));

    return NextResponse.json({
      doctors: formattedDoctors,
      total: formattedDoctors.length,
    });

  } catch (error) {
    console.error('Error fetching pending bank accounts:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
