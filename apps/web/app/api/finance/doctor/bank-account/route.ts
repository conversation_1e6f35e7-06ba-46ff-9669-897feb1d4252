import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { z } from 'zod';

// Schema de validação para dados bancários
const bankAccountSchema = z.object({
  bankName: z.string().min(1, "Nome do banco é obrigatório"),
  bankCode: z.string().min(3, "Código do banco é obrigatório"),
  accountType: z.enum(["CHECKING", "SAVINGS"]),
  agency: z.string().min(1, "Agência é obrigatória"),
  accountNumber: z.string().min(1, "Número da conta é obrigatório"),
  holderName: z.string().min(1, "Nome do titular é obrigatório"),
  holderDocument: z.string().min(11, "CPF/CNPJ é obrigatório"),
  isDefault: z.boolean().default(true)
});

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true, bankAccount: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    const body = await request.json();

    // Validar dados
    const validatedData = bankAccountSchema.parse(body);

    // Preparar dados para salvar
    const bankAccountData = {
      ...validatedData,
      status: 'PENDING_VERIFICATION', // Status inicial
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Atualizar o médico com os dados bancários
    const updatedDoctor = await db.doctor.update({
      where: { id: doctor.id },
      data: {
        bankAccount: bankAccountData,
      },
      select: {
        id: true,
        bankAccount: true,
      },
    });

    // Em um sistema real, você poderia:
    // 1. Enviar dados para verificação via API externa (Asaas, Stripe, etc.)
    // 2. Criar um webhook para receber confirmações
    // 3. Enviar email de confirmação
    // 4. Log da operação para auditoria

    console.log('Bank account configured for doctor:', doctor.id, bankAccountData);

    return NextResponse.json({
      success: true,
      message: 'Conta bancária configurada com sucesso',
      bankAccount: updatedDoctor.bankAccount,
    });

  } catch (error) {
    console.error('Error configuring bank account:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Dados inválidos',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: {
        id: true,
        bankAccount: true,
        user: {
          select: {
            name: true,
            email: true,
          }
        }
      },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Se não há conta bancária configurada
    if (!doctor.bankAccount) {
      return NextResponse.json({
        hasBankAccount: false,
        bankAccount: null,
        status: 'NOT_CONFIGURED'
      });
    }

    // Formatar dados da conta bancária
    const bankAccount = doctor.bankAccount as any;

    return NextResponse.json({
      hasBankAccount: true,
      bankAccount: {
        ...bankAccount,
        // Mascarar dados sensíveis para exibição
        accountNumber: bankAccount.accountNumber && bankAccount.accountNumber.length >= 4 ?
          `****${bankAccount.accountNumber.slice(-4)}` :
          bankAccount.accountNumber || null,
        holderDocument: bankAccount.holderDocument ?
          bankAccount.holderDocument.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4') : null,
      },
      status: bankAccount.status || 'PENDING_VERIFICATION'
    });

  } catch (error) {
    console.error('Error fetching bank account:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'DOCTOR') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Buscar o médico pelo userId
    const doctor = await db.doctor.findFirst({
      where: { userId: user.id },
      select: { id: true, bankAccount: true },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    if (!doctor.bankAccount) {
      return NextResponse.json({ error: 'Conta bancária não configurada' }, { status: 404 });
    }

    const body = await request.json();

    // Validar dados
    const validatedData = bankAccountSchema.parse(body);

    // Preparar dados para atualizar
    const currentBankAccount = doctor.bankAccount as any;
    const updatedBankAccountData = {
      ...currentBankAccount,
      ...validatedData,
      status: 'PENDING_VERIFICATION', // Reset status para nova verificação
      updatedAt: new Date().toISOString(),
    };

    // Atualizar o médico com os novos dados bancários
    const updatedDoctor = await db.doctor.update({
      where: { id: doctor.id },
      data: {
        bankAccount: updatedBankAccountData,
      },
      select: {
        id: true,
        bankAccount: true,
      },
    });

    console.log('Bank account updated for doctor:', doctor.id, updatedBankAccountData);

    return NextResponse.json({
      success: true,
      message: 'Conta bancária atualizada com sucesso',
      bankAccount: updatedDoctor.bankAccount,
    });

  } catch (error) {
    console.error('Error updating bank account:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Dados inválidos',
          details: error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
