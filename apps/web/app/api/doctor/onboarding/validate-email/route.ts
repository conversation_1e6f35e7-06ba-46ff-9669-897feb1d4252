import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

// ============================================================================
// POST /api/doctor/onboarding/validate-email
// ============================================================================

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { email } = body;

    // Validações básicas
    if (!email) {
      return NextResponse.json({
        success: false,
        isUnique: false,
        message: 'Email é obrigatório'
      });
    }

    // Validação de formato de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        success: false,
        isUnique: false,
        message: 'Formato de email inválido'
      });
    }

    // Verifica se email já existe (exceto para o usuário atual)
    const existingUser = await db.user.findFirst({
      where: {
        email: email.toLowerCase(),
        id: { not: user.id }
      }
    });

    const isUnique = !existingUser;

    return NextResponse.json({
      success: true,
      isUnique,
      message: isUnique ? 'Email disponível' : 'Email já cadastrado no sistema'
    });

  } catch (error) {
    console.error('Erro ao validar email:', error);
    return NextResponse.json(
      { 
        success: false, 
        isUnique: false,
        message: 'Erro interno do servidor'
      },
      { status: 500 }
    );
  }
}
