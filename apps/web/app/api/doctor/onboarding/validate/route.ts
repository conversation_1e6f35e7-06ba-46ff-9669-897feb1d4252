import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

// ============================================================================
// POST /api/doctor/onboarding/validate
// ============================================================================

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { stepId, data, validateAll = false } = body;

    if (validateAll) {
      // Valida todos os steps
      const validation = await validateAllSteps(data, user.id);
      return NextResponse.json({
        success: validation.isValid,
        errors: validation.errors,
        message: validation.isValid ? 'Todos os dados são válidos' : 'Alguns dados são inválidos'
      });
    } else {
      // Valida step específico
      const validation = await validateSingleStep(stepId, data, user.id);
      return NextResponse.json({
        success: validation.isValid,
        errors: validation.errors,
        message: validation.isValid ? 'Dados válidos' : 'Dados inválidos'
      });
    }

  } catch (error) {
    console.error('Erro na validação:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Erro interno do servidor',
        errors: { general: ['Erro na validação'] }
      },
      { status: 500 }
    );
  }
}

// ============================================================================
// FUNÇÕES DE VALIDAÇÃO
// ============================================================================

async function validateSingleStep(stepId: string, data: any, userId: string) {
  const errors: Record<string, string[]> = {};

  switch (stepId) {
    case 'personal':
      await validatePersonalData(data, errors, userId);
      break;
    case 'professional':
      await validateProfessionalData(data, errors, userId);
      break;
    case 'specialties':
      await validateSpecialtyData(data, errors);
      break;
    case 'schedule':
      validateScheduleData(data, errors);
      break;
    default:
      // Welcome e Review não precisam validação específica
      break;
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

async function validateAllSteps(data: any, userId: string) {
  const allErrors: Record<string, Record<string, string[]>> = {};
  let isValid = true;

  // Valida cada step que tem dados
  for (const [stepId, stepData] of Object.entries(data)) {
    if (stepData) {
      const validation = await validateSingleStep(stepId, stepData, userId);
      if (!validation.isValid) {
        allErrors[stepId] = validation.errors;
        isValid = false;
      }
    }
  }

  return { isValid, errors: allErrors };
}

// ============================================================================
// VALIDAÇÕES ESPECÍFICAS
// ============================================================================

async function validatePersonalData(data: any, errors: Record<string, string[]>, userId: string) {
  // Validações básicas
  if (!data.firstName || data.firstName.length < 2) {
    addError(errors, 'firstName', 'Nome deve ter pelo menos 2 caracteres');
  }

  if (!data.lastName || data.lastName.length < 2) {
    addError(errors, 'lastName', 'Sobrenome deve ter pelo menos 2 caracteres');
  }

  // Email não é obrigatório no onboarding pois o usuário já está logado
  if (data.email && data.email.trim() !== '') {
    if (!isValidEmail(data.email)) {
      addError(errors, 'email', 'Email inválido');
    } else {
      // Verifica se email já existe (exceto para o usuário atual)
      const existingUser = await db.user.findFirst({
        where: {
          email: data.email,
          id: { not: userId }
        }
      });
      
      if (existingUser) {
        addError(errors, 'email', 'Este email já está cadastrado');
      }
    }
  }

  if (!data.phone || data.phone.length < 10) {
    addError(errors, 'phone', 'Telefone deve ter pelo menos 10 dígitos');
  }

  if (!data.cpf || !isValidCpf(data.cpf)) {
    addError(errors, 'cpf', 'CPF inválido');
  } else {
    // Verifica se CPF já existe
    const existingDoctor = await db.doctor.findFirst({
      where: {
        cpf: data.cpf,
        userId: { not: userId }
      }
    });
    
    if (existingDoctor) {
      addError(errors, 'cpf', 'Este CPF já está cadastrado');
    }
  }

  if (!data.birthDate) {
    addError(errors, 'birthDate', 'Data de nascimento é obrigatória');
  } else {
    const birthDate = new Date(data.birthDate);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (age < 18 || age > 100) {
      addError(errors, 'birthDate', 'Idade deve estar entre 18 e 100 anos');
    }
  }

  // Validações de endereço
  if (data.address) {
    if (!data.address.street || data.address.street.length < 5) {
      addError(errors, 'address.street', 'Endereço deve ter pelo menos 5 caracteres');
    }

    if (!data.address.number) {
      addError(errors, 'address.number', 'Número é obrigatório');
    }

    if (!data.address.neighborhood || data.address.neighborhood.length < 2) {
      addError(errors, 'address.neighborhood', 'Bairro é obrigatório');
    }

    if (!data.address.city || data.address.city.length < 2) {
      addError(errors, 'address.city', 'Cidade é obrigatória');
    }

    if (!data.address.state || data.address.state.length !== 2) {
      addError(errors, 'address.state', 'Estado deve ter 2 caracteres');
    }

    if (!data.address.zipCode || data.address.zipCode.length !== 8) {
      addError(errors, 'address.zipCode', 'CEP deve ter 8 dígitos');
    }
  }
}

async function validateProfessionalData(data: any, errors: Record<string, string[]>, userId: string) {
  if (!data.crm || data.crm.length < 4) {
    addError(errors, 'crm', 'CRM deve ter pelo menos 4 caracteres');
  } else {
    // Verifica se CRM já existe
    const existingDoctor = await db.doctor.findFirst({
      where: {
        crm: data.crm,
        crmState: data.crmState,
        userId: { not: userId }
      }
    });
    
    if (existingDoctor) {
      addError(errors, 'crm', 'Este CRM já está cadastrado');
    }
  }

  if (!data.crmState || data.crmState.length !== 2) {
    addError(errors, 'crmState', 'Estado do CRM deve ter 2 caracteres');
  }

  if (!data.graduationYear || data.graduationYear < 1950 || data.graduationYear > new Date().getFullYear()) {
    addError(errors, 'graduationYear', 'Ano de formatura inválido');
  }

  if (!data.medicalSchool || data.medicalSchool.length < 5) {
    addError(errors, 'medicalSchool', 'Faculdade de medicina é obrigatória');
  }

  if (data.bio && data.bio.length > 500) {
    addError(errors, 'bio', 'Biografia deve ter no máximo 500 caracteres');
  }

  // Validações de residência
  if (data.residencyInfo?.hasResidency) {
    if (!data.residencyInfo.residencyHospital) {
      addError(errors, 'residencyInfo.residencyHospital', 'Hospital da residência é obrigatório');
    }

    if (!data.residencyInfo.residencySpecialty) {
      addError(errors, 'residencyInfo.residencySpecialty', 'Especialidade da residência é obrigatória');
    }

    if (data.residencyInfo.residencyYear && 
        (data.residencyInfo.residencyYear < 1950 || data.residencyInfo.residencyYear > new Date().getFullYear())) {
      addError(errors, 'residencyInfo.residencyYear', 'Ano de conclusão da residência inválido');
    }
  }
}

async function validateSpecialtyData(data: any, errors: Record<string, string[]>) {
  if (!data.primarySpecialtyId) {
    addError(errors, 'primarySpecialtyId', 'Especialidade principal é obrigatória');
  } else {
    // Verifica se a especialidade existe
    const specialty = await db.specialty.findUnique({
      where: { id: data.primarySpecialtyId }
    });
    
    if (!specialty) {
      addError(errors, 'primarySpecialtyId', 'Especialidade principal não encontrada');
    }
  }

  if (data.secondarySpecialtyIds && data.secondarySpecialtyIds.length > 3) {
    addError(errors, 'secondarySpecialtyIds', 'Máximo 3 especialidades secundárias');
  }

  // Verifica se especialidades secundárias existem
  if (data.secondarySpecialtyIds && data.secondarySpecialtyIds.length > 0) {
    for (const specialtyId of data.secondarySpecialtyIds) {
      const specialty = await db.specialty.findUnique({
        where: { id: specialtyId }
      });
      
      if (!specialty) {
        addError(errors, 'secondarySpecialtyIds', `Especialidade ${specialtyId} não encontrada`);
      }
    }
  }

  // Validações de certificações
  if (data.certifications && data.certifications.length > 0) {
    data.certifications.forEach((cert: any, index: number) => {
      if (!cert.name) {
        addError(errors, `certifications.${index}.name`, 'Nome da certificação é obrigatório');
      }

      if (!cert.issuingOrganization) {
        addError(errors, `certifications.${index}.issuingOrganization`, 'Organização emissora é obrigatória');
      }

      if (!cert.issueDate) {
        addError(errors, `certifications.${index}.issueDate`, 'Data de emissão é obrigatória');
      }
    });
  }
}

function validateScheduleData(data: any, errors: Record<string, string[]>) {
  if (!data.workingHours || data.workingHours.length === 0) {
    // Horários são opcionais, então não é erro
    return;
  }

  data.workingHours.forEach((schedule: any, index: number) => {
    if (schedule.dayOfWeek < 0 || schedule.dayOfWeek > 6) {
      addError(errors, `workingHours.${index}.dayOfWeek`, 'Dia da semana inválido');
    }

    if (!isValidTime(schedule.startTime)) {
      addError(errors, `workingHours.${index}.startTime`, 'Horário de início inválido');
    }

    if (!isValidTime(schedule.endTime)) {
      addError(errors, `workingHours.${index}.endTime`, 'Horário de fim inválido');
    }

    if (schedule.startTime && schedule.endTime) {
      const start = new Date(`2000-01-01T${schedule.startTime}:00`);
      const end = new Date(`2000-01-01T${schedule.endTime}:00`);
      
      if (start >= end) {
        addError(errors, `workingHours.${index}`, 'Horário de início deve ser antes do fim');
      }

      const diffHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      if (diffHours > 12) {
        addError(errors, `workingHours.${index}`, 'Jornada não pode exceder 12 horas');
      }
    }
  });

  if (data.consultationDuration && (data.consultationDuration < 15 || data.consultationDuration > 120)) {
    addError(errors, 'consultationDuration', 'Duração da consulta deve estar entre 15 e 120 minutos');
  }

  if (data.breakBetweenConsultations && (data.breakBetweenConsultations < 0 || data.breakBetweenConsultations > 60)) {
    addError(errors, 'breakBetweenConsultations', 'Intervalo entre consultas deve estar entre 0 e 60 minutos');
  }
}

// ============================================================================
// FUNÇÕES AUXILIARES
// ============================================================================

function addError(errors: Record<string, string[]>, field: string, message: string) {
  if (!errors[field]) {
    errors[field] = [];
  }
  errors[field].push(message);
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function isValidCpf(cpf: string): boolean {
  const cleanCpf = cpf.replace(/\D/g, '');
  
  if (cleanCpf.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(cleanCpf)) return false;
  
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCpf.charAt(9))) return false;
  
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCpf.charAt(10))) return false;
  
  return true;
}

function isValidTime(time: string): boolean {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
}
