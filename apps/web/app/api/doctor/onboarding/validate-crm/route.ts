import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

// ============================================================================
// POST /api/doctor/onboarding/validate-crm
// ============================================================================

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();
    
    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Não autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { crm, crmState } = body;

    // Validações básicas
    if (!crm || !crmState) {
      return NextResponse.json({
        success: false,
        isUnique: false,
        message: 'CRM e estado são obrigatórios'
      });
    }

    if (crm.length < 4) {
      return NextResponse.json({
        success: false,
        isUnique: false,
        message: 'CRM deve ter pelo menos 4 caracteres'
      });
    }

    if (crmState.length !== 2) {
      return NextResponse.json({
        success: false,
        isUnique: false,
        message: 'Estado deve ter 2 caracteres'
      });
    }

    // Verifica se CRM já existe (exceto para o usuário atual)
    const existingDoctor = await db.doctor.findFirst({
      where: {
        crm: crm.toUpperCase(),
        crmState: crmState.toUpperCase(),
        userId: { not: user.id }
      }
    });

    const isUnique = !existingDoctor;

    return NextResponse.json({
      success: true,
      isUnique,
      message: isUnique ? 'CRM disponível' : 'CRM já cadastrado no sistema'
    });

  } catch (error) {
    console.error('Erro ao validar CRM:', error);
    return NextResponse.json(
      { 
        success: false, 
        isUnique: false,
        message: 'Erro interno do servidor'
      },
      { status: 500 }
    );
  }
}
