import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Iniciando finalização do onboarding...');

    const { user } = await currentUser();
    console.log('👤 Usuário encontrado:', user ? 'Sim' : 'Não', user?.id);

    if (!user) {
      console.log('❌ Usuário não autenticado');
      return NextResponse.json(
        { success: false, message: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { data, finalValidation = true } = body;
    console.log('📦 Dados recebidos:', { data, finalValidation });

    // Busca o perfil do médico
    console.log('🔍 Buscando perfil do médico...');
    const doctor = await db.doctor.findUnique({
      where: { userId: user.id },
      include: {
        specialties: true,
        doctorSchedules: true
      }
    });
    console.log('👨‍⚕️ Médico encontrado:', doctor ? 'Sim' : 'N<PERSON>', doctor?.id);

    if (!doctor) {
      return NextResponse.json(
        { success: false, message: 'Perfil de médico não encontrado' },
        { status: 404 }
      );
    }

    // Valida se todos os dados obrigatórios estão preenchidos
    if (finalValidation) {
      console.log('🔍 Validando dados obrigatórios...');
      console.log('📋 CRM:', doctor.crm);
      console.log('📋 CRM State:', doctor.crmState);
      console.log('📋 Especialidades:', doctor.specialties.length);
      console.log('📋 Horários:', doctor.doctorSchedules.length);

      if (!doctor.crm || !doctor.crmState) {
        console.log('❌ Dados profissionais incompletos');
        return NextResponse.json(
          { success: false, message: 'Dados profissionais incompletos' },
          { status: 400 }
        );
      }

      if (doctor.specialties.length === 0) {
        console.log('❌ Especialidades não selecionadas');
        return NextResponse.json(
          { success: false, message: 'Especialidades não selecionadas' },
          { status: 400 }
        );
      }

      console.log('✅ Validação passou');
    }

    // Marca o onboarding como completo
    console.log('✅ Marcando onboarding como completo...');
    await db.user.update({
      where: { id: user.id },
      data: {
        onboardingComplete: true
      }
    });
    console.log('✅ Onboarding marcado como completo');

    // Atualiza o status do médico para aprovado
    console.log('🔄 Atualizando status do médico para aprovado...');
    await db.doctor.update({
      where: { id: doctor.id },
      data: {
        documentStatus: 'APPROVED',
        onlineStatus: 'OFFLINE'
      }
    });
    console.log('✅ Status do médico atualizado para aprovado');

    return NextResponse.json({
      success: true,
      data: {
        doctorId: doctor.id,
        profileUrl: `/app/dashboard/doctor`
      }
    });

  } catch (error) {
    console.error('❌ Erro ao finalizar onboarding:', error);
    console.error('❌ Stack trace:', error instanceof Error ? error.stack : 'N/A');
    console.error('❌ Error name:', error instanceof Error ? error.name : 'N/A');
    console.error('❌ Error message:', error instanceof Error ? error.message : 'N/A');

    return NextResponse.json(
      {
        success: false,
        message: 'Erro interno do servidor',
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
