import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Busca o progresso do onboarding do médico
    const doctor = await db.doctor.findUnique({
      where: { userId: user.id },
      select: {
        id: true,
        crm: true,
        crmState: true,
        specialties: { select: { id: true } },
        workingHours: true
      }
    });

    // Determina o step atual baseado nos dados existentes
    let currentStep = 'welcome';
    const completedSteps = [];

    if (doctor) {
      if (doctor.crm && doctor.crmState) {
        completedSteps.push('personal', 'professional');
        currentStep = 'specialties';
      }

      if (doctor.specialties.length > 0) {
        completedSteps.push('specialties');
        currentStep = 'schedule';
      }

      if (doctor.workingHours && doctor.workingHours.length > 0) {
        completedSteps.push('schedule');
        currentStep = 'review';
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        currentStep,
        completedSteps,
        totalSteps: 6,
        completionPercentage: Math.round((completedSteps.length / 6) * 100),
        estimatedTimeRemaining: 5,
        canContinue: true,
        canGoBack: false
      }
    });

  } catch (error) {
    console.error('Erro ao buscar progresso do onboarding:', error);
    return NextResponse.json(
      { success: false, message: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
