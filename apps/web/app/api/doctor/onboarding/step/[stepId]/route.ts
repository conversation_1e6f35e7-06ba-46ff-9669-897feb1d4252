import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';
import { z } from 'zod';

// Schemas de validação
const PersonalDataSchema = z.object({
  firstName: z.string().optional().default(''),
  lastName: z.string().optional().default(''),
  email: z.string().optional().default(''), // Removido validação de email pois usuário já está logado
  phone: z.string().optional().default(''),
  cpf: z.string().length(11, 'CPF deve ter 11 dígitos'),
  birthDate: z.string(),
  gender: z.enum(['male', 'female', 'other']),
  address: z.object({
    street: z.string().min(5, 'Endereço deve ter pelo menos 5 caracteres'),
    number: z.string().min(1, 'Número é obrigatório'),
    complement: z.string().optional(),
    neighborhood: z.string().min(2, 'Bairro é obrigatório'),
    city: z.string().min(2, 'Cidade é obrigatória'),
    state: z.string().length(2, 'Estado deve ter 2 caracteres'),
    zipCode: z.string().length(8, 'CEP deve ter 8 dígitos')
  })
});

const ProfessionalDataSchema = z.object({
  crm: z.string().min(4, 'CRM deve ter pelo menos 4 caracteres'),
  crmState: z.string().length(2, 'Estado do CRM deve ter 2 caracteres'),
  medicalLicense: z.string().optional(),
  graduationYear: z.number().min(1950).max(new Date().getFullYear()),
  medicalSchool: z.string().min(5, 'Faculdade de medicina é obrigatória'),
  residencyInfo: z.object({
    hasResidency: z.boolean(),
    residencyHospital: z.string().optional(),
    residencyYear: z.number().optional(),
    residencySpecialty: z.string().optional()
  }).optional(),
  hospitalId: z.string().optional(),
  bio: z.string().max(500, 'Biografia deve ter no máximo 500 caracteres').optional()
});

const SpecialtyDataSchema = z.object({
  primarySpecialtyId: z.string().min(1, 'Especialidade principal é obrigatória'),
  secondarySpecialtyIds: z.array(z.string()).max(3, 'Máximo 3 especialidades secundárias'),
  certifications: z.array(z.object({
    name: z.string(),
    issuingOrganization: z.string(),
    issueDate: z.string(),
    expiryDate: z.string().optional()
  })).optional()
});

const ScheduleDataSchema = z.object({
  workingHours: z.array(z.object({
    dayOfWeek: z.number().min(0).max(6),
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Formato de hora inválido'),
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Formato de hora inválido'),
    isActive: z.boolean()
  })),
  consultationDuration: z.number().min(15).max(120).default(30),
  breakBetweenConsultations: z.number().min(0).max(60).default(5),
  lunchBreak: z.object({
    startTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    endTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    isActive: z.boolean()
  }).optional()
});

const stepValidators = {
  personal: PersonalDataSchema,
  professional: ProfessionalDataSchema,
  specialties: SpecialtyDataSchema,
  schedule: ScheduleDataSchema
};

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ stepId: string }> }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    const { stepId } = await params;
    const body = await request.json();
    const { data, validateOnly = false } = body;

    // Valida os dados se necessário
    const validator = stepValidators[stepId as keyof typeof stepValidators];
    if (validator) {
      try {
        // Para o step personal, garante que o email seja preenchido com o email do usuário se não fornecido
        if (stepId === 'personal' && (!data.email || data.email.trim() === '')) {
          data.email = user.email || '';
        }
        
        validator.parse(data);
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errors: Record<string, string[]> = {};
          error.errors.forEach((err) => {
            const path = err.path.join('.');
            if (!errors[path]) {
              errors[path] = [];
            }
            errors[path].push(err.message);
          });

          return NextResponse.json({
            success: false,
            message: 'Dados inválidos',
            errors
          }, { status: 400 });
        }
      }
    }

    if (validateOnly) {
      return NextResponse.json({ success: true });
    }

    // Busca ou cria o perfil do médico
    let doctor = await db.doctor.findUnique({
      where: { userId: user.id }
    });

    if (!doctor) {
      doctor = await db.doctor.create({
        data: {
          userId: user.id,
          crm: '',
          crmState: ''
        }
      });
    }

    // Atualiza os dados baseado no step
    switch (stepId) {
      case 'personal':
        // Usa dados existentes do usuário como fallback
        const personalData = {
          name: data.firstName && data.lastName
            ? `${data.firstName} ${data.lastName}`
            : user.name || 'Dr. Usuário',
          email: data.email || user.email || '',
          phone: data.phone || user.phone || ''
        };

        // Log de fallbacks aplicados
        if (!data.firstName || !data.lastName) {
          console.log('⚠️ Nome não informado, usando fallback:', personalData.name);
        }
        if (!data.email) {
          console.log('⚠️ Email não informado, usando fallback:', personalData.email);
        }
        if (!data.phone) {
          console.log('⚠️ Telefone não informado, usando fallback:', personalData.phone);
        }

        await db.user.update({
          where: { id: user.id },
          data: personalData
        });
        break;

      case 'professional':
        // Dados profissionais com fallbacks
        const professionalData = {
          crm: data.crm || '0000000', // Fallback para CRM
          crmState: data.crmState || 'XX', // Fallback para estado
          medicalLicense: data.medicalLicense,
          graduationYear: data.graduationYear,
          medicalSchool: data.medicalSchool,
          bio: data.bio || 'Médico cadastrado via ZapVida' // Fallback para biografia
        };

        // Log de fallbacks aplicados
        if (!data.crm) console.log('⚠️ CRM não informado, usando fallback: 0000000');
        if (!data.crmState) console.log('⚠️ Estado CRM não informado, usando fallback: XX');
        if (!data.bio) console.log('⚠️ Biografia não informada, usando fallback padrão');

        await db.doctor.update({
          where: { id: doctor.id },
          data: professionalData
        });
        break;

      case 'specialties':
        // Atualiza as especialidades do médico com fallback
        let specialtyIds = [data.primarySpecialtyId, ...(data.secondarySpecialtyIds || [])].filter(Boolean);

        // Fallback: se não há especialidades selecionadas, usa "Clínico Geral"
        if (specialtyIds.length === 0) {
          console.log('⚠️ Nenhuma especialidade selecionada, usando Clínico Geral como padrão');

          // Busca ou cria especialidade "Clínico Geral"
          let clinicoGeral = await db.specialty.findFirst({
            where: { name: 'Clínico Geral' }
          });

          if (!clinicoGeral) {
            clinicoGeral = await db.specialty.create({
              data: {
                name: 'Clínico Geral',
                description: 'Medicina geral e clínica médica'
              }
            });
          }

          specialtyIds = [clinicoGeral.id];
        }

        await db.doctor.update({
          where: { id: doctor.id },
          data: {
            specialties: {
              set: specialtyIds.map(id => ({ id }))
            }
          }
        });
        break;

      case 'schedule':
        // Remove horários existentes
        await db.doctorSchedule.deleteMany({
          where: { doctorId: doctor.id }
        });

        // Adiciona novos horários com fallback
        let workingHours = data.workingHours || [];

        // Fallback: se não há horários configurados, usa horário padrão (Seg-Sex 8h-17h)
        if (workingHours.length === 0) {
          console.log('⚠️ Nenhum horário configurado, usando horário padrão (Seg-Sex 8h-17h)');

          workingHours = [
            { dayOfWeek: 1, startTime: '08:00', endTime: '17:00', isActive: true }, // Segunda
            { dayOfWeek: 2, startTime: '08:00', endTime: '17:00', isActive: true }, // Terça
            { dayOfWeek: 3, startTime: '08:00', endTime: '17:00', isActive: true }, // Quarta
            { dayOfWeek: 4, startTime: '08:00', endTime: '17:00', isActive: true }, // Quinta
            { dayOfWeek: 5, startTime: '08:00', endTime: '17:00', isActive: true }, // Sexta
          ];
        }

        // Adiciona os horários
        await db.doctorSchedule.createMany({
          data: workingHours.map((hour: any) => ({
            doctorId: doctor.id,
            weekDay: hour.dayOfWeek,
            startTime: hour.startTime,
            endTime: hour.endTime,
            isEnabled: hour.isActive
          }))
        });
        break;
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Erro ao salvar step:', error);
    return NextResponse.json(
      { success: false, message: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
