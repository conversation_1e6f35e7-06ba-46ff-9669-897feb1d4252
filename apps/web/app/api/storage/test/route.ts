import { NextRequest, NextResponse } from "next/server";
import { currentUser } from "@saas/auth/lib/current-user";
import { checkStorageConfig } from "../../../../lib/storage-config-check";
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    // Only allow admins and doctors to test storage
    if (user.role !== "DOCTOR" && user.role !== "ADMIN") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    // Check storage configuration
    const storageCheck = checkStorageConfig();

    // Test Supabase connection
    let supabaseTest = null;
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

      if (supabaseUrl && supabaseServiceKey) {
        const supabase = createClient(supabaseUrl, supabaseServiceKey, {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        });

        // Test bucket access
        const bucketName = process.env.NEXT_PUBLIC_PRESCRIPTIONS_BUCKET_NAME || 'prescriptions';
        const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
        
        if (bucketsError) {
          supabaseTest = {
            success: false,
            error: bucketsError.message,
            buckets: null
          };
        } else {
          const prescriptionsBucket = buckets?.find(b => b.name === bucketName);
          supabaseTest = {
            success: true,
            buckets: buckets?.map(b => b.name),
            prescriptionsBucketExists: !!prescriptionsBucket,
            prescriptionsBucketName: bucketName
          };
        }
      }
    } catch (error) {
      supabaseTest = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    return NextResponse.json({
      storageConfig: storageCheck,
      supabaseTest,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Erro ao testar storage:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
