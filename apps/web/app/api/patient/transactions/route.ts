import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();
    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar paciente pelo userId
    const patient = await db.patient.findFirst({
      where: { userId: user.id },
      select: { id: true },
    });

    if (!patient) {
      return NextResponse.json({ transactions: [] });
    }

    // Filtros básicos opcionais
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const search = searchParams.get('search');

    // Montar where para transações do paciente: por appointment.patientId ou patientSubscription.patientId
    const where: any = {
      OR: [
        { appointment: { patientId: patient.id } },
        { patientSubscription: { patientId: patient.id } },
      ],
    };

    if (status) where.status = status as any;
    if (type) where.paymentMethod = type as any; // Nota: type aqui é método/ajuste conforme front

    const transactions = await db.transaction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        amount: true,
        status: true,
        paymentMethod: true,
        dueDate: true,
        paidAt: true,
        createdAt: true,
        appointment: {
          select: {
            id: true,
            scheduledAt: true,
            doctor: {
              select: {
                specialties: { select: { name: true } },
                user: { select: { name: true } },
              },
            },
          },
        },
        patientSubscription: {
          select: {
            id: true,
            planName: true,
          },
        },
        invoice: { select: { url: true } },
      },
      take: 100,
    });

    // Mapear para o formato de exibição do front
    const mapped = transactions.map((t) => {
      const isSubscription = Boolean(t.patientSubscription);
      const description = isSubscription
        ? `Assinatura ${t.patientSubscription?.planName ?? ''}`.trim()
        : t.appointment?.doctor?.user?.name
          ? `Consulta ${t.appointment.doctor.user.name}`
          : 'Transação';

      const type = isSubscription ? 'SUBSCRIPTION' : 'CONSULTATION';

      return {
        id: t.id,
        description,
        amount: Number(t.amount),
        status: t.status,
        date: (t.paidAt ?? t.createdAt).toISOString(),
        dueDate: t.dueDate?.toISOString(),
        method: String(t.paymentMethod),
        invoiceUrl: t.invoice?.url ?? undefined,
        type,
      };
    });

    // Resumo simples
    const totalPaid = mapped.filter(m => m.status === 'PAID').reduce((acc, m) => acc + m.amount, 0);
    const totalPending = mapped.filter(m => m.status === 'PENDING').reduce((acc, m) => acc + m.amount, 0);
    const lastPayment = mapped.find(m => m.status === 'PAID')?.date ?? null;
    const monthlyAverage = mapped.length > 0 ? Number((totalPaid / Math.max(1, new Set(mapped.map(m => new Date(m.date).getMonth() + '-' + new Date(m.date).getFullYear())).size)).toFixed(2)) : 0;

    return NextResponse.json({
      transactions: mapped,
      summary: {
        totalPaid,
        totalPending,
        monthlyAverage,
        lastPayment,
      },
    });
  } catch (error) {
    console.error('Erro ao buscar transações do paciente:', error);
    return NextResponse.json({ transactions: [], summary: null }, { status: 500 });
  }
}


