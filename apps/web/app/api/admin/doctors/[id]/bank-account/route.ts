import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const { id } = params;
    const body = await request.json();
    const { action, reason } = body;

    // Check if doctor exists
    const doctor = await db.doctor.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          }
        }
      }
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    let updatedBankAccount = null;

    switch (action) {
      case 'approve':
        if (!doctor.bankAccount) {
          return NextResponse.json({ error: 'Médico não possui conta bancária configurada' }, { status: 400 });
        }

        updatedBankAccount = {
          ...doctor.bankAccount,
          status: 'APPROVED',
          approvedAt: new Date().toISOString(),
          approvedBy: user.id,
          updatedAt: new Date().toISOString(),
        };
        break;

      case 'reject':
        if (!doctor.bankAccount) {
          return NextResponse.json({ error: 'Médico não possui conta bancária configurada' }, { status: 400 });
        }

        if (!reason) {
          return NextResponse.json({ error: 'Motivo da rejeição é obrigatório' }, { status: 400 });
        }

        updatedBankAccount = {
          ...doctor.bankAccount,
          status: 'REJECTED',
          rejectionReason: reason,
          rejectedAt: new Date().toISOString(),
          rejectedBy: user.id,
          updatedAt: new Date().toISOString(),
        };
        break;

      case 'reset':
        updatedBankAccount = null;
        break;

      default:
        return NextResponse.json({ error: 'Ação inválida' }, { status: 400 });
    }

    // Update doctor's bank account
    const updatedDoctor = await db.doctor.update({
      where: { id },
      data: {
        bankAccount: updatedBankAccount,
        updatedAt: new Date(),
      }
    });

    // Log the action
    console.log(`Bank account ${action} for doctor ${doctor.user.name} (${doctor.user.email}) by admin ${user.email}. Reason: ${reason || 'N/A'}`);

    // In a real application, you would:
    // 1. Send email notification to the doctor
    // 2. Log the action in an audit table
    // 3. Update any related payment systems

    return NextResponse.json({
      success: true,
      doctor: updatedDoctor,
      message: `Conta bancária ${action === 'approve' ? 'aprovada' : action === 'reject' ? 'rejeitada' : 'resetada'} com sucesso`
    });

  } catch (error) {
    console.error('Error managing bank account:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
