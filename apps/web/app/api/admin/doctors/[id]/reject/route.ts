import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const doctorId = params.id;
    const body = await request.json();
    const { reason } = body;

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json({ error: 'Motivo da rejeição é obrigatório' }, { status: 400 });
    }

    // Buscar o médico
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Verificar se já está rejeitado
    if (doctor.documentStatus === 'REJECTED') {
      return NextResponse.json({ error: 'Médico já está rejeitado' }, { status: 400 });
    }

    // Rejeitar o médico
    const updatedDoctor = await db.doctor.update({
      where: { id: doctorId },
      data: {
        documentStatus: 'REJECTED',
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    // Em produção, você enviaria um email de notificação com o motivo
    console.log(`Doctor rejected: ${doctor.user.name} (${doctor.user.email}) - Reason: ${reason}`);

    return NextResponse.json({
      success: true,
      message: 'Médico rejeitado com sucesso',
      doctor: updatedDoctor,
    });

  } catch (error) {
    console.error('Error rejecting doctor:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
