import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const doctorId = params.id;

    // Buscar o médico
    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!doctor) {
      return NextResponse.json({ error: 'Médico não encontrado' }, { status: 404 });
    }

    // Verificar se já está aprovado
    if (doctor.documentStatus === 'APPROVED') {
      return NextResponse.json({ error: 'Médico já está aprovado' }, { status: 400 });
    }

    // Aprovar o médico
    const updatedDoctor = await db.doctor.update({
      where: { id: doctorId },
      data: {
        documentStatus: 'APPROVED',
      },
      include: {
        user: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    // Em produção, você enviaria um email de notificação
    console.log(`Doctor approved: ${doctor.user.name} (${doctor.user.email})`);

    return NextResponse.json({
      success: true,
      message: 'Médico aprovado com sucesso',
      doctor: updatedDoctor,
    });

  } catch (error) {
    console.error('Error approving doctor:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
