import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const specialty = searchParams.get('specialty') || 'all';
    const bankAccount = searchParams.get('bankAccount') || 'all';

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { crm: { contains: search } },
        { specialties: { some: { name: { contains: search, mode: 'insensitive' } } } }
      ];
    }

    if (status !== 'all') {
      where.documentStatus = status;
    }

    if (specialty !== 'all') {
      where.specialties = { some: { id: specialty } };
    }

    if (bankAccount === 'with') {
      where.bankAccount = { not: null };
    } else if (bankAccount === 'without') {
      where.bankAccount = null;
    } else if (bankAccount !== 'all') {
      where.bankAccount = { path: ['status'], equals: bankAccount };
    }

    // Fetch doctors with related data
    const doctors = await db.doctor.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            image: true,
            emailVerified: true,
          }
        },
        specialties: {
          select: {
            id: true,
            name: true,
          }
        },
        appointments: {
          select: {
            id: true,
            status: true,
            scheduledAt: true,
          }
        },
        transactions: {
          select: {
            id: true,
            amount: true,
            status: true,
            createdAt: true,
          }
        },
        evaluations: {
          select: {
            id: true,
            rating: true,
            comment: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Calculate metrics
    const totalDoctors = await db.doctor.count();
    const pendingDoctors = await db.doctor.count({ where: { documentStatus: 'PENDING' } });
    const approvedDoctors = await db.doctor.count({ where: { documentStatus: 'APPROVED' } });
    const rejectedDoctors = await db.doctor.count({ where: { documentStatus: 'REJECTED' } });
    const onlineDoctors = await db.doctor.count({ where: { onlineStatus: 'ONLINE' } });
    const offlineDoctors = await db.doctor.count({ where: { onlineStatus: 'OFFLINE' } });
    const withBankAccount = await db.doctor.count({ where: { bankAccount: { not: null } } });
    const withoutBankAccount = await db.doctor.count({ where: { bankAccount: null } });

    // Calculate total revenue
    const revenueResult = await db.transaction.aggregate({
      where: { status: 'PAID' },
      _sum: { amount: true }
    });
    const totalRevenue = Number(revenueResult._sum.amount || 0);

    // Calculate average rating
    const ratingResult = await db.doctor.aggregate({
      _avg: { rating: true }
    });
    const averageRating = Number(ratingResult._avg.rating || 0);

    // Calculate total appointments
    const totalAppointments = await db.appointment.count();
    const completedAppointments = await db.appointment.count({ where: { status: 'COMPLETED' } });

    // Get doctors by specialty
    const doctorsBySpecialty = await db.specialty.findMany({
      include: {
        _count: {
          select: { doctors: true }
        }
      }
    });

    // Get doctors by status
    const doctorsByStatus = await db.doctor.groupBy({
      by: ['documentStatus'],
      _count: { id: true }
    });

    // Get top performers
    const topPerformers = await db.doctor.findMany({
      include: {
        user: {
          select: {
            name: true,
          }
        },
        transactions: {
          where: { status: 'PAID' },
          select: {
            amount: true,
          }
        },
        appointments: {
          where: { status: 'COMPLETED' },
          select: {
            id: true,
          }
        }
      },
      orderBy: { rating: 'desc' },
      take: 10
    });

    // Process top performers
    const processedTopPerformers = topPerformers.map(doctor => ({
      id: doctor.id,
      name: doctor.user.name || 'Médico',
      revenue: doctor.transactions.reduce((sum, t) => sum + Number(t.amount), 0),
      appointments: doctor.appointments.length,
      rating: Number(doctor.rating || 0)
    })).sort((a, b) => b.revenue - a.revenue);

    // Get recent activity (simplified)
    const recentActivity = await db.doctor.findMany({
      include: {
        user: {
          select: {
            name: true,
          }
        }
      },
      orderBy: { updatedAt: 'desc' },
      take: 10
    });

    const processedRecentActivity = recentActivity.map(doctor => ({
      id: doctor.id,
      type: 'doctor_update',
      description: `Médico ${doctor.user.name} atualizado`,
      timestamp: doctor.updatedAt.toISOString(),
      doctorName: doctor.user.name || 'Médico'
    }));

    const metrics = {
      total: totalDoctors,
      pending: pendingDoctors,
      approved: approvedDoctors,
      rejected: rejectedDoctors,
      online: onlineDoctors,
      offline: offlineDoctors,
      withBankAccount,
      withoutBankAccount,
      totalRevenue,
      averageRating,
      totalAppointments,
      completedAppointments,
      bySpecialty: doctorsBySpecialty.map(s => ({
        specialty: s.name,
        count: s._count.doctors
      })),
      byStatus: doctorsByStatus.map(s => ({
        status: s.documentStatus,
        count: s._count.id
      })),
      topPerformers: processedTopPerformers,
      recentActivity: processedRecentActivity
    };

    return NextResponse.json({
      doctors,
      metrics
    });

  } catch (error) {
    console.error('Error fetching doctors data:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
