import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const body = await request.json();
    const { action, subscriptionIds } = body;

    if (!action || !subscriptionIds || !Array.isArray(subscriptionIds)) {
      return NextResponse.json(
        { error: 'Ação e IDs das assinaturas são obrigatórios' },
        { status: 400 }
      );
    }

    if (subscriptionIds.length === 0) {
      return NextResponse.json(
        { error: 'Pelo menos uma assinatura deve ser selecionada' },
        { status: 400 }
      );
    }

    const now = new Date();
    let updateData: any = {};
    let validActions = ['pause', 'reactivate', 'cancel'];

    if (!validActions.includes(action)) {
      return NextResponse.json(
        { error: 'Ação inválida' },
        { status: 400 }
      );
    }

    // Definir dados de atualização baseado na ação
    switch (action) {
      case 'pause':
        updateData = {
          status: 'PAUSED',
          pausedAt: now
        };
        break;
      case 'reactivate':
        updateData = {
          status: 'ACTIVE',
          pausedAt: null
        };
        break;
      case 'cancel':
        updateData = {
          status: 'CANCELED',
          canceledAt: now,
          endDate: now
        };
        break;
    }

    // Verificar se as assinaturas existem e podem ser modificadas
    const subscriptions = await db.patientSubscription.findMany({
      where: {
        id: { in: subscriptionIds }
      },
      select: {
        id: true,
        status: true,
        patient: {
          select: {
            user: { select: { name: true, email: true } }
          }
        }
      }
    });

    if (subscriptions.length !== subscriptionIds.length) {
      return NextResponse.json(
        { error: 'Algumas assinaturas não foram encontradas' },
        { status: 404 }
      );
    }

    // Validar se a ação pode ser aplicada a cada assinatura
    const invalidSubscriptions = [];
    for (const subscription of subscriptions) {
      const canApplyAction = validateAction(action, subscription.status);
      if (!canApplyAction) {
        invalidSubscriptions.push({
          id: subscription.id,
          patientName: subscription.patient.user.name,
          currentStatus: subscription.status
        });
      }
    }

    if (invalidSubscriptions.length > 0) {
      return NextResponse.json({
        error: 'Algumas assinaturas não podem receber esta ação',
        invalidSubscriptions
      }, { status: 400 });
    }

    // Aplicar a ação em lote
    const result = await db.patientSubscription.updateMany({
      where: {
        id: { in: subscriptionIds }
      },
      data: updateData
    });

    // Criar notificações para os pacientes afetados
    const notifications = subscriptions.map(subscription => ({
      subscriptionId: subscription.id,
      type: getNotificationType(action),
      title: getNotificationTitle(action),
      message: getNotificationMessage(action, subscription.patient.user.name || 'Paciente'),
      sentAt: now
    }));

    await db.subscriptionNotification.createMany({
      data: notifications
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} assinatura(s) ${getActionPastTense(action)} com sucesso`,
      affectedCount: result.count
    });

  } catch (error) {
    console.error('Erro ao executar ação em lote:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function validateAction(action: string, currentStatus: string): boolean {
  switch (action) {
    case 'pause':
      return currentStatus === 'ACTIVE';
    case 'reactivate':
      return currentStatus === 'PAUSED';
    case 'cancel':
      return ['ACTIVE', 'PAUSED', 'PAST_DUE', 'UNPAID'].includes(currentStatus);
    default:
      return false;
  }
}

function getNotificationType(action: string): string {
  switch (action) {
    case 'pause':
      return 'SUBSCRIPTION_PAUSED';
    case 'reactivate':
      return 'SUBSCRIPTION_REACTIVATED';
    case 'cancel':
      return 'SUBSCRIPTION_CANCELED';
    default:
      return 'SUBSCRIPTION_UPDATED';
  }
}

function getNotificationTitle(action: string): string {
  switch (action) {
    case 'pause':
      return 'Assinatura Pausada';
    case 'reactivate':
      return 'Assinatura Reativada';
    case 'cancel':
      return 'Assinatura Cancelada';
    default:
      return 'Assinatura Atualizada';
  }
}

function getNotificationMessage(action: string, patientName: string): string {
  switch (action) {
    case 'pause':
      return `Olá ${patientName}, sua assinatura foi pausada temporariamente. Entre em contato conosco para mais informações.`;
    case 'reactivate':
      return `Olá ${patientName}, sua assinatura foi reativada com sucesso. Você já pode agendar suas consultas.`;
    case 'cancel':
      return `Olá ${patientName}, sua assinatura foi cancelada. Agradecemos por ter usado nossos serviços.`;
    default:
      return `Olá ${patientName}, sua assinatura foi atualizada.`;
  }
}

function getActionPastTense(action: string): string {
  switch (action) {
    case 'pause':
      return 'pausada(s)';
    case 'reactivate':
      return 'reativada(s)';
    case 'cancel':
      return 'cancelada(s)';
    default:
      return 'atualizada(s)';
  }
}
