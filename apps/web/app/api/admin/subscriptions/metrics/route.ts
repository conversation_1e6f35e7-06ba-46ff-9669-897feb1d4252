import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function GET(request: NextRequest) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sixMonthsAgo = new Date(now.getTime() - 6 * 30 * 24 * 60 * 60 * 1000);

    // Calcular MRR (Monthly Recurring Revenue)
    const activeSubscriptions = await db.patientSubscription.findMany({
      where: { status: 'ACTIVE' },
      select: { planPrice: true, cycle: true }
    });

    let mrr = 0;
    activeSubscriptions.forEach(sub => {
      const price = Number(sub.planPrice);
      switch (sub.cycle) {
        case 'WEEKLY':
          mrr += price * 4.33; // ~4.33 semanas por mês
          break;
        case 'BIWEEKLY':
          mrr += price * 2.17; // ~2.17 quinzenas por mês
          break;
        case 'MONTHLY':
          mrr += price;
          break;
        case 'QUARTERLY':
          mrr += price / 3;
          break;
        case 'SEMIANNUALLY':
          mrr += price / 6;
          break;
        case 'YEARLY':
          mrr += price / 12;
          break;
        default:
          mrr += price; // Default para mensal
      }
    });

    // Calcular Churn Rate (cancelamentos nos últimos 30 dias)
    const [totalSubscriptionsStart, canceledSubscriptions] = await Promise.all([
      db.patientSubscription.count({
        where: {
          createdAt: { lte: thirtyDaysAgo }
        }
      }),
      db.patientSubscription.count({
        where: {
          status: 'CANCELED',
          canceledAt: { gte: thirtyDaysAgo }
        }
      })
    ]);

    const churnRate = totalSubscriptionsStart > 0 ? (canceledSubscriptions / totalSubscriptionsStart) * 100 : 0;

    // Top 5 médicos por receita
    const topDoctors = await db.transaction.groupBy({
      by: ['doctorId'],
      where: {
        status: 'PAID',
        createdAt: { gte: thirtyDaysAgo }
      },
      _sum: {
        doctorAmount: true
      },
      _count: {
        id: true
      },
      orderBy: {
        _sum: {
          doctorAmount: 'desc'
        }
      },
      take: 5
    });

    // Buscar informações dos médicos
    const doctorIds = topDoctors.map(d => d.doctorId).filter(Boolean) as string[];
    const doctors = await db.doctor.findMany({
      where: { id: { in: doctorIds } },
      include: {
        user: { select: { name: true } }
      }
    });

    const topDoctorsWithInfo = topDoctors.map(doctor => {
      const doctorInfo = doctors.find(d => d.id === doctor.doctorId);
      return {
        id: doctor.doctorId,
        name: doctorInfo?.user?.name || 'Médico',
        revenue: Number(doctor._sum.doctorAmount || 0),
        appointments: doctor._count.id
      };
    });

    // Evolução do MRR (últimos 6 meses)
    const mrrEvolution = [];
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);
      
      const monthlySubscriptions = await db.patientSubscription.findMany({
        where: {
          status: 'ACTIVE',
          startDate: { lte: monthEnd },
          OR: [
            { endDate: null },
            { endDate: { gte: monthStart } }
          ]
        },
        select: { planPrice: true, cycle: true }
      });

      let monthMrr = 0;
      monthlySubscriptions.forEach(sub => {
        const price = Number(sub.planPrice);
        switch (sub.cycle) {
          case 'WEEKLY':
            monthMrr += price * 4.33;
            break;
          case 'BIWEEKLY':
            monthMrr += price * 2.17;
            break;
          case 'MONTHLY':
            monthMrr += price;
            break;
          case 'QUARTERLY':
            monthMrr += price / 3;
            break;
          case 'SEMIANNUALLY':
            monthMrr += price / 6;
            break;
          case 'YEARLY':
            monthMrr += price / 12;
            break;
          default:
            monthMrr += price;
        }
      });

      mrrEvolution.push({
        month: monthStart.toISOString().slice(0, 7), // YYYY-MM
        mrr: Math.round(monthMrr * 100) / 100
      });
    }

    // Métricas adicionais
    const [
      totalActiveSubscriptions,
      totalPastDueSubscriptions,
      totalCanceledThisMonth,
      totalNewThisMonth
    ] = await Promise.all([
      db.patientSubscription.count({ where: { status: 'ACTIVE' } }),
      db.patientSubscription.count({ where: { status: { in: ['PAST_DUE', 'UNPAID'] } } }),
      db.patientSubscription.count({
        where: {
          status: 'CANCELED',
          canceledAt: { gte: thirtyDaysAgo }
        }
      }),
      db.patientSubscription.count({
        where: {
          status: 'ACTIVE',
          createdAt: { gte: thirtyDaysAgo }
        }
      })
    ]);

    return NextResponse.json({
      mrr: Math.round(mrr * 100) / 100,
      churnRate: Math.round(churnRate * 100) / 100,
      topDoctors: topDoctorsWithInfo,
      mrrEvolution,
      metrics: {
        activeSubscriptions: totalActiveSubscriptions,
        pastDueSubscriptions: totalPastDueSubscriptions,
        canceledThisMonth: totalCanceledThisMonth,
        newThisMonth: totalNewThisMonth
      }
    });

  } catch (error) {
    console.error('Erro ao buscar métricas de assinaturas:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
