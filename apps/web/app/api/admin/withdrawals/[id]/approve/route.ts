import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const withdrawalId = params.id;

    // Buscar o saque
    const withdrawal = await db.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!withdrawal) {
      return NextResponse.json({ error: 'Saque não encontrado' }, { status: 404 });
    }

    // Verificar se já está aprovado
    if (withdrawal.status === 'APPROVED' || withdrawal.status === 'COMPLETED') {
      return NextResponse.json({ error: 'Saque já foi aprovado' }, { status: 400 });
    }

    // Aprovar o saque
    const updatedWithdrawal = await db.withdrawal.update({
      where: { id: withdrawalId },
      data: {
        status: 'APPROVED',
        approvedAt: new Date(),
      },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    // Em produção, você enviaria um email de notificação
    console.log(`Withdrawal approved: ${withdrawal.doctor.user.name} (${withdrawal.doctor.user.email}) - Amount: ${withdrawal.amount}`);

    return NextResponse.json({
      success: true,
      message: 'Saque aprovado com sucesso',
      withdrawal: updatedWithdrawal,
    });

  } catch (error) {
    console.error('Error approving withdrawal:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

