import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@saas/auth/lib/current-user';
import { db } from 'database';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const withdrawalId = params.id;
    const body = await request.json();
    const { reason } = body;

    if (!reason || reason.trim().length === 0) {
      return NextResponse.json({ error: 'Motivo da rejeição é obrigatório' }, { status: 400 });
    }

    // Buscar o saque
    const withdrawal = await db.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!withdrawal) {
      return NextResponse.json({ error: 'Saque não encontrado' }, { status: 404 });
    }

    // Verificar se já está rejeitado
    if (withdrawal.status === 'REJECTED') {
      return NextResponse.json({ error: 'Saque já foi rejeitado' }, { status: 400 });
    }

    // Rejeitar o saque
    const updatedWithdrawal = await db.withdrawal.update({
      where: { id: withdrawalId },
      data: {
        status: 'REJECTED',
        rejectionReason: reason,
      },
      include: {
        doctor: {
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    // Em produção, você enviaria um email de notificação com o motivo
    console.log(`Withdrawal rejected: ${withdrawal.doctor.user.name} (${withdrawal.doctor.user.email}) - Amount: ${withdrawal.amount} - Reason: ${reason}`);

    return NextResponse.json({
      success: true,
      message: 'Saque rejeitado com sucesso',
      withdrawal: updatedWithdrawal,
    });

  } catch (error) {
    console.error('Error rejecting withdrawal:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

