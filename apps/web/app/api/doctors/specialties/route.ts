import { NextResponse } from "next/server";
import { createApiCaller } from "api/trpc/caller";

export async function GET() {
  try {
    const api = await createApiCaller();
    // Preferir specialties.list se existir, senão usar doctors.getSpecialties
    const specialties = await api.specialties.list();
    return NextResponse.json(
      specialties.map((s: any) => ({ id: s.id, name: s.name }))
    );
  } catch (err) {
    try {
      const api = await createApiCaller();
      // Fallback: doctors.getSpecialties
      const specs = await api.doctors.getSpecialties();
      return NextResponse.json(specs);
    } catch (error) {
      console.error("[API/DOCTORS/SPECIALTIES] Error:", error);
      return NextResponse.json({ error: "Failed to load specialties" }, { status: 500 });
    }
  }
}


