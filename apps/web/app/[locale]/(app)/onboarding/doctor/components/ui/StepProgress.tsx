'use client';

import React from 'react';
import { CheckCircle, Circle } from 'lucide-react';
import { cn } from '@ui/lib';

// ============================================================================
// INTERFACE
// ============================================================================

interface StepProgressProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: string[];
  stepNames: string[];
}

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function StepProgress({
  currentStep,
  totalSteps,
  completedSteps,
  stepNames
}: StepProgressProps) {
  return (
    <div className="w-full">
      {/* Progress Bar */}
      <div className="flex items-center justify-between">
        {Array.from({ length: totalSteps }, (_, index) => {
                 const stepId = ['personal', 'professional', 'specialties', 'schedule'][index];
          const isCompleted = completedSteps.includes(stepId);
          const isCurrent = index === currentStep;
          const isPast = index < currentStep;

          return (
            <div key={index} className="flex items-center">
              {/* Step Circle */}
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200",
                    {
                      "bg-blue-600 border-blue-600 text-white": isCurrent || isCompleted,
                      "bg-white border-gray-300 text-gray-400": !isCurrent && !isCompleted,
                      "ring-2 ring-blue-100": isCurrent
                    }
                  )}
                >
                  {isCompleted ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    <span className="text-xs font-semibold">{index + 1}</span>
                  )}
                </div>

                {/* Step Name */}
                <div className="mt-2 text-center">
                  <p className={cn(
                    "text-xs font-medium transition-colors duration-200",
                    {
                      "text-blue-600": isCurrent || isCompleted,
                      "text-gray-500": !isCurrent && !isCompleted
                    }
                  )}>
                    {stepNames[index]}
                  </p>
                </div>
              </div>

              {/* Connector Line */}
              {index < totalSteps - 1 && (
                <div className={cn(
                  "flex-1 h-0.5 mx-4 transition-colors duration-200",
                  {
                    "bg-blue-600": isCompleted,
                    "bg-gray-300": !isCompleted
                  }
                )} />
              )}
            </div>
          );
        })}
      </div>


    </div>
  );
}

// ============================================================================
// COMPONENTE COMPACTO PARA MOBILE
// ============================================================================

export function CompactStepProgress({
  currentStep,
  totalSteps,
  completedSteps
}: Omit<StepProgressProps, 'stepNames'>) {
  return (
    <div className="w-full">
      {/* Progress Bar */}
      <div className="flex items-center justify-between mb-4">
        {Array.from({ length: totalSteps }, (_, index) => {
                 const stepId = ['personal', 'professional', 'specialties', 'schedule'][index];
          const isCompleted = completedSteps.includes(stepId);
          const isCurrent = index === currentStep;

          return (
            <div key={index} className="flex items-center">
              {/* Step Circle */}
              <div
                className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-200",
                  {
                    "bg-blue-600 border-blue-600 text-white": isCurrent || isCompleted,
                    "bg-white border-gray-300 text-gray-400": !isCurrent && !isCompleted,
                    "ring-2 ring-blue-100": isCurrent
                  }
                )}
              >
                {isCompleted ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <span className="text-xs font-semibold">{index + 1}</span>
                )}
              </div>

              {/* Connector Line */}
              {index < totalSteps - 1 && (
                <div className={cn(
                  "flex-1 h-0.5 mx-2 transition-colors duration-200",
                  {
                    "bg-blue-600": isCompleted,
                    "bg-gray-300": !isCompleted
                  }
                )} />
              )}
            </div>
          );
        })}
      </div>

      {/* Current Step Name */}
      <div className="text-center">
        <p className="text-sm font-medium text-gray-900">
          Passo {currentStep + 1} de {totalSteps}
        </p>
      </div>
    </div>
  );
}
