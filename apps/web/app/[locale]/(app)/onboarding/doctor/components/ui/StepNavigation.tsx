'use client';

import React from 'react';
import { ChevronLeft, ChevronRight, Loader2, Save, Check } from 'lucide-react';

import { StepNavigationProps } from '../../lib/types';
import { Button } from '@ui/components/button';
import { cn } from '@ui/lib';

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function StepNavigation({
  currentStep,
  totalSteps,
  canGoNext,
  canGoPrevious,
  onNext,
  onPrevious,
  isLoading
}: StepNavigationProps) {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className="bg-white border-t border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Botão Voltar */}
        <div>
          {canGoPrevious && !isFirstStep && (
            <Button
              variant="outline"
              onClick={onPrevious}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="w-4 h-4" />
              <span>Voltar</span>
            </Button>
          )}
        </div>

        {/* Botão Próximo/Finalizar */}
        <div>
          <NextButton
            isLastStep={isLastStep}
            canGoNext={canGoNext}
            isLoading={isLoading}
            onNext={onNext}
          />
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// COMPONENTE DO BOTÃO PRÓXIMO
// ============================================================================

interface NextButtonProps {
  isLastStep: boolean;
  canGoNext: boolean;
  isLoading: boolean;
  onNext: () => void;
}

function NextButton({ isLastStep, canGoNext, isLoading, onNext }: NextButtonProps) {
  if (isLastStep) {
    return (
      <Button
        onClick={onNext}
        disabled={!canGoNext || isLoading}
        className={cn(
          "flex items-center space-x-2 min-w-[120px]",
          "bg-green-600 hover:bg-green-700 text-white",
          "disabled:bg-gray-300 disabled:text-gray-500"
        )}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Finalizando...</span>
          </>
        ) : (
          <>
            <Check className="w-4 h-4" />
            <span>Finalizar</span>
          </>
        )}
      </Button>
    );
  }

  return (
    <Button
      onClick={onNext}
      disabled={!canGoNext || isLoading}
      className="flex items-center space-x-2 min-w-[120px]"
    >
      {isLoading ? (
        <>
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>Salvando...</span>
        </>
      ) : (
        <>
          <span>Próximo</span>
          <ChevronRight className="w-4 h-4" />
        </>
      )}
    </Button>
  );
}

// ============================================================================
// NAVEGAÇÃO COMPACTA PARA MOBILE
// ============================================================================

export function CompactStepNavigation({
  currentStep,
  totalSteps,
  canGoNext,
  canGoPrevious,
  onNext,
  onPrevious,
  isLoading
}: StepNavigationProps) {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-3 z-50">
      <div className="flex items-center justify-between">
        {/* Botão Voltar */}
        {canGoPrevious && !isFirstStep ? (
          <Button
            variant="outline"
            size="sm"
            onClick={onPrevious}
            disabled={isLoading}
            className="flex items-center space-x-1"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Voltar</span>
          </Button>
        ) : (
          <div />
        )}

        {/* Botão Próximo */}
        <Button
          size="sm"
          onClick={onNext}
          disabled={!canGoNext || isLoading}
          className={cn(
            "flex items-center space-x-1 px-6",
            isLastStep && "bg-green-600 hover:bg-green-700"
          )}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : isLastStep ? (
            <>
              <Check className="w-4 h-4" />
              <span>Finalizar</span>
            </>
          ) : (
            <>
              <span>Próximo</span>
              <ChevronRight className="w-4 h-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

