'use client';

import React, { useState } from 'react';
import { Clock, Plus, Trash2, Calendar } from 'lucide-react';

import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Checkbox } from '@ui/components/checkbox';

import { StepProps } from '../../lib/types';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

interface WorkingHours {
  id: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
}

interface LunchBreak {
  startTime: string;
  endTime: string;
  isActive: boolean;
}

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO SIMPLIFICADA
// ============================================================================

export function ScheduleStep({ data, onDataChange, isLoading, errors }: StepProps) {
  const [workingHours, setWorkingHours] = useState<WorkingHours[]>(
    data?.workingHours || [
      { id: '1', dayOfWeek: 'monday', startTime: '08:00', endTime: '17:00', isActive: true },
      { id: '2', dayOfWeek: 'tuesday', startTime: '08:00', endTime: '17:00', isActive: true },
      { id: '3', dayOfWeek: 'wednesday', startTime: '08:00', endTime: '17:00', isActive: true },
      { id: '4', dayOfWeek: 'thursday', startTime: '08:00', endTime: '17:00', isActive: true },
      { id: '5', dayOfWeek: 'friday', startTime: '08:00', endTime: '17:00', isActive: true },
      { id: '6', dayOfWeek: 'saturday', startTime: '08:00', endTime: '12:00', isActive: false },
      { id: '7', dayOfWeek: 'sunday', startTime: '08:00', endTime: '12:00', isActive: false }
    ]
  );

  const [lunchBreak, setLunchBreak] = useState<LunchBreak>(
    data?.lunchBreak || {
      startTime: '12:00',
      endTime: '13:00',
      isActive: true
    }
  );

  // Envia dados padrão no carregamento inicial
  React.useEffect(() => {
    if (!data) {
      const defaultData = {
        workingHours,
        lunchBreak
      };
      onDataChange?.(defaultData);
    }
  }, []);

  const [scheduleErrors, setScheduleErrors] = useState<string[]>([]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleInputChange = (field: string, value: any) => {
    const newData = {
      workingHours,
      lunchBreak,
      [field]: value
    };
    onDataChange?.(newData);
  };

  const handleWorkingHoursChange = (id: string, field: string, value: any) => {
    const newWorkingHours = workingHours.map(hour =>
      hour.id === id ? { ...hour, [field]: value } : hour
    );
    setWorkingHours(newWorkingHours);

    const newData = {
      workingHours: newWorkingHours,
      lunchBreak
    };
    onDataChange?.(newData);
  };

  const handleLunchBreakChange = (field: string, value: any) => {
    const newLunchBreak = { ...lunchBreak, [field]: value };
    setLunchBreak(newLunchBreak);

    const newData = {
      workingHours,
      lunchBreak: newLunchBreak
    };
    onDataChange?.(newData);
  };

  const toggleDayActive = (id: string) => {
    handleWorkingHoursChange(id, 'isActive', !workingHours.find(h => h.id === id)?.isActive);
  };

  const getDayName = (dayOfWeek: string) => {
    const days = {
      monday: 'Segunda-feira',
      tuesday: 'Terça-feira',
      wednesday: 'Quarta-feira',
      thursday: 'Quinta-feira',
      friday: 'Sexta-feira',
      saturday: 'Sábado',
      sunday: 'Domingo'
    };
    return days[dayOfWeek as keyof typeof days] || dayOfWeek;
  };

  const getErrorMessage = (fieldName: string) => {
    const fieldErrors = errors[fieldName];
    return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] : '';
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-full mb-4">
          <Calendar className="w-8 h-8 text-blue-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Horários de Trabalho
        </h1>
        <p className="text-gray-600">
          Configure seus horários de atendimento (opcional)
        </p>
      </div>

      <div className="space-y-6">
        {/* Horários de Trabalho */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span>Horários Semanais</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {workingHours.map((hour) => (
                <div key={hour.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`day-${hour.id}`}
                      checked={hour.isActive}
                      onCheckedChange={() => toggleDayActive(hour.id)}
                    />
                    <Label htmlFor={`day-${hour.id}`} className="font-medium min-w-[120px]">
                      {getDayName(hour.dayOfWeek)}
                    </Label>
                  </div>

                  {hour.isActive && (
                    <div className="flex items-center space-x-2">
                      <div>
                        <Label htmlFor={`start-${hour.id}`} className="text-sm text-gray-600">
                          Início
                        </Label>
                        <Input
                          id={`start-${hour.id}`}
                          type="time"
                          value={hour.startTime}
                          onChange={(e) => handleWorkingHoursChange(hour.id, 'startTime', e.target.value)}
                          className="w-32"
                        />
                      </div>
                      <span className="text-gray-400">-</span>
                      <div>
                        <Label htmlFor={`end-${hour.id}`} className="text-sm text-gray-600">
                          Fim
                        </Label>
                        <Input
                          id={`end-${hour.id}`}
                          type="time"
                          value={hour.endTime}
                          onChange={(e) => handleWorkingHoursChange(hour.id, 'endTime', e.target.value)}
                          className="w-32"
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Horário de Almoço */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span>Horário de Almoço</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="lunch-break"
                  checked={lunchBreak.isActive}
                  onCheckedChange={(checked) => handleLunchBreakChange('isActive', checked)}
                />
                <Label htmlFor="lunch-break" className="font-medium">
                  Tenho horário de almoço
                </Label>
              </div>

              {lunchBreak.isActive && (
                <div className="flex items-center space-x-4">
                  <div>
                    <Label htmlFor="lunch-start" className="text-sm text-gray-600">
                      Início do Almoço
                    </Label>
                    <Input
                      id="lunch-start"
                      type="time"
                      value={lunchBreak.startTime}
                      onChange={(e) => handleLunchBreakChange('startTime', e.target.value)}
                      className="w-32"
                    />
                  </div>
                  <span className="text-gray-400">-</span>
                  <div>
                    <Label htmlFor="lunch-end" className="text-sm text-gray-600">
                      Fim do Almoço
                    </Label>
                    <Input
                      id="lunch-end"
                      type="time"
                      value={lunchBreak.endTime}
                      onChange={(e) => handleLunchBreakChange('endTime', e.target.value)}
                      className="w-32"
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Resumo */}
        <Card>
          <CardHeader>
            <CardTitle>Resumo dos Horários</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {workingHours
                .filter(hour => hour.isActive)
                .map(hour => (
                  <div key={hour.id} className="flex justify-between items-center py-2 border-b last:border-b-0">
                    <span className="font-medium">{getDayName(hour.dayOfWeek)}</span>
                    <span className="text-gray-600">
                      {hour.startTime} - {hour.endTime}
                    </span>
                  </div>
                ))}

              {workingHours.filter(hour => hour.isActive).length === 0 && (
                <p className="text-gray-500 text-center py-4">
                  Nenhum horário configurado
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
