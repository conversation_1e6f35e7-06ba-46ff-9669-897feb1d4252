'use client';

import React from 'react';
import { CheckCircle, Circle, AlertCircle, Clock } from 'lucide-react';
import { cn } from '@ui/lib';
import {
  ProgressIndicatorProps,
  ONBOARDING_STEPS,
  STEP_STATUS
} from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function ProgressIndicator({
  currentStep,
  totalSteps,
  completedSteps,
  stepStatuses
}: ProgressIndicatorProps) {
  const completionPercentage = Math.round((completedSteps.length / totalSteps) * 100);

  return (
    <div className="w-full bg-white border-b border-gray-200 px-6 py-4">
      {/* Header simples */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            Cadastro Médico
          </h2>
          <p className="text-sm text-gray-600">
            Etapa {currentStep + 1} de {totalSteps}
          </p>
        </div>

        <div className="text-right">
          <div className="text-lg font-bold text-blue-600">
            {completionPercentage}%
          </div>
          <div className="text-xs text-gray-500">
            Concluído
          </div>
        </div>
      </div>

      {/* Barra de progresso simples */}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${completionPercentage}%` }}
        />
      </div>
    </div>
  );
}


// ============================================================================
// COMPONENTE COMPACTO PARA MOBILE
// ============================================================================

export function CompactProgressIndicator({
  currentStep,
  totalSteps,
  completedSteps
}: Pick<ProgressIndicatorProps, 'currentStep' | 'totalSteps' | 'completedSteps'>) {
  const completionPercentage = Math.round((completedSteps.length / totalSteps) * 100);

  return (
    <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-900">
          Etapa {currentStep + 1} de {totalSteps}
        </span>
        <span className="text-sm font-bold text-blue-600">
          {completionPercentage}%
        </span>
      </div>

      <div className="w-full bg-gray-200 rounded-full h-1.5">
        <div
          className="bg-blue-600 h-1.5 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${completionPercentage}%` }}
        />
      </div>
    </div>
  );
}
