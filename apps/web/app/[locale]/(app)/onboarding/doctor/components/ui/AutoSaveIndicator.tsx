'use client';

import React, { useEffect, useState } from 'react';
import { Check, Clock, AlertCircle, Wifi, WifiOff, Loader2 } from 'lucide-react';
import { cn } from '@ui/lib';
import { AutoSaveIndicatorProps } from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL
// ============================================================================

export function AutoSaveIndicator({
  isDirty,
  lastSaved,
  isAutoSaving,
  autoSaveEnabled
}: AutoSaveIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [timeAgo, setTimeAgo] = useState<string>('');

  // Monitora status de conexão
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Atualiza tempo desde último salvamento
  useEffect(() => {
    if (!lastSaved) return;

    const updateTimeAgo = () => {
      const now = new Date();
      const diff = now.getTime() - lastSaved.getTime();
      const minutes = Math.floor(diff / 60000);
      const seconds = Math.floor((diff % 60000) / 1000);

      if (minutes > 0) {
        setTimeAgo(`${minutes}m atrás`);
      } else if (seconds > 0) {
        setTimeAgo(`${seconds}s atrás`);
      } else {
        setTimeAgo('agora');
      }
    };

    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 1000);

    return () => clearInterval(interval);
  }, [lastSaved]);

  // Determina o status atual
  const status = getAutoSaveStatus({
    isDirty,
    isAutoSaving,
    autoSaveEnabled,
    isOnline,
    lastSaved
  });

  return (
    <div className="flex items-center space-x-2 text-sm">
      {/* Ícone de status */}
      <div className={cn("flex items-center", status.className)}>
        {status.icon}
      </div>

      {/* Texto de status */}
      <div className="flex flex-col">
        <span className={cn("font-medium", status.className)}>
          {status.text}
        </span>

        {lastSaved && timeAgo && (
          <span className="text-xs text-gray-500">
            Salvo {timeAgo}
          </span>
        )}
      </div>

      {/* Indicador de conexão */}
      <div className="ml-2">
        {isOnline ? (
          <Wifi className="w-4 h-4 text-green-500" />
        ) : (
          <WifiOff className="w-4 h-4 text-red-500" />
        )}
      </div>
    </div>
  );
}

// ============================================================================
// VERSÃO COMPACTA
// ============================================================================

export function CompactAutoSaveIndicator({
  isDirty,
  lastSaved,
  isAutoSaving,
  autoSaveEnabled
}: AutoSaveIndicatorProps) {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const status = getAutoSaveStatus({
    isDirty,
    isAutoSaving,
    autoSaveEnabled,
    isOnline,
    lastSaved
  });

  return (
    <div className={cn(
      "flex items-center space-x-1 px-2 py-1 rounded-full text-xs",
      status.bgClassName
    )}>
      {status.icon}
      <span className={status.className}>
        {status.shortText || status.text}
      </span>
    </div>
  );
}

// ============================================================================
// INDICADOR FLUTUANTE
// ============================================================================

export function FloatingAutoSaveIndicator({
  isDirty,
  lastSaved,
  isAutoSaving,
  autoSaveEnabled
}: AutoSaveIndicatorProps) {
  const [isVisible, setIsVisible] = useState(false);

  // Mostra o indicador quando há mudanças ou está salvando
  useEffect(() => {
    if (isDirty || isAutoSaving) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => setIsVisible(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [isDirty, isAutoSaving]);

  if (!isVisible) return null;

  const status = getAutoSaveStatus({
    isDirty,
    isAutoSaving,
    autoSaveEnabled,
    isOnline: true,
    lastSaved
  });

  return (
    <div className={cn(
      "fixed bottom-4 right-4 z-50 px-3 py-2 rounded-lg shadow-lg",
      "flex items-center space-x-2 text-sm font-medium",
      "transition-all duration-300 ease-in-out",
      status.bgClassName,
      isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
    )}>
      {status.icon}
      <span className={status.className}>
        {status.text}
      </span>
    </div>
  );
}

// ============================================================================
// FUNÇÕES AUXILIARES
// ============================================================================

interface AutoSaveStatus {
  icon: React.ReactNode;
  text: string;
  shortText?: string;
  className: string;
  bgClassName: string;
}

function getAutoSaveStatus({
  isDirty,
  isAutoSaving,
  autoSaveEnabled,
  isOnline,
  lastSaved
}: {
  isDirty: boolean;
  isAutoSaving: boolean;
  autoSaveEnabled: boolean;
  isOnline: boolean;
  lastSaved: Date | null;
}): AutoSaveStatus {
  // Salvando
  if (isAutoSaving) {
    return {
      icon: <Loader2 className="w-4 h-4 animate-spin" />,
      text: 'Salvando...',
      shortText: 'Salvando',
      className: 'text-blue-600',
      bgClassName: 'bg-blue-50 border border-blue-200'
    };
  }

  // Offline
  if (!isOnline) {
    return {
      icon: <WifiOff className="w-4 h-4" />,
      text: 'Offline - Mudanças serão salvas quando conectar',
      shortText: 'Offline',
      className: 'text-red-600',
      bgClassName: 'bg-red-50 border border-red-200'
    };
  }

  // Auto-save desabilitado
  if (!autoSaveEnabled) {
    return {
      icon: <AlertCircle className="w-4 h-4" />,
      text: 'Auto-save desabilitado',
      shortText: 'Manual',
      className: 'text-yellow-600',
      bgClassName: 'bg-yellow-50 border border-yellow-200'
    };
  }

  // Há mudanças não salvas
  if (isDirty) {
    return {
      icon: <Clock className="w-4 h-4" />,
      text: 'Mudanças não salvas',
      shortText: 'Não salvo',
      className: 'text-yellow-600',
      bgClassName: 'bg-yellow-50 border border-yellow-200'
    };
  }

  // Tudo salvo
  if (lastSaved) {
    return {
      icon: <Check className="w-4 h-4" />,
      text: 'Todas as mudanças foram salvas',
      shortText: 'Salvo',
      className: 'text-green-600',
      bgClassName: 'bg-green-50 border border-green-200'
    };
  }

  // Estado inicial
  return {
    icon: <Clock className="w-4 h-4" />,
    text: 'Pronto para salvar',
    shortText: 'Pronto',
    className: 'text-gray-600',
    bgClassName: 'bg-gray-50 border border-gray-200'
  };
}

// ============================================================================
// HOOK PARA CONTROLAR VISIBILIDADE
// ============================================================================

export function useAutoSaveVisibility(isDirty: boolean, isAutoSaving: boolean) {
  const [showIndicator, setShowIndicator] = useState(false);

  useEffect(() => {
    if (isDirty || isAutoSaving) {
      setShowIndicator(true);
    } else {
      const timer = setTimeout(() => {
        setShowIndicator(false);
      }, 3000); // Esconde após 3 segundos

      return () => clearTimeout(timer);
    }
  }, [isDirty, isAutoSaving]);

  return showIndicator;
}

// ============================================================================
// CONFIGURAÇÕES DE AUTO-SAVE
// ============================================================================

export function AutoSaveSettings({
  autoSaveEnabled,
  onToggleAutoSave
}: {
  autoSaveEnabled: boolean;
  onToggleAutoSave: (enabled: boolean) => void;
}) {
  return (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div>
        <h4 className="text-sm font-medium text-gray-900">
          Salvamento Automático
        </h4>
        <p className="text-xs text-gray-600">
          Salva suas mudanças automaticamente a cada 30 segundos
        </p>
      </div>

      <button
        onClick={() => onToggleAutoSave(!autoSaveEnabled)}
        className={cn(
          "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
          autoSaveEnabled ? "bg-blue-600" : "bg-gray-200"
        )}
      >
        <span
          className={cn(
            "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
            autoSaveEnabled ? "translate-x-6" : "translate-x-1"
          )}
        />
      </button>
    </div>
  );
}
