'use client';

import React from 'react';
import { Logo } from '@shared/components/Logo';
import { cn } from '@ui/lib';

interface OnboardingHeaderProps {
  currentStep: number;
  totalSteps: number;
  completedSteps: string[];
  className?: string;
}

export function OnboardingHeader({
  currentStep,
  totalSteps,
  completedSteps,
  className
}: OnboardingHeaderProps) {
  const completionPercentage = Math.round((completedSteps.length / totalSteps) * 100);

  return (
    <header className={cn(
      "bg-white border-b border-gray-200 sticky top-0 z-50 w-full",
      className
    )}>
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo ZapVida */}
          <div className="flex items-center">
            <Logo withLabel={true} />
          </div>

          {/* Progress Info - Simplificado */}
          <div className="text-sm text-gray-600">
            <span className="font-semibold text-gray-900">
              Etapa {currentStep + 1} de {totalSteps}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
}
