'use client';

import React, { useState, useEffect } from 'react';
import { CheckCircle, Edit, MapPin, GraduationCap, Award, Calendar, Clock } from 'lucide-react';

import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Checkbox } from '@ui/components/checkbox';
import { Badge } from '@ui/components/badge';
import { Label } from '@ui/components/label';

import { getSpecialties } from '../../lib/api';
import { StepProps } from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO SIMPLIFICADA
// ============================================================================

export function ReviewStep({ data, onDataChange, isLoading, errors }: StepProps) {
  const [specialties, setSpecialties] = useState<any[]>([]);
  const [agreedToTerms, setAgreedToTerms] = useState(data?.agreedToTerms || false);
  const [agreedToPrivacy, setAgreedToPrivacy] = useState(data?.agreedToPrivacy || false);

  // Carrega dados auxiliares
  useEffect(() => {
    const loadData = async () => {
      try {
        const specialtyList = await getSpecialties();
        setSpecialties(specialtyList);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
      }
    };

    loadData();
  }, []);

  const handleInputChange = (field: string, value: any) => {
    const newData = {
      ...data,
      [field]: value
    };
    onDataChange?.(newData);
  };

  const getSpecialtyName = (id: string) => {
    const specialty = specialties.find(s => s.id === id);
    return specialty ? specialty.name : 'Especialidade não encontrada';
  };


  const getDayName = (dayOfWeek: string) => {
    const days = {
      monday: 'Segunda-feira',
      tuesday: 'Terça-feira',
      wednesday: 'Quarta-feira',
      thursday: 'Quinta-feira',
      friday: 'Sexta-feira',
      saturday: 'Sábado',
      sunday: 'Domingo'
    };
    return days[dayOfWeek as keyof typeof days] || dayOfWeek;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-50 rounded-full mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Revisão Final
        </h1>
        <p className="text-gray-600">
          Confirme seus dados antes de finalizar o cadastro
        </p>
      </div>

      <div className="space-y-6">
        {/* Dados Pessoais */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              <span>Dados Pessoais</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">CPF</p>
                <p className="font-medium">{data?.cpf || 'Não informado'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Data de Nascimento</p>
                <p className="font-medium">{data?.birthDate || 'Não informado'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Gênero</p>
                <p className="font-medium">{data?.gender || 'Não informado'}</p>
              </div>
            </div>

            {data?.address && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">Endereço</p>
                <p className="font-medium">
                  {data.address.street}, {data.address.number}
                  {data.address.complement && `, ${data.address.complement}`}
                </p>
                <p className="text-sm text-gray-600">
                  {data.address.neighborhood} - {data.address.city}/{data.address.state}
                </p>
                <p className="text-sm text-gray-600">CEP: {data.address.zipCode}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Dados Profissionais */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <GraduationCap className="w-5 h-5 text-blue-600" />
              <span>Dados Profissionais</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">CRM</p>
                <p className="font-medium">{data?.crm || 'Não informado'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Estado do CRM</p>
                <p className="font-medium">{data?.crmState || 'Não informado'}</p>
              </div>
            </div>

          </CardContent>
        </Card>

        {/* Especialidades */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-blue-600" />
              <span>Especialidades</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data?.primarySpecialtyId && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Especialidade Principal</p>
                  <Badge className="text-sm">
                    {getSpecialtyName(data.primarySpecialtyId)}
                  </Badge>
                </div>
              )}

              {data?.secondarySpecialtyIds && data.secondarySpecialtyIds.length > 0 && (
                <div>
                  <p className="text-sm text-gray-600 mb-2">Especialidades Secundárias</p>
                  <div className="flex flex-wrap gap-2">
                    {data.secondarySpecialtyIds.map((id: string) => (
                      <Badge key={id} className="text-sm border">
                        {getSpecialtyName(id)}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Horários */}
        {data?.workingHours && data.workingHours.some((hour: any) => hour.isActive) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-blue-600" />
                <span>Horários de Trabalho</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {data.workingHours
                  .filter((hour: any) => hour.isActive)
                  .map((hour: any) => (
                    <div key={hour.id} className="flex justify-between items-center py-2 border-b last:border-b-0">
                      <span className="font-medium">{getDayName(hour.dayOfWeek)}</span>
                      <span className="text-gray-600">
                        {hour.startTime} - {hour.endTime}
                      </span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Termos e Condições */}
        <Card>
          <CardHeader>
            <CardTitle>Termos e Condições</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={agreedToTerms}
                onCheckedChange={(checked) => {
                  setAgreedToTerms(checked as boolean);
                  handleInputChange('agreedToTerms', checked);
                }}
              />
              <div className="space-y-1">
                <Label htmlFor="terms" className="text-sm font-medium">
                  Aceito os Termos de Uso e Política de Privacidade
                </Label>
                <p className="text-xs text-gray-600">
                  Li e concordo com os termos de uso da plataforma ZapVida
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Checkbox
                id="privacy"
                checked={agreedToPrivacy}
                onCheckedChange={(checked) => {
                  setAgreedToPrivacy(checked as boolean);
                  handleInputChange('agreedToPrivacy', checked);
                }}
              />
              <div className="space-y-1">
                <Label htmlFor="privacy" className="text-sm font-medium">
                  Autorizo o uso dos meus dados para fins médicos
                </Label>
                <p className="text-xs text-gray-600">
                  Concordo com o processamento dos meus dados conforme a LGPD
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
