'use client';

import React from 'react';
import { Clock, ArrowRight } from 'lucide-react';
import { Button } from '@ui/components/button';
import { StepProps } from '../../lib/types';

// ============================================================================
// COMPONENTE PRINCIPAL - VERSÃO ULTRA SIMPLIFICADA
// ============================================================================

export function WelcomeStep({ onNext, isLoading }: StepProps) {
  const handleNext = () => {
    onNext?.();
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="mb-12 text-center">
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-blue-50 rounded-full mb-6">
            <span className="text-5xl">🩺</span>
          </div>
        </div>

        <h1 className="text-4xl font-bold text-gray-900 mb-6">
          Bem-vindo ao ZapVida!
        </h1>

        <p className="text-xl text-gray-600 mb-12 leading-relaxed max-w-2xl mx-auto">
          Sua conta foi criada com sucesso! Agora vamos configurar seu perfil médico em <strong>5 passos simples</strong> para começar a atender pacientes.
        </p>
      </div>

      {/* CTA Principal */}
      <div className="flex flex-col items-center justify-center">
        <Button
          onClick={handleNext}
          disabled={isLoading}
          size="lg"
          className="px-12 py-4 text-lg font-semibold bg-blue-600 hover:bg-blue-700"
        >
          {isLoading ? (
            <>
              <Clock className="w-5 h-5 mr-2 animate-spin" />
              Preparando...
            </>
          ) : (
            <>
              Começar Configuração
              <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </Button>

        <p className="text-sm text-gray-500 mt-4">
          Leva apenas 3-5 minutos para completar
        </p>
      </div>
    </div>
  );
}
