import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  OnboardingState,
  OnboardingStepId,
  OnboardingProgress,
  UseOnboardingStateReturn,
  ONBOARDING_STEPS,
  STEP_STATUS
} from '../lib/types';

// ============================================================================
// ESTADO INICIAL
// ============================================================================

const initialState: OnboardingState = {
  currentStep: 0,
  completedSteps: [],
  stepStatuses: {
    personal: STEP_STATUS.NOT_STARTED,
    professional: STEP_STATUS.NOT_STARTED,
    specialties: STEP_STATUS.NOT_STARTED,
    schedule: STEP_STATUS.NOT_STARTED
  },
  data: {},
  errors: {},
  isLoading: false,
  isDirty: false,
  lastSaved: null,
  autoSaveEnabled: false
};

// ============================================================================
// HOOK PRINCIPAL - COM NAVEGAÇÃO POR URL
// ============================================================================

export function useOnboardingState(): UseOnboardingStateReturn {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [state, setState] = useState<OnboardingState>(initialState);

  // Sincroniza com URL
  useEffect(() => {
    const stepParam = searchParams.get('step');
    if (stepParam) {
      const stepIndex = parseInt(stepParam) - 1;
      if (stepIndex >= 0 && stepIndex < ONBOARDING_STEPS.length) {
        setState(prev => ({
          ...prev,
          currentStep: stepIndex
        }));
      }
    }
  }, [searchParams]);

  // ============================================================================
  // ACTIONS - COM ATUALIZAÇÃO DE URL
  // ============================================================================

  const setCurrentStep = (step: number) => {
    const validStep = Math.max(0, Math.min(step, ONBOARDING_STEPS.length - 1));

    setState(prev => ({
      ...prev,
      currentStep: validStep
    }));

    // Atualiza URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('step', (validStep + 1).toString());
    router.push(newUrl.pathname + newUrl.search, { scroll: false });
  };

  const updateStepData = (stepId: OnboardingStepId, data: any) => {
    setState(prev => ({
      ...prev,
      data: {
        ...prev.data,
        [stepId]: data
      },
      isDirty: true,
      stepStatuses: {
        ...prev.stepStatuses,
        [stepId]: STEP_STATUS.IN_PROGRESS
      }
    }));
  };

  const markStepCompleted = (stepId: OnboardingStepId) => {
    setState(prev => {
      const completedSteps = prev.completedSteps.includes(stepId)
        ? prev.completedSteps
        : [...prev.completedSteps, stepId];

      return {
        ...prev,
        completedSteps,
        stepStatuses: {
          ...prev.stepStatuses,
          [stepId]: STEP_STATUS.COMPLETED
        },
        isDirty: false,
        lastSaved: new Date()
      };
    });
  };

  const setErrors = (errors: Record<string, string[]>) => {
    setState(prev => ({
      ...prev,
      errors
    }));
  };

  const clearErrors = () => {
    setState(prev => ({
      ...prev,
      errors: {}
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading
    }));
  };

  const reset = () => {
    setState(initialState);
    router.push('/onboarding/doctor?step=1', { scroll: false });
  };

  // ============================================================================
  // PROGRESS - CALCULADO DIRETAMENTE
  // ============================================================================

  const currentStepId = ONBOARDING_STEPS[state.currentStep];
  const stepData = state.data[currentStepId];

  // Verifica se há dados para continuar
  let canContinue = false;
  if (stepData) {
    canContinue = Object.keys(stepData).length > 0;
  }

  const progress: OnboardingProgress = {
    currentStep: currentStepId,
    completedSteps: state.completedSteps,
    totalSteps: ONBOARDING_STEPS.length,
    completionPercentage: Math.round((state.completedSteps.length / ONBOARDING_STEPS.length) * 100),
    estimatedTimeRemaining: 15,
    canContinue,
    canGoBack: state.currentStep > 0
  };

  return {
    state,
    actions: {
      setCurrentStep,
      updateStepData,
      markStepCompleted,
      setErrors,
      clearErrors,
      setLoading,
      reset
    },
    progress
  };
}

// ============================================================================
// HOOKS AUXILIARES
// ============================================================================

export function useAutoSave() {
  return {
    isAutoSaving: false,
    lastSaved: null,
    saveNow: async () => {}
  };
}

export function useStepValidation() {
  return {
    validateStepData: async () => true,
    isValidating: false,
    validationErrors: {},
    clearValidationErrors: () => {}
  };
}
