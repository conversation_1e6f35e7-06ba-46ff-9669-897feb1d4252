import { z } from 'zod';
import {
  PersonalDataSchema,
  ProfessionalDataSchema,
  SpecialtyDataSchema,
  ScheduleDataSchema,
  OnboardingStepId,
  OnboardingData
} from './types';

// ============================================================================
// VALIDADORES POR STEP - VERSÃO SIMPLIFICADA
// ============================================================================

export const stepValidators: Record<OnboardingStepId, z.ZodSchema | null> = {
  welcome: null, // Não precisa de validação
  personal: PersonalDataSchema,
  professional: ProfessionalDataSchema,
  specialties: SpecialtyDataSchema,
  schedule: ScheduleDataSchema,
  review: null // Validação será feita em todos os steps anteriores
};

// ============================================================================
// FUNÇÕES DE VALIDAÇÃO - VERSÃO SIMPLIFICADA
// ============================================================================

/**
 * Valida os dados de um step específico - VERSÃO SIMPLIFICADA
 */
export function validateStep(stepId: OnboardingStepId, data: any): {
  isValid: boolean;
  errors: Record<string, string[]>;
} {
  // Validação simples - apenas verifica se há dados
  if (!data || Object.keys(data).length === 0) {
    return { isValid: false, errors: { general: ['Dados obrigatórios não preenchidos'] } };
  }

  const validator = stepValidators[stepId];

  if (!validator) {
    return { isValid: true, errors: {} };
  }

  try {
    validator.parse(data);
    return { isValid: true, errors: {} };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string[]> = {};

      error.errors.forEach((err) => {
        const path = err.path.join('.');
        if (!errors[path]) {
          errors[path] = [];
        }
        errors[path].push(err.message);
      });

      return { isValid: false, errors };
    }

    return {
      isValid: false,
      errors: { general: ['Erro de validação desconhecido'] }
    };
  }
}

/**
 * Valida todos os dados do onboarding - VERSÃO SIMPLIFICADA
 */
export function validateAllSteps(data: Partial<OnboardingData>): {
  isValid: boolean;
  errors: Record<string, Record<string, string[]>>;
} {
  const allErrors: Record<string, Record<string, string[]>> = {};
  let isValid = true;

  // Validação simples - apenas verifica se há dados
  Object.entries(data).forEach(([stepId, stepData]) => {
    if (!stepData || Object.keys(stepData).length === 0) {
      allErrors[stepId] = { general: ['Dados obrigatórios não preenchidos'] };
      isValid = false;
    }
  });

  return { isValid, errors: allErrors };
}

// ============================================================================
// VALIDAÇÕES CUSTOMIZADAS - VERSÃO SIMPLIFICADA
// ============================================================================

/**
 * Valida se o CRM é único no sistema - VERSÃO SIMPLIFICADA
 */
export async function validateCrmUniqueness(crm: string, crmState: string): Promise<boolean> {
  // Mock - sempre retorna true para simplificar
  return true;
}

/**
 * Valida se o email é único no sistema - VERSÃO SIMPLIFICADA
 */
export async function validateEmailUniqueness(email: string): Promise<boolean> {
  // Mock - sempre retorna true para simplificar
  return true;
}

/**
 * Valida se o CPF é válido usando algoritmo de validação
 */
export function validateCpf(cpf: string): boolean {
  // Remove caracteres não numéricos
  const cleanCpf = cpf.replace(/\D/g, '');

  // Verifica se tem 11 dígitos
  if (cleanCpf.length !== 11) return false;

  // Verifica se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleanCpf)) return false;

  // Validação do primeiro dígito verificador
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
  }
  let remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCpf.charAt(9))) return false;

  // Validação do segundo dígito verificador
  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
  }
  remainder = (sum * 10) % 11;
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(cleanCpf.charAt(10))) return false;

  return true;
}

/**
 * Valida horários de trabalho para evitar conflitos - VERSÃO SIMPLIFICADA
 */
export function validateWorkingHours(workingHours: any[]): {
  isValid: boolean;
  errors: string[];
} {
  // Validação simples - apenas verifica se há horários
  if (!workingHours || workingHours.length === 0) {
    return { isValid: true, errors: [] };
  }

  return {
    isValid: true,
    errors: []
  };
}

// ============================================================================
// FORMATADORES E SANITIZADORES
// ============================================================================

/**
 * Formata CPF para exibição
 */
export function formatCpf(cpf: string): string {
  const cleanCpf = cpf.replace(/\D/g, '');
  return cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
}

/**
 * Formata telefone para exibição
 */
export function formatPhone(phone: string): string {
  const cleanPhone = phone.replace(/\D/g, '');
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleanPhone.length === 10) {
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  return phone;
}

/**
 * Formata CEP para exibição
 */
export function formatCep(cep: string): string {
  const cleanCep = cep.replace(/\D/g, '');
  return cleanCep.replace(/(\d{5})(\d{3})/, '$1-$2');
}

/**
 * Sanitiza dados antes de salvar - VERSÃO SIMPLIFICADA
 */
export function sanitizeOnboardingData(data: Partial<OnboardingData>): Partial<OnboardingData> {
  // Sanitização simples - apenas retorna os dados como estão
  return { ...data };
}

// ============================================================================
// CONSTANTES DE VALIDAÇÃO
// ============================================================================

export const VALIDATION_MESSAGES = {
  REQUIRED: 'Este campo é obrigatório',
  INVALID_EMAIL: 'Email inválido',
  INVALID_CPF: 'CPF inválido',
  INVALID_PHONE: 'Telefone inválido',
  INVALID_CRM: 'CRM inválido',
  EMAIL_EXISTS: 'Este email já está cadastrado',
  CRM_EXISTS: 'Este CRM já está cadastrado',
  INVALID_DATE: 'Data inválida',
  INVALID_TIME: 'Horário inválido',
  TIME_CONFLICT: 'Conflito de horários',
  MAX_LENGTH: (max: number) => `Máximo ${max} caracteres`,
  MIN_LENGTH: (min: number) => `Mínimo ${min} caracteres`,
  MIN_AGE: (min: number) => `Idade mínima: ${min} anos`,
  MAX_AGE: (max: number) => `Idade máxima: ${max} anos`
} as const;
