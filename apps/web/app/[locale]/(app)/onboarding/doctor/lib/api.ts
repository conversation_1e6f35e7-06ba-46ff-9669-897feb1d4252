import { useState } from 'react';
import {
  OnboardingStepId,
  OnboardingData,
  OnboardingApiResponse,
  SaveStepRequest,
  ValidateStepRequest,
  OnboardingCompleteRequest,
  OnboardingProgress
} from './types';

// ============================================================================
// CONFIGURAÇÃO DA API
// ============================================================================

const API_BASE_URL = '/api/doctor/onboarding';

class OnboardingApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public errors?: Record<string, string[]>
  ) {
    super(message);
    this.name = 'OnboardingApiError';
  }
}

// ============================================================================
// FUNÇÕES DE API
// ============================================================================

/**
 * Faz uma requisição HTTP com tratamento de erro
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<OnboardingApiResponse<T>> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new OnboardingApiError(
        data.message || 'Erro na requisição',
        response.status,
        data.errors
      );
    }

    return data;
  } catch (error) {
    if (error instanceof OnboardingApiError) {
      throw error;
    }

    throw new OnboardingApiError(
      'Erro de conexão com o servidor',
      0
    );
  }
}

/**
 * Obtém o progresso atual do onboarding
 */
export async function getOnboardingProgress(): Promise<OnboardingProgress> {
  const response = await apiRequest<OnboardingProgress>('/progress');

  if (!response.success || !response.data) {
    throw new OnboardingApiError('Erro ao obter progresso do onboarding', 500);
  }

  return response.data;
}

/**
 * Salva os dados de um step específico
 */
export async function saveStep(request: SaveStepRequest): Promise<void> {
  const response = await apiRequest(`/step/${request.stepId}`, {
    method: 'POST',
    body: JSON.stringify({
      data: request.data,
      validateOnly: request.validateOnly || false
    })
  });

  if (!response.success) {
    throw new OnboardingApiError(
      response.message || 'Erro ao salvar step',
      400,
      response.errors
    );
  }
}

/**
 * Valida os dados de um step específico
 */
export async function validateStep(request: ValidateStepRequest): Promise<{
  isValid: boolean;
  errors: Record<string, string[]>;
}> {
  try {
    const response = await apiRequest('/validate', {
      method: 'POST',
      body: JSON.stringify(request)
    });

    return {
      isValid: response.success,
      errors: response.errors || {}
    };
  } catch (error) {
    if (error instanceof OnboardingApiError && error.errors) {
      return {
        isValid: false,
        errors: error.errors
      };
    }

    return {
      isValid: false,
      errors: { general: ['Erro de validação'] }
    };
  }
}

/**
 * Finaliza o onboarding
 */
export async function completeOnboarding(request: OnboardingCompleteRequest): Promise<{
  doctorId: string;
  profileUrl: string;
}> {
  const response = await apiRequest<{
    doctorId: string;
    profileUrl: string;
  }>('/complete', {
    method: 'POST',
    body: JSON.stringify(request)
  });

  if (!response.success || !response.data) {
    throw new OnboardingApiError(
      response.message || 'Erro ao finalizar onboarding',
      400,
      response.errors
    );
  }

  return response.data;
}

/**
 * Valida se um CRM é único
 */
export async function validateCrmUniqueness(crm: string, crmState: string): Promise<boolean> {
  try {
    const response = await apiRequest<{ isUnique: boolean }>('/validate-crm', {
      method: 'POST',
      body: JSON.stringify({ crm, crmState })
    });

    return response.data?.isUnique || false;
  } catch (error) {
    console.error('Erro ao validar CRM:', error);
    return false;
  }
}

/**
 * Valida se um email é único
 */
export async function validateEmailUniqueness(email: string): Promise<boolean> {
  try {
    const response = await apiRequest<{ isUnique: boolean }>('/validate-email', {
      method: 'POST',
      body: JSON.stringify({ email })
    });

    return response.data?.isUnique || false;
  } catch (error) {
    console.error('Erro ao validar email:', error);
    return false;
  }
}

/**
 * Busca especialidades médicas
 */
export async function getSpecialties(): Promise<Array<{
  id: string;
  name: string;
  category: string;
  description?: string;
}>> {
  try {
    const response = await fetch('/api/specialties');
    const data = await response.json();

    if (!response.ok) {
      throw new Error('Erro ao buscar especialidades');
    }

    // A API retorna os dados diretamente, não dentro de um objeto specialties
    return Array.isArray(data) ? data.map(specialty => ({
      id: specialty.id,
      name: specialty.name,
      category: 'Médica', // Categoria padrão
      description: specialty.description
    })) : [];
  } catch (error) {
    console.error('Erro ao buscar especialidades:', error);
    return [];
  }
}

/**
 * Busca hospitais disponíveis
 */
export async function getHospitals(): Promise<Array<{
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
}>> {
  try {
    const response = await fetch('/api/hospitals');
    const data = await response.json();

    if (!response.ok) {
      throw new Error('Erro ao buscar hospitais');
    }

    return data.hospitals || [];
  } catch (error) {
    console.error('Erro ao buscar hospitais:', error);
    return [];
  }
}

/**
 * Busca CEP na API dos Correios
 */
export async function getCepInfo(cep: string): Promise<{
  street: string;
  neighborhood: string;
  city: string;
  state: string;
} | null> {
  try {
    const cleanCep = cep.replace(/\D/g, '');

    if (cleanCep.length !== 8) {
      return null;
    }

    const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`);
    const data = await response.json();

    if (data.erro) {
      return null;
    }

    return {
      street: data.logradouro || '',
      neighborhood: data.bairro || '',
      city: data.localidade || '',
      state: data.uf || ''
    };
  } catch (error) {
    console.error('Erro ao buscar CEP:', error);
    return null;
  }
}

// ============================================================================
// HOOKS DE API
// ============================================================================

/**
 * Hook para gerenciar estado de loading das APIs
 */
export function useApiState() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeApi = async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await apiCall();
      return result;
    } catch (error) {
      if (error instanceof OnboardingApiError) {
        setError(error.message);
      } else {
        setError('Erro inesperado');
      }
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    error,
    executeApi,
    clearError: () => setError(null)
  };
}

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Debounce para evitar muitas chamadas de API
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Retry automático para APIs que falharam
 */
export async function retryApi<T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i < maxRetries; i++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;

      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }

  throw lastError!;
}

/**
 * Cache simples para evitar requisições desnecessárias
 */
class SimpleCache {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private ttl = 5 * 60 * 1000; // 5 minutos

  get<T>(key: string): T | null {
    const item = this.cache.get(key);

    if (!item) return null;

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clear(): void {
    this.cache.clear();
  }
}

export const apiCache = new SimpleCache();
