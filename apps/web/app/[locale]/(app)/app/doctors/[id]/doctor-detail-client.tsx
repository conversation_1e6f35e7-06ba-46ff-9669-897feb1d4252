'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Separator } from "@ui/components/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { Label } from "@ui/components/label";
import { Skeleton } from "@ui/components/skeleton";
import {
  ArrowLeft,
  User,
  Mail,
  Phone,
  FileText,
  Calendar,
  Star,
  Banknote,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Activity,
  CreditCard,
  MapPin,
  Building,
  Eye,
  Download,
  Edit,
  Ban,
  Check,
  X,
  DollarSign,
  Users,
  Award,
  BarChart3,
  ArrowUpRight,
  ArrowDownLeft,
  RefreshCw,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    avatarUrl?: string;
    emailVerified: boolean;
    createdAt: string;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  documents: Array<{
    id: string;
    type: string;
    fileName: string;
    fileUrl: string;
    status: string;
    createdAt: string;
    updatedAt: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
    createdAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    doctorAmount: number;
    platformFee: number;
    status: string;
    paymentMethod: string;
    createdAt: string;
    paidAt?: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
    createdAt: string;
    patient: {
      user: {
        name: string;
      };
    };
  }>;
  financialMetrics: {
    totalRevenue: number;
    totalPlatformFee: number;
    totalAppointments: number;
    completedAppointments: number;
    averageRating: number;
    totalEvaluations: number;
    availableBalance: number;
  };
}

interface DoctorDetailClientProps {
  doctor: DoctorData;
}

export function DoctorDetailClient({ doctor }: DoctorDetailClientProps) {
  const router = useRouter();
  const [approvalModalOpen, setApprovalModalOpen] = useState(false);
  const [rejectionModalOpen, setRejectionModalOpen] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Badge className="bg-green-100 text-green-800 border-green-200 flex items-center"><CheckCircle className="w-3 h-3 mr-1" />Aprovado</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-100 text-red-800 border-red-200 flex items-center"><XCircle className="w-3 h-3 mr-1" />Rejeitado</Badge>;
      case 'PENDING':
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 flex items-center"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>;
    }
  };

  const getOnlineStatusBadge = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return <Badge className="bg-green-100 text-green-800 border-green-200 flex items-center"><Activity className="w-3 h-3 mr-1" />Online</Badge>;
      case 'BUSY':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200 flex items-center"><Clock className="w-3 h-3 mr-1" />Ocupado</Badge>;
      case 'OFFLINE':
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200 flex items-center"><XCircle className="w-3 h-3 mr-1" />Offline</Badge>;
    }
  };


  const getDocumentTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      'CRM': 'CRM',
      'SPECIALTY': 'Especialidade',
      'ID': 'Identidade',
      'COLLEGE_DEGREE': 'Diploma',
      'RESIDENCY': 'Residência',
      'OTHER': 'Outro',
    };
    return types[type] || type;
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'PAID':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="w-3 h-3 mr-1" />Pago</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="w-3 h-3 mr-1" />Pendente</Badge>;
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="w-3 h-3 mr-1" />Falhou</Badge>;
      case 'REFUNDED':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><XCircle className="w-3 h-3 mr-1" />Reembolsado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleApprove = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/doctors/${doctor.id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao aprovar médico');
      }

      toast.success('Médico aprovado com sucesso');
      setApprovalModalOpen(false);
      router.refresh();
    } catch (error) {
      toast.error('Erro ao aprovar médico');
      console.error('Error approving doctor:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      toast.error('Por favor, informe o motivo da rejeição');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/doctors/${doctor.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: rejectionReason }),
      });

      if (!response.ok) {
        throw new Error('Erro ao rejeitar médico');
      }

      toast.success('Médico rejeitado com sucesso');
      setRejectionModalOpen(false);
      setRejectionReason('');
      router.refresh();
    } catch (error) {
      toast.error('Erro ao rejeitar médico');
      console.error('Error rejecting doctor:', error);
    } finally {
      setLoading(false);
    }
  };


  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{doctor.user.name}</h1>
            <p className="text-muted-foreground">
              {doctor.specialties?.map(s => s.name).join(', ') || 'Especialidade não definida'} • CRM: {doctor.crm}/{doctor.crmState}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {doctor.documentStatus === 'PENDING' && (
            <>
              <Button
                variant="outline"
                onClick={() => setRejectionModalOpen(true)}
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                <X className="h-4 w-4 mr-2" />
                Rejeitar
              </Button>
              <Button
                onClick={() => setApprovalModalOpen(true)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="h-4 w-4 mr-2" />
                Aprovar
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Informações Básicas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Informações Pessoais
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-6">
            <Avatar className="h-20 w-20">
              <AvatarImage src={doctor.user.avatarUrl} />
              <AvatarFallback>
                {doctor.user.name?.split(' ').map(n => n[0]).join('').toUpperCase()}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Nome</Label>
                  <p className="text-lg font-semibold">{doctor.user.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                  <p className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    {doctor.user.email}
                    {doctor.user.emailVerified && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Telefone</Label>
                  <p className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    {doctor.user.phone || 'Não informado'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">CRM</Label>
                  <p className="font-mono">{doctor.crm}/{doctor.crmState}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status do Documento</Label>
                  <div>{getStatusBadge(doctor.documentStatus)}</div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status Online</Label>
                  <div>{getOnlineStatusBadge(doctor.onlineStatus)}</div>
                </div>
              </div>

              {doctor.biography && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Biografia</Label>
                  <p className="text-sm mt-1">{doctor.biography}</p>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                <Label className="text-sm font-medium text-muted-foreground">Especialidades:</Label>
                {doctor.specialties.map((specialty) => (
                  <Badge key={specialty.id} variant="secondary">
                    {specialty.name}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Métricas Financeiras */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Receita Total</p>
                <p className="text-2xl font-bold">{formatCurrency(doctor.financialMetrics.totalRevenue)}</p>
              </div>
            </div>
          </CardContent>
        </Card>


        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Banknote className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Saldo Disponível</p>
                <p className="text-2xl font-bold">{formatCurrency(doctor.financialMetrics.availableBalance)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs de Conteúdo */}
      <Tabs defaultValue="documents" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="documents">Documentos</TabsTrigger>
          <TabsTrigger value="transactions">Transações</TabsTrigger>
          <TabsTrigger value="appointments">Agendamentos</TabsTrigger>
          <TabsTrigger value="evaluations">Avaliações</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documentos Enviados
              </CardTitle>
            </CardHeader>
            <CardContent>
              {doctor.documents.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Nenhum documento enviado
                </p>
              ) : (
                <div className="space-y-4">
                  {doctor.documents.map((document) => (
                    <div key={document.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <FileText className="h-8 w-8 text-blue-600" />
                        <div>
                          <p className="font-medium">{getDocumentTypeLabel(document.type)}</p>
                          <p className="text-sm text-muted-foreground">{document.fileName}</p>
                          <p className="text-xs text-muted-foreground">
                            Enviado em {format(new Date(document.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(document.status)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(document.fileUrl, '_blank')}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Ver
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Transações Recentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {doctor.transactions.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Nenhuma transação encontrada
                </p>
              ) : (
                <div className="space-y-4">
                  {doctor.transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{formatCurrency(Number(transaction.doctorAmount))}</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(transaction.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Taxa: {formatCurrency(Number(transaction.platformFee))}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getPaymentStatusBadge(transaction.status)}
                        <Badge variant="outline">{transaction.paymentMethod}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Agendamentos Recentes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {doctor.appointments.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Nenhum agendamento encontrado
                </p>
              ) : (
                <div className="space-y-4">
                  {doctor.appointments.map((appointment) => (
                    <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          {format(new Date(appointment.scheduledAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Criado em {format(new Date(appointment.createdAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </p>
                      </div>
                      <Badge variant="outline">{appointment.status}</Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="evaluations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Avaliações dos Pacientes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {doctor.evaluations.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Nenhuma avaliação encontrada
                </p>
              ) : (
                <div className="space-y-4">
                  {doctor.evaluations.map((evaluation) => (
                    <div key={evaluation.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < evaluation.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium">{evaluation.patient.user.name}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(evaluation.createdAt), 'dd/MM/yyyy', { locale: ptBR })}
                        </span>
                      </div>
                      {evaluation.comment && (
                        <p className="text-sm text-muted-foreground">{evaluation.comment}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal de Aprovação */}
      <Dialog open={approvalModalOpen} onOpenChange={setApprovalModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Aprovar Médico
            </DialogTitle>
            <DialogDescription>
              Tem certeza que deseja aprovar este médico? Ele poderá começar a atender pacientes.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalModalOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleApprove} disabled={loading} className="bg-green-600 hover:bg-green-700">
              {loading ? 'Aprovando...' : 'Aprovar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Rejeição */}
      <Dialog open={rejectionModalOpen} onOpenChange={setRejectionModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              Rejeitar Médico
            </DialogTitle>
            <DialogDescription>
              Informe o motivo da rejeição para que o médico possa corrigir os problemas.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="rejection-reason">Motivo da Rejeição</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Descreva os motivos da rejeição..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectionModalOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleReject} disabled={loading} className="bg-red-600 hover:bg-red-700">
              {loading ? 'Rejeitando...' : 'Rejeitar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
}
