import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";
import { DoctorDetailClient } from "./doctor-detail-client";
import { DoctorDetailSkeleton } from "./doctor-detail-skeleton";
import { db } from "database";
import { Suspense } from "react";

export const dynamic = "force-dynamic";

interface DoctorDetailPageProps {
  params: {
    id: string;
  };
}

export default async function DoctorDetailPage({ params }: DoctorDetailPageProps) {
  const { user } = await currentUser();
  const locale = await getLocale();

  // Verificar se o usuário está autenticado
  if (!user) {
    redirect(`/${locale}/auth/login`);
  }

  // Verificar se o usuário tem permissão para acessar esta página
  // Apenas ADMIN pode ver detalhes de médicos
  if (user.role !== "ADMIN") {
    redirect(`/${locale}/app/dashboard`);
  }

  // Buscar dados do médico
  const doctor = await db.doctor.findUnique({
    where: { id: params.id },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          avatarUrl: true,
          emailVerified: true,
          createdAt: true,
        },
      },
      specialties: {
        select: {
          id: true,
          name: true,
        },
      },
      documents: {
        select: {
          id: true,
          type: true,
          fileName: true,
          fileUrl: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      },
      appointments: {
        select: {
          id: true,
          status: true,
          scheduledAt: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10, // Últimos 10 agendamentos
      },
      transactions: {
        select: {
          id: true,
          amount: true,
          doctorAmount: true,
          platformFee: true,
          status: true,
          paymentMethod: true,
          createdAt: true,
          paidAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 20, // Últimas 20 transações
      },
      evaluations: {
        select: {
          id: true,
          rating: true,
          comment: true,
          createdAt: true,
          patient: {
            select: {
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10, // Últimas 10 avaliações
      },
    },
  });

  if (!doctor) {
    redirect(`/${locale}/app/doctors`);
  }

  // Calcular métricas financeiras
  const totalRevenue = doctor.transactions
    .filter(t => t.status === 'PAID')
    .reduce((sum, t) => sum + Number(t.doctorAmount), 0);

  const totalPlatformFee = doctor.transactions
    .filter(t => t.status === 'PAID')
    .reduce((sum, t) => sum + Number(t.platformFee), 0);

  const totalAppointments = doctor.appointments.length;
  const completedAppointments = doctor.appointments.filter(a => a.status === 'COMPLETED').length;
  const averageRating = doctor.evaluations.length > 0
    ? doctor.evaluations.reduce((sum, e) => sum + e.rating, 0) / doctor.evaluations.length
    : 0;

  // Calcular saldo disponível (sem sistema de saques implementado ainda)
  const availableBalance = totalRevenue;

  // Preparar dados para o componente
  const doctorData = {
    ...doctor,
    financialMetrics: {
      totalRevenue,
      totalPlatformFee,
      totalAppointments,
      completedAppointments,
      averageRating,
      totalEvaluations: doctor.evaluations.length,
      availableBalance,
    },
  };

  return (
    <Suspense fallback={<DoctorDetailSkeleton />}>
      <DoctorDetailClient doctor={doctorData} />
    </Suspense>
  );
}
