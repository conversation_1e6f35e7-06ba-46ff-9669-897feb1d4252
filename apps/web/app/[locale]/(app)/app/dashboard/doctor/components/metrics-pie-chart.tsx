'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { <PERSON>, <PERSON><PERSON><PERSON>, Cell, ResponsiveContainer, Toolt<PERSON>, Legend } from 'recharts';
import { CheckCircle, XCircle, Clock, UserCheck } from 'lucide-react';

interface PieChartData {
  name: string;
  value: number;
  color: string;
  icon: React.ReactNode;
}

interface MetricsPieChartProps {
  completed: number;
  canceled: number;
  inProgress: number;
  noShow: number;
  isLoading?: boolean;
}

export function MetricsPieChart({
  completed,
  canceled,
  inProgress,
  noShow,
  isLoading
}: MetricsPieChartProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="h-6 w-40 bg-muted animate-pulse rounded" />
          <div className="h-4 w-56 bg-muted animate-pulse rounded" />
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  const data: PieChartData[] = [
    {
      name: 'Realizadas',
      value: completed,
      color: '#10b981',
      icon: <CheckCircle className="h-4 w-4" />
    },
    {
      name: 'Canceladas',
      value: canceled,
      color: '#ef4444',
      icon: <XCircle className="h-4 w-4" />
    },
    {
      name: 'Em Andamento',
      value: inProgress,
      color: '#f59e0b',
      icon: <Clock className="h-4 w-4" />
    },
    {
      name: 'Não Compareceu',
      value: noShow,
      color: '#6b7280',
      icon: <UserCheck className="h-4 w-4" />
    }
  ].filter(item => item.value > 0);

  const total = data.reduce((sum, item) => sum + item.value, 0);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Distribuição de Consultas</CardTitle>
        <CardDescription>
          Status das consultas da semana
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-4">
          <div className="h-64 w-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {data.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                  }}
                  labelStyle={{ color: 'hsl(var(--foreground))' }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>

          <div className="flex-1 space-y-3">
            {data.map((item, index) => {
              const percentage = total > 0 ? ((item.value / total) * 100).toFixed(1) : '0';
              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-medium">{item.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-bold">{item.value}</span>
                    <span className="text-xs text-muted-foreground">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

