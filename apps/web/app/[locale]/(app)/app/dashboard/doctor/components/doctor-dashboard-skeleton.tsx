import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>le, CardDescription } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { Calendar, Users, CheckCircle, FileText, Filter, CalendarClock } from "lucide-react";

export function DoctorDashboardSkeleton() {
  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Header - Mostrar elementos estáticos */}
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
        <div className="w-full sm:w-auto">
          <div className="h-8 w-64 mb-2">
            <Skeleton className="h-8 w-64" />
          </div>
          <div className="text-muted-foreground">
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      {/* Quick Stats Row - Mostrar estrutura dos cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Consultas Hoje
                </p>
                <Skeleton className="h-8 w-16 mb-2" />
                <div className="flex items-center text-xs text-muted-foreground">
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Calendar className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Consultas na Semana
                </p>
                <Skeleton className="h-8 w-16 mb-2" />
                <div className="flex items-center text-xs text-muted-foreground">
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Taxa de Conclusão
                </p>
                <Skeleton className="h-8 w-16 mb-2" />
                <div className="flex items-center text-xs text-muted-foreground">
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <CheckCircle className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Proporção de Retornos
                </p>
                <Skeleton className="h-8 w-16 mb-2" />
                <div className="flex items-center text-xs text-muted-foreground">
                  <Skeleton className="h-3 w-32" />
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content - Mostrar estrutura dos cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Próximas Consultas */}
        <Card className="hover:shadow-lg transition-all duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">Próximas Consultas</CardTitle>
                <CardDescription>Suas próximas consultas agendadas</CardDescription>
              </div>
              <Skeleton className="h-6 w-20" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground text-lg font-medium">Nenhuma consulta agendada</p>
              <p className="text-sm text-muted-foreground mt-2">Suas próximas consultas aparecerão aqui</p>
            </div>
          </CardContent>
          <div className="p-4 pt-0">
            <button className="w-full h-10 border rounded-md flex items-center justify-center text-sm font-medium hover:bg-muted/50 transition-colors">
              <CalendarClock className="h-4 w-4 mr-2" />
              Ver toda a agenda
            </button>
          </div>
        </Card>

        {/* Chart */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Desempenho do Período</CardTitle>
            <CardDescription>Consultas agendadas vs. realizadas</CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>

      {/* Chart de Distribuição */}
      <div className="mt-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Distribuição de Consultas</CardTitle>
            <CardDescription>Status das consultas do período</CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>

      {/* Quick Access Buttons - Mostrar estrutura */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-16 md:mb-0">
        <button className="h-24 flex flex-col items-center justify-center border-2 rounded-lg hover:shadow-lg transition-all duration-200">
          <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-2">
            <CalendarClock className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-sm">Consultas</span>
        </button>

        <button className="h-24 flex flex-col items-center justify-center border-2 rounded-lg hover:shadow-lg transition-all duration-200">
          <div className="h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-2">
            <Users className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-sm">Pacientes</span>
        </button>

        <button className="h-24 flex flex-col items-center justify-center border-2 rounded-lg hover:shadow-lg transition-all duration-200">
          <div className="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-2">
            <FileText className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-sm">Prescrições</span>
        </button>

        <button className="h-24 flex flex-col items-center justify-center border-2 rounded-lg hover:shadow-lg transition-all duration-200">
          <div className="h-8 w-8 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-2">
            <Calendar className="h-4 w-4 text-white" />
          </div>
          <span className="font-medium text-sm">Gerenciar Agenda</span>
        </button>
      </div>
    </div>
  );
}

