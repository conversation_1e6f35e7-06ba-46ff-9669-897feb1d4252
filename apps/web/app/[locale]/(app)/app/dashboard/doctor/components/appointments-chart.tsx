'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, YA<PERSON>s, Responsive<PERSON><PERSON>r, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface ChartData {
  day: string;
  appointments: number;
  completed: number;
  canceled: number;
}

interface AppointmentsChartProps {
  data: ChartData[];
  isLoading?: boolean;
}

export function AppointmentsChart({ data, isLoading }: AppointmentsChartProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <div className="h-6 w-40 bg-muted animate-pulse rounded" />
          <div className="h-4 w-56 bg-muted animate-pulse rounded" />
        </CardHeader>
        <CardContent>
          <div className="h-64 w-full bg-muted animate-pulse rounded" />
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Desempenho do Período</CardTitle>
          <CardDescription>
            Consultas agendadas vs. realizadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <div className="text-4xl mb-2">📊</div>
              <p>Nenhum dado disponível para o período selecionado</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalAppointments = data.reduce((sum, item) => sum + item.appointments, 0);
  const totalCompleted = data.reduce((sum, item) => sum + item.completed, 0);
  const completionRate = totalAppointments > 0 ? (totalCompleted / totalAppointments) * 100 : 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Desempenho Semanal</CardTitle>
            <CardDescription>
              Consultas agendadas vs. realizadas
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            {completionRate >= 80 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500" />
            )}
            <span className={`font-medium ${completionRate >= 80 ? 'text-green-600' : 'text-red-600'}`}>
              {completionRate.toFixed(1)}%
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
              <XAxis
                dataKey="day"
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--background))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                }}
                labelStyle={{ color: 'hsl(var(--foreground))' }}
              />
              <Legend />
              <Bar
                dataKey="appointments"
                name="Agendadas"
                fill="#3b82f6"
                radius={[4, 4, 0, 0]}
              />
              <Bar
                dataKey="completed"
                name="Realizadas"
                fill="#10b981"
                radius={[4, 4, 0, 0]}
              />
              <Bar
                dataKey="canceled"
                name="Canceladas"
                fill="#ef4444"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

