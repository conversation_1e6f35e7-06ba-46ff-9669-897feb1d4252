import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";
import { DoctorFinanceClient } from "./doctor-finance-client";

export default async function DoctorFinancePage() {
  const { user } = await currentUser();
  const locale = await getLocale();

  if (!user) {
    redirect(`/${locale}/auth/login`);
  }

  if (user.role !== "DOCTOR") {
    redirect("/app/dashboard");
  }

  return <DoctorFinanceClient />;
}
