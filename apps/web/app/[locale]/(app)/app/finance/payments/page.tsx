import { currentUser } from "@saas/auth/lib/current-user";
import { UserRoleSchema } from "database";
import { redirect } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Receipt, DollarSign, TrendingUp, TrendingDown, CreditCard, Users } from "lucide-react";
import { PaginationButton } from "@ui/components/pagination-button";

export default async function PaymentsPage({
  searchParams,
}: { searchParams?: { page?: string; search?: string; status?: string; type?: string } }) {
  const session = await currentUser();

  if (!session?.user || (session.user.role !== UserRoleSchema.Values.ADMIN && session.user.role !== UserRoleSchema.Values.DOCTOR)) {
    redirect("/app/dashboard");
  }

  const page = Number(searchParams?.page || 1);
  const pageSize = 10;
  const skip = Math.max(0, (page - 1) * pageSize);
  const search = searchParams?.search || "";
  const status = searchParams?.status || "";
  const type = searchParams?.type || "";

  // Mock data - em implementação real, buscar do banco de dados
  const allTransactions = [
    {
      id: "1",
      amount: 99.90,
      status: "PAID",
      paymentMethod: "Cartão de Crédito",
      description: "Assinatura Premium - Jan 2024",
      patientName: "João Silva",
      doctorName: "Dr. Maria Santos",
      paidAt: "2024-01-15T10:30:00Z",
      createdAt: "2024-01-15T10:25:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "2",
      amount: 150.00,
      status: "PAID",
      paymentMethod: "PIX",
      description: "Consulta de Retorno",
      patientName: "Ana Costa",
      doctorName: "Dr. Pedro Oliveira",
      paidAt: "2024-01-14T14:20:00Z",
      createdAt: "2024-01-14T14:15:00Z",
      type: "CONSULTATION"
    },
    {
      id: "3",
      amount: 99.90,
      status: "PENDING",
      paymentMethod: "Boleto",
      description: "Assinatura Premium - Fev 2024",
      patientName: "Carlos Lima",
      doctorName: "Dr. Maria Santos",
      paidAt: null,
      createdAt: "2024-01-13T09:00:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "4",
      amount: 200.00,
      status: "PAID",
      paymentMethod: "Cartão de Débito",
      description: "Consulta Urgente",
      patientName: "Mariana Souza",
      doctorName: "Dr. Pedro Oliveira",
      paidAt: "2024-01-12T16:45:00Z",
      createdAt: "2024-01-12T16:40:00Z",
      type: "CONSULTATION"
    },
    {
      id: "5",
      amount: 99.90,
      status: "FAILED",
      paymentMethod: "Cartão de Crédito",
      description: "Assinatura Premium - Jan 2024",
      patientName: "Roberto Alves",
      doctorName: "Dr. Maria Santos",
      paidAt: null,
      createdAt: "2024-01-11T11:30:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "6",
      amount: 120.00,
      status: "PAID",
      paymentMethod: "PIX",
      description: "Consulta de Rotina",
      patientName: "Fernanda Lima",
      doctorName: "Dr. Carlos Mendes",
      paidAt: "2024-01-10T09:15:00Z",
      createdAt: "2024-01-10T09:10:00Z",
      type: "CONSULTATION"
    },
    {
      id: "7",
      amount: 99.90,
      status: "PAID",
      paymentMethod: "Cartão de Crédito",
      description: "Assinatura Premium - Jan 2024",
      patientName: "Lucas Pereira",
      doctorName: "Dr. Ana Beatriz",
      paidAt: "2024-01-09T15:30:00Z",
      createdAt: "2024-01-09T15:25:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "8",
      amount: 180.00,
      status: "PENDING",
      paymentMethod: "Boleto",
      description: "Consulta Especializada",
      patientName: "Patricia Santos",
      doctorName: "Dr. Pedro Oliveira",
      paidAt: null,
      createdAt: "2024-01-08T11:20:00Z",
      type: "CONSULTATION"
    },
    {
      id: "9",
      amount: 99.90,
      status: "PAID",
      paymentMethod: "PIX",
      description: "Assinatura Premium - Jan 2024",
      patientName: "Rafael Costa",
      doctorName: "Dr. Maria Santos",
      paidAt: "2024-01-07T13:45:00Z",
      createdAt: "2024-01-07T13:40:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "10",
      amount: 250.00,
      status: "PAID",
      paymentMethod: "Cartão de Crédito",
      description: "Consulta de Emergência",
      patientName: "Juliana Almeida",
      doctorName: "Dr. Carlos Mendes",
      paidAt: "2024-01-06T08:30:00Z",
      createdAt: "2024-01-06T08:25:00Z",
      type: "CONSULTATION"
    },
    {
      id: "11",
      amount: 99.90,
      status: "REFUNDED",
      paymentMethod: "Cartão de Crédito",
      description: "Assinatura Premium - Jan 2024",
      patientName: "Marcos Silva",
      doctorName: "Dr. Ana Beatriz",
      paidAt: "2024-01-05T16:20:00Z",
      createdAt: "2024-01-05T16:15:00Z",
      type: "SUBSCRIPTION"
    },
    {
      id: "12",
      amount: 160.00,
      status: "PAID",
      paymentMethod: "PIX",
      description: "Consulta de Acompanhamento",
      patientName: "Camila Rodrigues",
      doctorName: "Dr. Pedro Oliveira",
      paidAt: "2024-01-04T14:10:00Z",
      createdAt: "2024-01-04T14:05:00Z",
      type: "CONSULTATION"
    }
  ];

  // Filtrar transações
  let filteredTransactions = allTransactions;

  if (search) {
    filteredTransactions = filteredTransactions.filter(t =>
      t.description.toLowerCase().includes(search.toLowerCase()) ||
      t.patientName.toLowerCase().includes(search.toLowerCase()) ||
      t.doctorName.toLowerCase().includes(search.toLowerCase())
    );
  }

  if (status) {
    filteredTransactions = filteredTransactions.filter(t => t.status === status);
  }

  if (type) {
    filteredTransactions = filteredTransactions.filter(t => t.type === type);
  }

  // Paginação
  const totalCount = filteredTransactions.length;
  const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));
  const transactions = filteredTransactions.slice(skip, skip + pageSize);

  // Calcular métricas
  const totalTransactions = allTransactions.length;
  const paidTransactions = allTransactions.filter(t => t.status === "PAID").length;
  const pendingTransactions = allTransactions.filter(t => t.status === "PENDING").length;
  const failedTransactions = allTransactions.filter(t => t.status === "FAILED").length;
  const totalRevenue = allTransactions
    .filter(t => t.status === "PAID")
    .reduce((sum, t) => sum + t.amount, 0);
  const successRate = totalTransactions > 0 ? Math.round((paidTransactions / totalTransactions) * 100) : 0;

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(amount);

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short"
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      PAID: { label: "Pago", className: "bg-green-100 text-green-800" },
      PENDING: { label: "Pendente", className: "bg-yellow-100 text-yellow-800" },
      FAILED: { label: "Falhou", className: "bg-red-100 text-red-800" },
      REFUNDED: { label: "Reembolsado", className: "bg-gray-100 text-gray-800" }
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || { label: status, className: "bg-gray-100 text-gray-800" };

    return (
      <Badge className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "SUBSCRIPTION":
        return <CreditCard className="h-4 w-4 text-blue-600" />;
      case "CONSULTATION":
        return <Receipt className="h-4 w-4 text-green-600" />;
      default:
        return <Receipt className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "SUBSCRIPTION":
        return "Assinatura";
      case "CONSULTATION":
        return "Consulta";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Receipt className="h-6 w-6" />
          Pagamentos
        </h1>
        <p className="text-sm text-muted-foreground mt-2">
          Histórico de pagamentos e transações financeiras
        </p>
      </div>

      {/* Métricas com novo padrão */}
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Total de Pagamentos
                </p>
                <h3 className="text-3xl font-bold text-blue-600">
                  {totalTransactions}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Todas as transações
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Receipt className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Receita Total
                </p>
                <h3 className="text-3xl font-bold text-green-600">
                  {formatCurrency(totalRevenue)}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Valor total arrecadado
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Taxa de Sucesso
                </p>
                <h3 className="text-3xl font-bold text-purple-600">
                  {successRate}%
                </h3>
                <div className="flex items-center mt-2 text-xs text-muted-foreground">
                  <div className={`flex items-center mr-2 ${successRate >= 80 ? 'text-green-600' : successRate >= 60 ? 'text-muted-foreground' : 'text-red-600'}`}>
                    {successRate >= 80 ? <TrendingUp className="h-3 w-3" /> : successRate >= 60 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
                    <span className="ml-1">{successRate}%</span>
                  </div>
                  <span>Pagamentos aprovados</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Pendentes
                </p>
                <h3 className="text-3xl font-bold text-orange-600">
                  {pendingTransactions}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Aguardando pagamento
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Pagamentos */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Pagamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="p-2 bg-muted rounded-full">
                    {getTypeIcon(transaction.type)}
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{transaction.description}</span>
                      {getStatusBadge(transaction.status)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {transaction.patientName} • {transaction.doctorName}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {getTypeLabel(transaction.type)} • {transaction.paymentMethod}
                    </div>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div className="font-semibold text-lg">
                    {formatCurrency(transaction.amount)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {transaction.paidAt ? formatDateTime(transaction.paidAt) : formatDateTime(transaction.createdAt)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Paginação */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationButton
            currentPage={page}
            totalPages={totalPages}
            baseUrl="/app/finance/payments"
            searchParams={{
              ...(search ? { search } : {}),
              ...(status ? { status } : {}),
              ...(type ? { type } : {})
            }}
          />
        </div>
      )}
    </div>
  );
}
