import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";

export default async function FinancePage() {
  const { user } = await currentUser();
  const locale = await getLocale();

  if (!user) {
    redirect(`/${locale}/auth/login`);
  }

  // Redirect based on role
  switch (user.role) {
    case "DOCTOR":
      redirect("/app/finance/doctor");
      break;
    case "ADMIN":
      redirect("/app/finance/admin");
      break;
    default:
      redirect("/app/dashboard");
  }
}
