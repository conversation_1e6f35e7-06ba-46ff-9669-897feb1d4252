"use client";

import { She<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>itle } from "@ui/components/sheet";
import { Badge } from "@ui/components/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { But<PERSON> } from "@ui/components/button";
import {
  CreditCard,
  User,
  Calendar,
  DollarSign,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Pause,
  Play
} from "lucide-react";

interface SubscriptionDetailsSheetProps {
  isOpen: boolean;
  onClose: () => void;
  subscription: {
    id: string;
    planId: string;
    planName: string;
    planPrice: number;
    status: string;
    startDate: string | Date | null;
    nextBillingDate: string | Date | null;
    asaasSubscriptionId: string | null;
    consultationsIncluded: number;
    consultationsUsed: number;
    lastResetDate: string | Date;
    lastPaymentStatus: string | null;
    lastPaymentAt: string | Date | null;
    failedPaymentAt: string | Date | null;
    paymentMethod: string | null;
    createdAt: string | Date;
    updatedAt: string | Date;
    patient: {
      id: string;
      user: {
        name: string | null;
        email: string | null;
        phone: string | null
      }
    };
  } | null;
}

export function SubscriptionDetailsSheet({ isOpen, onClose, subscription }: SubscriptionDetailsSheetProps) {
  if (!subscription) return null;

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(amount);

  const formatDateTime = (dateString: string | Date | null) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short"
    }).format(date);
  };

  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("pt-BR", { dateStyle: "short" }).format(date);
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      ACTIVE: { label: "Ativa", className: "bg-green-100 text-green-800", icon: CheckCircle },
      PAST_DUE: { label: "Em atraso", className: "bg-yellow-100 text-yellow-800", icon: AlertCircle },
      UNPAID: { label: "Não paga", className: "bg-orange-100 text-orange-800", icon: AlertCircle },
      PAUSED: { label: "Pausada", className: "bg-gray-100 text-gray-800", icon: Pause },
      CANCELED: { label: "Cancelada", className: "bg-red-100 text-red-800", icon: XCircle },
      PENDING: { label: "Pendente", className: "bg-blue-100 text-blue-800", icon: Clock },
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      className: "bg-gray-100 text-gray-800",
      icon: AlertCircle
    };

    const IconComponent = statusInfo.icon;

    return (
      <Badge className={statusInfo.className}>
        <IconComponent className="w-3 h-3 mr-1" />
        {statusInfo.label}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: string | null) => {
    if (!status) return <Badge variant="outline">Não informado</Badge>;

    const statusMap = {
      PAID: { label: "Pago", className: "bg-green-100 text-green-800" },
      PENDING: { label: "Pendente", className: "bg-yellow-100 text-yellow-800" },
      FAILED: { label: "Falhou", className: "bg-red-100 text-red-800" },
      REFUNDED: { label: "Reembolsado", className: "bg-gray-100 text-gray-800" }
    };

    const statusInfo = statusMap[status as keyof typeof statusMap] || {
      label: status,
      className: "bg-gray-100 text-gray-800"
    };

    return (
      <Badge className={statusInfo.className}>
        {statusInfo.label}
      </Badge>
    );
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[700px] sm:max-w-[700px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Detalhes da Assinatura
          </SheetTitle>
        </SheetHeader>

        <div className="mt-6 space-y-6 max-h-[calc(100vh-120px)] overflow-y-auto pr-2">
          {/* Status e Informações Básicas */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Status da Assinatura</CardTitle>
                {getStatusBadge(subscription.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Plano</label>
                  <p className="text-sm font-semibold">{subscription.planName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Valor</label>
                  <p className="text-sm font-semibold">{formatCurrency(subscription.planPrice)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">ID do Plano</label>
                  <p className="text-sm font-mono">{subscription.planId}</p>
                </div>
                <div className="col-span-3">
                  <label className="text-sm font-medium text-muted-foreground">ID Asaas</label>
                  <p className="text-sm font-mono break-all">{subscription.asaasSubscriptionId || "N/A"}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações do Paciente */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações do Paciente
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">{subscription.patient.user.name || "Nome não informado"}</p>
                    <p className="text-xs text-muted-foreground">Nome completo</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium break-all">{subscription.patient.user.email || "Email não informado"}</p>
                    <p className="text-xs text-muted-foreground">Endereço de email</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">{subscription.patient.user.phone || "Telefone não informado"}</p>
                    <p className="text-xs text-muted-foreground">Número de telefone</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Datas e Cronograma */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Cronograma
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Data de Início:</span>
                  <span className="text-sm">{formatDate(subscription.startDate)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Próxima Cobrança:</span>
                  <span className="text-sm">{formatDate(subscription.nextBillingDate)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Último Reset:</span>
                  <span className="text-sm">{formatDate(subscription.lastResetDate)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Criado em:</span>
                  <span className="text-sm">{formatDateTime(subscription.createdAt)}</span>
                </div>
                <div className="col-span-2 flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Atualizado em:</span>
                  <span className="text-sm">{formatDateTime(subscription.updatedAt)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Consultas */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Consultas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Incluídas</label>
                  <p className="text-2xl font-bold text-blue-600">{subscription.consultationsIncluded}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Utilizadas</label>
                  <p className="text-2xl font-bold text-green-600">{subscription.consultationsUsed}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Restantes</label>
                  <p className="text-2xl font-bold text-orange-600">{subscription.consultationsIncluded - subscription.consultationsUsed}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min((subscription.consultationsUsed / subscription.consultationsIncluded) * 100, 100)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-muted-foreground text-center">
                {subscription.consultationsUsed} de {subscription.consultationsIncluded} consultas utilizadas
              </p>
            </CardContent>
          </Card>

          {/* Pagamentos */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Pagamentos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Método de Pagamento:</span>
                  <span className="text-sm">{subscription.paymentMethod || "Não informado"}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Status do Último Pagamento:</span>
                  {getPaymentStatusBadge(subscription.lastPaymentStatus)}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-muted-foreground">Último Pagamento:</span>
                  <span className="text-sm">{formatDateTime(subscription.lastPaymentAt)}</span>
                </div>
                {subscription.failedPaymentAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-muted-foreground">Falha no Pagamento:</span>
                    <span className="text-sm text-red-600">{formatDateTime(subscription.failedPaymentAt)}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Ações */}
          <div className="flex gap-2 pt-4">
            <Button variant="outline" size="sm" className="flex-1">
              Editar Assinatura
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              Histórico de Pagamentos
            </Button>
            <Button variant="destructive" size="sm" className="flex-1">
              Cancelar
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
