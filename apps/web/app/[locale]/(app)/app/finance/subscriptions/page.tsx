import { Suspense } from "react";
import { db } from "database";
import { <PERSON>, CardContent, <PERSON>H<PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { currentUser } from "@saas/auth/lib/current-user";
import { PaginationButton } from "@ui/components/pagination-button";
import { SubscriptionsClient } from "./components/subscriptions-client";
import { redirect } from "@i18n/routing";
import { CreditCard, Users, DollarSign, TrendingUp, TrendingDown } from "lucide-react";

export const dynamic = "force-dynamic";

type SearchParams = {
  page?: string;
  search?: string;
  status?: string;
};

export default async function AdminSubscriptionsPage({
  searchParams,
}: { searchParams?: SearchParams }) {
  const session = await currentUser();
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect("/app/dashboard");
  }

  const page = Number(searchParams?.page || 1);
  const pageSize = 10;
  const skip = Math.max(0, (isNaN(page) ? 0 : page - 1) * pageSize);
  const search = (searchParams?.search || "").trim();
  const status = (searchParams?.status || "").trim().toUpperCase();

  const allowedStatuses = [
    "ACTIVE",
    "PAUSED",
    "CANCELED",
    "PAST_DUE",
    "UNPAID",
  ] as const;

  const where: any = {};
  if (status && (allowedStatuses as readonly string[]).includes(status)) {
    where.status = status;
  }
  if (search) {
    where.OR = [
      { planName: { contains: search, mode: "insensitive" } },
      { asaasSubscriptionId: { contains: search } },
      {
        patient: {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
      },
    ];
  }

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Consultas simplificadas para evitar erros
  const totalCount = await db.patientSubscription.count({ where });

  const subscriptions = await db.patientSubscription.findMany({
    where,
    skip,
    take: pageSize,
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      planId: true,
      planName: true,
      planPrice: true,
      status: true,
      startDate: true,
      nextBillingDate: true,
      asaasSubscriptionId: true,
      consultationsIncluded: true,
      consultationsUsed: true,
      lastResetDate: true,
      lastPaymentStatus: true,
      lastPaymentAt: true,
      failedPaymentAt: true,
      paymentMethod: true,
      createdAt: true,
      updatedAt: true,
      patient: {
        select: {
          id: true,
          user: { select: { name: true, email: true, phone: true } },
        },
      },
    },
  });

  const activeCount = await db.patientSubscription.count({ where: { status: "ACTIVE" } });
  const riskCount = await db.patientSubscription.count({ where: { status: { in: ["PAST_DUE", "UNPAID"] } } });

  const mrrAgg = await db.patientSubscription.aggregate({
    _sum: { planPrice: true },
    where: { status: "ACTIVE" },
  });

  const canceled30d = await db.patientSubscription.count({
    where: {
      status: "CANCELED",
      updatedAt: { gte: thirtyDaysAgo },
    },
  });

  const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));
  const mrr = Number(mrrAgg._sum.planPrice || 0);

  // Serialize Decimal -> number for client
  const serialized = subscriptions.map((s) => ({
    ...s,
    planPrice: Number(s.planPrice as any),
    transactions: [], // Removido para simplificar
    consultationUsages: [], // Removido para simplificar
  }));

  const metrics = {
    active: activeCount,
    risk: riskCount,
    mrr,
    canceled30d,
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <CreditCard className="h-6 w-6" />
          Assinaturas
        </h1>
        <p className="text-sm text-muted-foreground mt-2">
          Gerencie assinaturas e planos dos pacientes
        </p>
      </div>

      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Assinaturas Ativas
                </p>
                <h3 className="text-3xl font-bold text-green-600">
                  {metrics.active}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Total de assinaturas ativas
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  MRR
                </p>
                <h3 className="text-3xl font-bold text-blue-600">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL',
                  }).format(metrics.mrr)}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Receita recorrente mensal
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Em Risco
                </p>
                <h3 className="text-3xl font-bold text-orange-600">
                  {metrics.risk}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Inadimplência
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingDown className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Canceladas (30d)
                </p>
                <h3 className="text-3xl font-bold text-red-600">
                  {metrics.canceled30d}
                </h3>
                <p className="text-xs text-muted-foreground mt-2">
                  Últimos 30 dias
                </p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Assinaturas</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Carregando...</div>}>
            <SubscriptionsClient
              subscriptions={serialized as any}
              currentPage={!isNaN(page) ? page : 1}
              totalPages={totalPages}
              currentSearch={search}
              currentStatus={status}
            />
          </Suspense>
        </CardContent>
      </Card>

      {totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationButton
            currentPage={!isNaN(page) ? page : 1}
            totalPages={totalPages}
            baseUrl="/app/finance/subscriptions"
            searchParams={{ ...(search ? { search } : {}), ...(status ? { status } : {}) }}
          />
        </div>
      )}
    </div>
  );
}
