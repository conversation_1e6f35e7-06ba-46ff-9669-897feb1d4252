"use client";

import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/table";
import { Badge } from "@ui/components/badge";
import { Checkbox } from "@ui/components/checkbox";
import { SubscriptionDetailsSheet } from "./subscription-details-sheet";

type SubscriptionRow = {
  id: string;
  planId: string;
  planName: string;
  planPrice: number;
  status: string;
  startDate: string | Date | null;
  nextBillingDate: string | Date | null;
  asaasSubscriptionId: string | null;
  consultationsIncluded: number;
  consultationsUsed: number;
  lastResetDate: string | Date;
  lastPaymentStatus: string | null;
  lastPaymentAt: string | Date | null;
  failedPaymentAt: string | Date | null;
  paymentMethod: string | null;
  createdAt: string | Date;
  updatedAt: string | Date;
  patient: { id: string; user: { name: string | null; email: string | null; phone: string | null } };
};

export function SubscriptionsClient({
  subscriptions,
  currentPage,
  totalPages,
  currentSearch,
  currentStatus,
}: {
  subscriptions: SubscriptionRow[];
  currentPage: number;
  totalPages: number;
  currentSearch?: string;
  currentStatus?: string;
}) {
  // client-side URL helpers to avoid next/navigation types in lints
  const [search, setSearch] = useState<string>(currentSearch || "");
  const [status, setStatus] = useState<string>(currentStatus && currentStatus.length > 0 ? currentStatus : "ALL");
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [selectedSubscription, setSelectedSubscription] = useState<SubscriptionRow | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const goto = (page: number) => {
    const params = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : "");
    if (search) params.set("search", search); else params.delete("search");
    if (status && status !== "ALL") params.set("status", status); else params.delete("status");
    params.set("page", String(page));
    if (typeof window !== 'undefined') {
      window.location.assign(`/app/finance/subscriptions?${params.toString()}`);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(subscriptions.map(s => s.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectRow = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  const handleActionComplete = () => {
    // Recarregar a página para atualizar os dados
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  const handleRowClick = (subscription: SubscriptionRow) => {
    setSelectedSubscription(subscription);
    setIsSheetOpen(true);
  };

  const handleCloseSheet = () => {
    setIsSheetOpen(false);
    setSelectedSubscription(null);
  };

  const formatDate = (d: string | Date | null) => {
    if (!d) return "-";
    const date = typeof d === "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat("pt-BR", { dateStyle: "short" }).format(date);
  };

  const formatCurrency = (v: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(v || 0);

  const formatDateTime = (d: string | Date | null) => {
    if (!d) return "-";
    const date = typeof d === "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short"
    }).format(date);
  };

  const statusBadge = (s: string) => {
    const map: Record<string, string> = {
      ACTIVE: "bg-green-100 text-green-800",
      PAST_DUE: "bg-yellow-100 text-yellow-800",
      UNPAID: "bg-orange-100 text-orange-800",
      PAUSED: "bg-gray-100 text-gray-800",
      CANCELED: "bg-red-100 text-red-800",
      PENDING: "bg-blue-100 text-blue-800",
    };
    const labelMap: Record<string, string> = {
      ACTIVE: "Ativa",
      PAST_DUE: "Em atraso",
      UNPAID: "Não paga",
      PAUSED: "Pausada",
      CANCELED: "Cancelada",
      PENDING: "Pendente",
    };
    return (
      <Badge className={map[s] || "bg-gray-100 text-gray-800"}>
        {labelMap[s] || s}
      </Badge>
    );
  };


  return (
    <div className="space-y-4">
      {/* Filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Buscar por nome, email ou ID da assinatura..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                goto(1);
              }
            }}
          />
        </div>
        <div className="w-full sm:w-48">
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">Todos os status</SelectItem>
              <SelectItem value="ACTIVE">Ativa</SelectItem>
              <SelectItem value="PAUSED">Pausada</SelectItem>
              <SelectItem value="CANCELED">Cancelada</SelectItem>
              <SelectItem value="PAST_DUE">Em atraso</SelectItem>
              <SelectItem value="UNPAID">Não paga</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <Button onClick={() => goto(1)}>Filtrar</Button>
      </div>

      {/* Tabela */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedIds.length === subscriptions.length && subscriptions.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Paciente</TableHead>
              <TableHead>Plano</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Próxima Cobrança</TableHead>
              <TableHead>Consultas</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subscriptions.map((row) => (
              <TableRow
                key={row.id}
                className="cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => handleRowClick(row)}
              >
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedIds.includes(row.id)}
                    onCheckedChange={(checked) => handleSelectRow(row.id, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{row.patient.user.name || "Sem nome"}</div>
                    <div className="text-sm text-muted-foreground">{row.patient.user.email}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{row.planName}</div>
                    <div className="text-sm text-muted-foreground">ID: {row.planId}</div>
                  </div>
                </TableCell>
                <TableCell>{statusBadge(row.status)}</TableCell>
                <TableCell>{formatCurrency(row.planPrice)}</TableCell>
                <TableCell>{formatDate(row.nextBillingDate)}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    {row.consultationsUsed} / {row.consultationsIncluded}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Paginação */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => goto(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              Anterior
            </Button>
            <span className="text-sm text-muted-foreground">
              Página {currentPage} de {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => goto(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Próxima
            </Button>
          </div>
        </div>
      )}

      {/* Sheet de Detalhes */}
      <SubscriptionDetailsSheet
        isOpen={isSheetOpen}
        onClose={handleCloseSheet}
        subscription={selectedSubscription}
      />
    </div>
  );
}
