import { db } from "database";
import { notFound } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";

export const dynamic = "force-dynamic";

export default async function AdminSubscriptionDetail({ params }: { params: { id: string } }) {
  const sub = await db.patientSubscription.findUnique({
    where: { id: params.id },
    select: {
      id: true,
      planId: true,
      planName: true,
      planPrice: true,
      status: true,
      startDate: true,
      currentPeriodStart: true,
      currentPeriodEnd: true,
      nextBillingDate: true,
      asaasSubscriptionId: true,
      createdAt: true,
      updatedAt: true,
      patient: { select: { id: true, user: { select: { name: true, email: true } } } },
      transactions: {
        select: {
          id: true,
          amount: true,
          status: true,
          paymentMethod: true,
          dueDate: true,
          paidAt: true,
          createdAt: true,
        },
        orderBy: { createdAt: "desc" },
        take: 10,
      },
    },
  });

  if (!sub) return notFound();

  const formatCurrency = (v: any) => new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(Number(v || 0));
  const formatDate = (d: Date | null) => (d ? new Intl.DateTimeFormat("pt-BR", { dateStyle: "short" }).format(new Date(d)) : "-");

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Assinatura #{sub.id}</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div>
            <div className="text-sm text-muted-foreground">Paciente</div>
            <div className="font-medium">{sub.patient.user.name || "Paciente"}</div>
            <div className="text-xs text-muted-foreground">{sub.patient.user.email || "-"}</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Plano</div>
            <div className="font-medium">{sub.planName} ({sub.planId})</div>
            <div className="text-xs text-muted-foreground">{formatCurrency(sub.planPrice)}</div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Status</div>
            <div className="font-medium">
              <Badge>{sub.status}</Badge>
            </div>
          </div>
          <div>
            <div className="text-sm text-muted-foreground">Próxima cobrança</div>
            <div className="font-medium">{formatDate(sub.nextBillingDate as any)}</div>
          </div>
          {sub.asaasSubscriptionId && (
            <div>
              <div className="text-sm text-muted-foreground">Asaas Subscription ID</div>
              <div className="font-mono text-xs">{sub.asaasSubscriptionId}</div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Últimas faturas</CardTitle>
        </CardHeader>
        <CardContent>
          {sub.transactions.length === 0 ? (
            <div className="text-sm text-muted-foreground">Nenhuma fatura encontrada</div>
          ) : (
            <div className="divide-y">
              {sub.transactions.map((t) => (
                <div key={t.id} className="py-3 grid grid-cols-2 md:grid-cols-5 gap-2 text-sm">
                  <div className="font-mono text-xs">{t.id}</div>
                  <div>{formatCurrency(t.amount)}</div>
                  <div>{new Intl.DateTimeFormat("pt-BR", { dateStyle: "short" }).format(new Date(t.dueDate))}</div>
                  <div>{t.status}</div>
                  <div>{t.paymentMethod}</div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


