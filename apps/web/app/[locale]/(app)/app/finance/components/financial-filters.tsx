'use client';

import { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Card, CardContent } from "@ui/components/card";
import { Filter, RefreshCw } from "lucide-react";
import { PaymentStatusSchema, PaymentMethodSchema, type PaymentStatusType, type PaymentMethodType } from 'database';
import { ExportButton } from './export-button';

interface FinancialFiltersProps {
  onFiltersChange: (filters: {
    period: string;
    status: string;
    method: string;
  }) => void;
  onRefresh?: () => void;
  loading?: boolean;
  showDoctorFilter?: boolean;
  doctors?: Array<{ id: string; name: string }>;
  selectedDoctorId?: string;
  onDoctorChange?: (doctorId: string) => void;
  exportData?: any[];
  exportFilename?: string;
}

const PERIOD_OPTIONS = [
  { value: 'today', label: 'Hoje' },
  { value: '7days', label: 'Últimos 7 dias' },
  { value: '30days', label: 'Últimos 30 dias' },
  { value: '3months', label: 'Últimos 3 meses' },
  { value: '6months', label: 'Últimos 6 meses' },
  { value: '1year', label: 'Último ano' },
];

const STATUS_OPTIONS = [
  { value: 'all', label: 'Todos os status' },
  { value: 'PAID', label: 'Pago' },
  { value: 'PENDING', label: 'Pendente' },
  { value: 'FAILED', label: 'Falhou' },
  { value: 'REFUNDED', label: 'Reembolsado' },
];

const METHOD_OPTIONS = [
  { value: 'all', label: 'Métodos' },
  { value: 'CREDIT_CARD', label: 'Cartão de Crédito' },
  { value: 'PIX', label: 'PIX' },
  { value: 'BOLETO', label: 'Boleto' },
];

export function FinancialFilters({
  onFiltersChange,
  onRefresh,
  loading = false,
  showDoctorFilter = false,
  doctors = [],
  selectedDoctorId = 'all',
  onDoctorChange,
  exportData = [],
  exportFilename = 'financial-data',
}: FinancialFiltersProps) {
  const [filters, setFilters] = useState({
    period: '30days',
    status: 'all',
    method: 'all',
  });

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4 md:items-center">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Filtros:</span>
          </div>

          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 flex-1">
            {/* Filtro de Período */}
            <Select
              value={filters.period}
              onValueChange={(value) => handleFilterChange('period', value)}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Período" />
              </SelectTrigger>
              <SelectContent>
                {PERIOD_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Filtro de Status */}
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                {STATUS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Filtro de Método de Pagamento */}
            <Select
              value={filters.method}
              onValueChange={(value) => handleFilterChange('method', value)}
            >
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Método" />
              </SelectTrigger>
              <SelectContent>
                {METHOD_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Filtro de Médico (apenas para admin) */}
            {showDoctorFilter && (
              <Select
                value={selectedDoctorId}
                onValueChange={onDoctorChange}
              >
                <SelectTrigger className="w-full md:w-[200px]">
                  <SelectValue placeholder="Médico" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os médicos</SelectItem>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      {doctor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Botões de Ação */}
          <div className="flex space-x-2">
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            )}

            <ExportButton
              data={exportData}
              filename={exportFilename}
              disabled={loading}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
