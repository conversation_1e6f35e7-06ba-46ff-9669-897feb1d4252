'use client';

import { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { 
  AlertTriangle, 
  CreditCard, 
  DollarSign, 
  Info,
  CheckCircle,
  Loader2
} from "lucide-react";
import { toast } from "sonner";

interface BankAccount {
  id: string;
  bankName: string;
  accountType: string;
  accountNumber: string;
  agency: string;
  holderName: string;
  holderDocument: string;
  isDefault: boolean;
}

interface WithdrawalRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableBalance: number;
  bankAccounts: BankAccount[];
  onSubmit: (data: WithdrawalRequestData) => Promise<void>;
}

interface WithdrawalRequestData {
  amount: number;
  bankAccountId: string;
  notes?: string;
}

const WITHDRAWAL_LIMITS = {
  min: 50,
  max: 10000,
  fee: 0.02, // 2%
  processingDays: 2
};

export function WithdrawalRequestModal({
  isOpen,
  onClose,
  availableBalance,
  bankAccounts,
  onSubmit
}: WithdrawalRequestModalProps) {
  const [step, setStep] = useState<'form' | 'confirmation' | 'success'>('form');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<WithdrawalRequestData>({
    amount: 0,
    bankAccountId: '',
    notes: ''
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const calculateFee = (amount: number) => {
    return amount * WITHDRAWAL_LIMITS.fee;
  };

  const calculateNetAmount = (amount: number) => {
    return amount - calculateFee(amount);
  };

  const validateAmount = (amount: number) => {
    if (amount < WITHDRAWAL_LIMITS.min) {
      return `Valor mínimo: ${formatCurrency(WITHDRAWAL_LIMITS.min)}`;
    }
    if (amount > WITHDRAWAL_LIMITS.max) {
      return `Valor máximo: ${formatCurrency(WITHDRAWAL_LIMITS.max)}`;
    }
    if (amount > availableBalance) {
      return 'Valor superior ao saldo disponível';
    }
    return null;
  };

  const handleAmountChange = (value: string) => {
    const numericValue = parseFloat(value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
    setFormData(prev => ({ ...prev, amount: numericValue }));
  };

  const handleSubmit = async () => {
    const error = validateAmount(formData.amount);
    if (error) {
      toast.error(error);
      return;
    }

    if (!formData.bankAccountId) {
      toast.error('Selecione uma conta bancária');
      return;
    }

    setStep('confirmation');
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      await onSubmit(formData);
      setStep('success');
    } catch (error) {
      toast.error('Erro ao processar solicitação de saque');
      console.error('Withdrawal request error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setStep('form');
    setFormData({ amount: 0, bankAccountId: '', notes: '' });
    onClose();
  };

  const selectedAccount = bankAccounts.find(acc => acc.id === formData.bankAccountId);
  const amountError = validateAmount(formData.amount);
  const fee = calculateFee(formData.amount);
  const netAmount = calculateNetAmount(formData.amount);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        {step === 'form' && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                Solicitar Saque
              </DialogTitle>
              <DialogDescription>
                Solicite o saque do seu saldo disponível para sua conta bancária.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Available Balance Info */}
              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-green-700">Saldo Disponível</span>
                    <span className="font-semibold text-green-800">
                      {formatCurrency(availableBalance)}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Amount Input */}
              <div className="space-y-2">
                <Label htmlFor="amount">Valor do Saque</Label>
                <Input
                  id="amount"
                  type="text"
                  placeholder="R$ 0,00"
                  value={formData.amount > 0 ? formatCurrency(formData.amount) : ''}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  className={amountError ? 'border-red-500' : ''}
                />
                {amountError && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    {amountError}
                  </p>
                )}
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Mín: {formatCurrency(WITHDRAWAL_LIMITS.min)}</span>
                  <span>Máx: {formatCurrency(WITHDRAWAL_LIMITS.max)}</span>
                </div>
              </div>

              {/* Bank Account Selection */}
              <div className="space-y-2">
                <Label htmlFor="bankAccount">Conta Bancária</Label>
                <Select value={formData.bankAccountId} onValueChange={(value) => 
                  setFormData(prev => ({ ...prev, bankAccountId: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma conta bancária" />
                  </SelectTrigger>
                  <SelectContent>
                    {bankAccounts.map((account) => (
                      <SelectItem key={account.id} value={account.id}>
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          <div>
                            <div className="font-medium">{account.bankName}</div>
                            <div className="text-xs text-muted-foreground">
                              {account.accountType} • ••••{account.accountNumber.slice(-4)}
                              {account.isDefault && (
                                <Badge variant="secondary" className="ml-1 text-xs">
                                  Padrão
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Observações (opcional)</Label>
                <Textarea
                  id="notes"
                  placeholder="Adicione observações sobre este saque..."
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>

              {/* Fee Information */}
              {formData.amount > 0 && !amountError && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Valor solicitado:</span>
                        <span>{formatCurrency(formData.amount)}</span>
                      </div>
                      <div className="flex justify-between text-muted-foreground">
                        <span>Taxa de processamento (2%):</span>
                        <span>- {formatCurrency(fee)}</span>
                      </div>
                      <hr className="border-blue-200" />
                      <div className="flex justify-between font-semibold text-blue-800">
                        <span>Valor líquido:</span>
                        <span>{formatCurrency(netAmount)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Processing Info */}
              <div className="flex items-start gap-2 p-3 bg-gray-50 rounded-lg">
                <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                <div className="text-xs text-muted-foreground">
                  <p className="font-medium text-gray-700 mb-1">Informações importantes:</p>
                  <ul className="space-y-1">
                    <li>• Processamento em até {WITHDRAWAL_LIMITS.processingDays} dias úteis</li>
                    <li>• Taxa de {(WITHDRAWAL_LIMITS.fee * 100).toFixed(0)}% sobre o valor solicitado</li>
                    <li>• Transferência via TED/PIX conforme conta selecionada</li>
                  </ul>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button 
                onClick={handleSubmit}
                disabled={!formData.amount || !formData.bankAccountId || !!amountError}
              >
                Continuar
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Confirmar Solicitação
              </DialogTitle>
              <DialogDescription>
                Revise os dados da sua solicitação de saque antes de confirmar.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <Card>
                <CardContent className="p-4 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Valor solicitado:</span>
                    <span className="font-semibold">{formatCurrency(formData.amount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Taxa de processamento:</span>
                    <span className="text-red-600">- {formatCurrency(fee)}</span>
                  </div>
                  <hr />
                  <div className="flex justify-between">
                    <span className="font-semibold">Valor líquido:</span>
                    <span className="font-semibold text-green-600">{formatCurrency(netAmount)}</span>
                  </div>
                </CardContent>
              </Card>

              {selectedAccount && (
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <CreditCard className="h-5 w-5 text-gray-600" />
                      <div>
                        <div className="font-medium">{selectedAccount.bankName}</div>
                        <div className="text-sm text-muted-foreground">
                          {selectedAccount.holderName}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {selectedAccount.accountType} • Ag: {selectedAccount.agency} • 
                          Conta: ••••{selectedAccount.accountNumber.slice(-4)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {formData.notes && (
                <Card>
                  <CardContent className="p-4">
                    <div className="text-sm">
                      <span className="font-medium">Observações:</span>
                      <p className="text-muted-foreground mt-1">{formData.notes}</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setStep('form')}>
                Voltar
              </Button>
              <Button onClick={handleConfirm} disabled={loading}>
                {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Confirmar Saque
              </Button>
            </DialogFooter>
          </>
        )}

        {step === 'success' && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                Solicitação Enviada!
              </DialogTitle>
              <DialogDescription>
                Sua solicitação de saque foi enviada com sucesso.
              </DialogDescription>
            </DialogHeader>

            <div className="text-center py-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Solicitação Processada</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Você receberá {formatCurrency(netAmount)} em sua conta bancária em até {WITHDRAWAL_LIMITS.processingDays} dias úteis.
              </p>
              <p className="text-xs text-muted-foreground">
                Você pode acompanhar o status na seção de histórico de saques.
              </p>
            </div>

            <DialogFooter>
              <Button onClick={handleClose} className="w-full">
                Fechar
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
