'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { 
  Download, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  Calendar,
  CreditCard
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@ui/components/dialog";

interface WithdrawalRecord {
  id: string;
  amount: number;
  netAmount: number;
  fee: number;
  status: 'pending' | 'processing' | 'completed' | 'rejected' | 'cancelled';
  requestedAt: string;
  processedAt?: string;
  completedAt?: string;
  bankAccount: {
    bankName: string;
    accountType: string;
    accountNumber: string;
    agency: string;
    holderName: string;
  };
  notes?: string;
  rejectionReason?: string;
  transactionId?: string;
}

interface WithdrawalHistoryProps {
  withdrawals: WithdrawalRecord[];
  loading?: boolean;
  onRefresh?: () => void;
}

export function WithdrawalHistory({ 
  withdrawals, 
  loading = false, 
  onRefresh 
}: WithdrawalHistoryProps) {
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRecord | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          label: 'Concluído',
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: CheckCircle,
          iconColor: 'text-green-600'
        };
      case 'processing':
        return {
          label: 'Processando',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: Clock,
          iconColor: 'text-blue-600'
        };
      case 'pending':
        return {
          label: 'Pendente',
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: AlertCircle,
          iconColor: 'text-yellow-600'
        };
      case 'rejected':
        return {
          label: 'Rejeitado',
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: XCircle,
          iconColor: 'text-red-600'
        };
      case 'cancelled':
        return {
          label: 'Cancelado',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: XCircle,
          iconColor: 'text-gray-600'
        };
      default:
        return {
          label: status,
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: AlertCircle,
          iconColor: 'text-gray-600'
        };
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Histórico de Saques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-2">
                    <div className="h-4 bg-muted rounded w-32"></div>
                    <div className="h-3 bg-muted rounded w-24"></div>
                  </div>
                  <div className="h-6 bg-muted rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Histórico de Saques
          </CardTitle>
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              Atualizar
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {withdrawals.length === 0 ? (
            <div className="text-center py-8">
              <Download className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-semibold text-muted-foreground mb-2">
                Nenhum saque realizado
              </h3>
              <p className="text-sm text-muted-foreground">
                Seus saques aparecerão aqui quando forem solicitados.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {withdrawals.map((withdrawal) => {
                const statusConfig = getStatusConfig(withdrawal.status);
                const StatusIcon = statusConfig.icon;

                return (
                  <div
                    key={withdrawal.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-full bg-gray-100`}>
                        <StatusIcon className={`h-4 w-4 ${statusConfig.iconColor}`} />
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold">
                            {formatCurrency(withdrawal.netAmount)}
                          </span>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${statusConfig.color}`}
                          >
                            {statusConfig.label}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(withdrawal.requestedAt)}</span>
                          </div>
                          
                          <div className="flex items-center space-x-1">
                            <CreditCard className="h-3 w-3" />
                            <span>
                              {withdrawal.bankAccount.bankName} • 
                              ••••{withdrawal.bankAccount.accountNumber.slice(-4)}
                            </span>
                          </div>
                        </div>

                        {withdrawal.status === 'rejected' && withdrawal.rejectionReason && (
                          <div className="text-xs text-red-600 mt-1">
                            Motivo: {withdrawal.rejectionReason}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {formatCurrency(withdrawal.amount)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Taxa: {formatCurrency(withdrawal.fee)}
                        </div>
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedWithdrawal(withdrawal)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Withdrawal Details Modal */}
      <Dialog 
        open={!!selectedWithdrawal} 
        onOpenChange={() => setSelectedWithdrawal(null)}
      >
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Detalhes do Saque
            </DialogTitle>
            <DialogDescription>
              Informações completas sobre a solicitação de saque.
            </DialogDescription>
          </DialogHeader>

          {selectedWithdrawal && (
            <div className="space-y-4">
              {/* Status and Amount */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <Badge 
                      variant="outline" 
                      className={getStatusConfig(selectedWithdrawal.status).color}
                    >
                      {getStatusConfig(selectedWithdrawal.status).label}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      ID: {selectedWithdrawal.id.slice(-8)}
                    </span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Valor solicitado:</span>
                      <span className="font-semibold">{formatCurrency(selectedWithdrawal.amount)}</span>
                    </div>
                    <div className="flex justify-between text-muted-foreground">
                      <span>Taxa de processamento:</span>
                      <span>- {formatCurrency(selectedWithdrawal.fee)}</span>
                    </div>
                    <hr />
                    <div className="flex justify-between font-semibold">
                      <span>Valor líquido:</span>
                      <span className="text-green-600">{formatCurrency(selectedWithdrawal.netAmount)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Bank Account */}
              <Card>
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Conta Bancária
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Banco:</span>
                      <span>{selectedWithdrawal.bankAccount.bankName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Titular:</span>
                      <span>{selectedWithdrawal.bankAccount.holderName}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tipo:</span>
                      <span>{selectedWithdrawal.bankAccount.accountType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Agência:</span>
                      <span>{selectedWithdrawal.bankAccount.agency}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Conta:</span>
                      <span>••••{selectedWithdrawal.bankAccount.accountNumber.slice(-4)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Card>
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Timeline
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <div>
                        <div className="font-medium">Solicitação criada</div>
                        <div className="text-muted-foreground">
                          {formatDate(selectedWithdrawal.requestedAt)}
                        </div>
                      </div>
                    </div>
                    
                    {selectedWithdrawal.processedAt && (
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                        <div>
                          <div className="font-medium">Em processamento</div>
                          <div className="text-muted-foreground">
                            {formatDate(selectedWithdrawal.processedAt)}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {selectedWithdrawal.completedAt && (
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                        <div>
                          <div className="font-medium">Concluído</div>
                          <div className="text-muted-foreground">
                            {formatDate(selectedWithdrawal.completedAt)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Notes */}
              {selectedWithdrawal.notes && (
                <Card>
                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-2">Observações</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedWithdrawal.notes}
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Rejection Reason */}
              {selectedWithdrawal.rejectionReason && (
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-2 text-red-800 flex items-center gap-2">
                      <XCircle className="h-4 w-4" />
                      Motivo da Rejeição
                    </h4>
                    <p className="text-sm text-red-700">
                      {selectedWithdrawal.rejectionReason}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
