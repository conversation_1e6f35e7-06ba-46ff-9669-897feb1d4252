'use client';

import { useState } from 'react';
import { Button } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Card, CardContent } from "@ui/components/card";
import { Filter, RefreshCw } from "lucide-react";
import { ExportButton } from './export-button';

interface SimplifiedFinancialFiltersProps {
  onFiltersChange: (filters: {
    period: string;
    status: string;
  }) => void;
  onRefresh?: () => void;
  loading?: boolean;
  exportData?: any[];
  exportFilename?: string;
}

const PERIOD_OPTIONS = [
  { value: '7days', label: 'Últimos 7 dias' },
  { value: '30days', label: 'Últimos 30 dias' },
  { value: '3months', label: 'Últimos 3 meses' },
  { value: '6months', label: 'Últimos 6 meses' },
  { value: '1year', label: 'Último ano' },
];

const STATUS_OPTIONS = [
  { value: 'all', label: 'Todos os status' },
  { value: 'PAID', label: 'Pago' },
  { value: 'PENDING', label: 'Pendente' },
  { value: 'FAILED', label: 'Falhou' },
  { value: 'REFUNDED', label: 'Reembolsado' },
];

export function SimplifiedFinancialFilters({
  onFiltersChange,
  onRefresh,
  loading = false,
  exportData = [],
  exportFilename = 'financial-data',
}: SimplifiedFinancialFiltersProps) {
  const [filters, setFilters] = useState({
    period: '30days',
    status: 'all',
  });

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center sm:justify-between">
          {/* Left side - Filters */}
          <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filtros:</span>
            </div>

            {/* Period Filter */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground min-w-[60px]">Período:</span>
              <Select
                value={filters.period}
                onValueChange={(value) => handleFilterChange('period', value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PERIOD_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground min-w-[45px]">Status:</span>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger className="w-[160px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-2">
            <ExportButton
              data={exportData}
              filename={exportFilename}
              disabled={loading}
            />
            
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
