'use client';

import { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  Settings,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from "lucide-react";
import { toast } from "sonner";

interface DevelopmentToolsProps {
  doctorId: string;
  currentStatus: string;
  onStatusChange: () => void;
}

export function DevelopmentTools({
  doctorId,
  currentStatus,
  onStatusChange
}: DevelopmentToolsProps) {
  const [loading, setLoading] = useState<string | null>(null);

  const handleStatusChange = async (newStatus: string) => {
    setLoading(newStatus);
    try {
      const response = await fetch('/api/finance/doctor/bank-account/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doctorId,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao atualizar status');
      }

      toast.success(`Status alterado para ${newStatus}`);
      onStatusChange();
    } catch (error) {
      toast.error('Erro ao atualizar status da conta');
      console.error('Status update error:', error);
    } finally {
      setLoading(null);
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return { color: 'bg-green-100 text-green-800', icon: CheckCircle };
      case 'PENDING_VERIFICATION':
        return { color: 'bg-yellow-100 text-yellow-800', icon: Clock };
      case 'REJECTED':
        return { color: 'bg-red-100 text-red-800', icon: XCircle };
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: AlertTriangle };
    }
  };

  const statusInfo = getStatusInfo(currentStatus);
  const StatusIcon = statusInfo.icon;

  // Só mostrar em desenvolvimento
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card className="border-dashed border-2 border-orange-200 bg-orange-50/50">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-orange-800 flex items-center gap-2">
          <Settings className="h-4 w-4" />
          Ferramentas de Desenvolvimento
        </CardTitle>
        <p className="text-xs text-orange-700">
          Apenas para testes - não aparecerá em produção
        </p>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <StatusIcon className="h-4 w-4" />
              <span className="text-sm font-medium">Status Atual:</span>
              <Badge className={`${statusInfo.color} text-xs`}>
                {currentStatus}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleStatusChange('PENDING_VERIFICATION')}
              disabled={loading === 'PENDING_VERIFICATION' || currentStatus === 'PENDING_VERIFICATION'}
              className="text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Pendente
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleStatusChange('APPROVED')}
              disabled={loading === 'APPROVED' || currentStatus === 'APPROVED'}
              className="text-xs text-green-600 border-green-600 hover:bg-green-50"
            >
              <CheckCircle className="h-3 w-3 mr-1" />
              Aprovar
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleStatusChange('REJECTED')}
              disabled={loading === 'REJECTED' || currentStatus === 'REJECTED'}
              className="text-xs text-red-600 border-red-600 hover:bg-red-50"
            >
              <XCircle className="h-3 w-3 mr-1" />
              Rejeitar
            </Button>
          </div>

          <div className="text-xs text-orange-700 bg-orange-100/50 p-2 rounded">
            <p className="font-medium mb-1">Como testar:</p>
            <ul className="space-y-1 text-xs">
              <li>• <strong>Pendente:</strong> Mostra que está sendo verificado</li>
              <li>• <strong>Aprovado:</strong> Permite solicitar saques</li>
              <li>• <strong>Rejeitado:</strong> Mostra erro e permite reconfigurar</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
