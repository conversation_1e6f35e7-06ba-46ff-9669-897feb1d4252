'use client';

import { Card, CardContent } from "@ui/components/card";
import { LucideIcon, TrendingUp, TrendingDown, Activity } from "lucide-react";
import { cn } from "@ui/lib";

interface FinancialMetricsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: string;
  trendDirection?: 'up' | 'down' | 'neutral';
  description?: string;
  className?: string;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
}

export function FinancialMetricsCard({
  title,
  value,
  icon: Icon,
  trend,
  trendDirection = 'neutral',
  description,
  className,
  color = 'blue',
}: FinancialMetricsCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      // Se for um valor monetário (maior que 1000), formatar como moeda
      if (val >= 1000) {
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(val);
      }
      // Se for um percentual (menor que 100 e tem trend), adicionar %
      if (val < 100 && trend) {
        return `${val}%`;
      }
      return val.toString();
    }
    return val;
  };

  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return {
          border: 'border-l-blue-500',
          text: 'text-blue-600',
          gradient: 'from-blue-500 to-blue-600',
        };
      case 'green':
        return {
          border: 'border-l-green-500',
          text: 'text-green-600',
          gradient: 'from-green-500 to-green-600',
        };
      case 'purple':
        return {
          border: 'border-l-purple-500',
          text: 'text-purple-600',
          gradient: 'from-purple-500 to-purple-600',
        };
      case 'orange':
        return {
          border: 'border-l-orange-500',
          text: 'text-orange-600',
          gradient: 'from-orange-500 to-orange-600',
        };
      case 'red':
        return {
          border: 'border-l-red-500',
          text: 'text-red-600',
          gradient: 'from-red-500 to-red-600',
        };
      default:
        return {
          border: 'border-l-blue-500',
          text: 'text-blue-600',
          gradient: 'from-blue-500 to-blue-600',
        };
    }
  };

  const getTrendColor = () => {
    switch (trendDirection) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  const getTrendIcon = () => {
    switch (trendDirection) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Activity className="h-3 w-3" />;
    }
  };

  const colorClasses = getColorClasses();

  return (
    <Card className={cn(
      "hover:shadow-lg transition-all duration-300 border-l-4",
      colorClasses.border,
      className
    )}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">
              {title}
            </p>
            <h3 className={cn("text-3xl font-bold", colorClasses.text)}>
              {formatValue(value)}
            </h3>
            {(trend || description) && (
              <div className="flex items-center mt-2 text-xs text-muted-foreground">
                {trend && (
                  <div className={cn("flex items-center mr-2", getTrendColor())}>
                    {getTrendIcon()}
                    <span className="ml-1">{trend}</span>
                  </div>
                )}
                {description && (
                  <span>{description}</span>
                )}
              </div>
            )}
          </div>
          <div className={cn(
            "h-12 w-12 bg-gradient-to-br rounded-xl flex items-center justify-center shadow-lg",
            `bg-gradient-to-br ${colorClasses.gradient}`
          )}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
