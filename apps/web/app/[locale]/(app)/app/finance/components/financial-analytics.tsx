'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  DollarSign, 
  Users, 
  Target,
  BarChart3,
  PieChart,
  Download
} from "lucide-react";
import { cn } from "@ui/lib";

interface FinancialAnalyticsData {
  monthlyGrowth: {
    revenue: number;
    appointments: number;
    averageTicket: number;
  };
  topPerformingPeriods: Array<{
    period: string;
    revenue: number;
    appointments: number;
  }>;
  paymentMethodAnalysis: Array<{
    method: string;
    percentage: number;
    revenue: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  revenueGoals: {
    monthly: {
      target: number;
      current: number;
      percentage: number;
    };
    yearly: {
      target: number;
      current: number;
      percentage: number;
    };
  };
  insights: Array<{
    type: 'positive' | 'negative' | 'neutral';
    title: string;
    description: string;
    value?: string;
  }>;
}

interface FinancialAnalyticsProps {
  data: FinancialAnalyticsData;
  loading?: boolean;
  onExportReport?: () => void;
}

export function FinancialAnalytics({ 
  data, 
  loading = false, 
  onExportReport 
}: FinancialAnalyticsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'positive':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'negative':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'positive':
        return <TrendingUp className="h-4 w-4" />;
      case 'negative':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-600" />;
      default:
        return <div className="h-3 w-3 bg-gray-400 rounded-full" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 w-48 bg-muted animate-pulse rounded" />
          <div className="h-10 w-32 bg-muted animate-pulse rounded" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-3">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-8 bg-muted rounded w-1/2" />
                  <div className="h-3 bg-muted rounded w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold">Análise Financeira</h2>
          <p className="text-muted-foreground">
            Insights detalhados sobre seu desempenho financeiro
          </p>
        </div>
        
        {onExportReport && (
          <Button onClick={onExportReport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar Relatório
          </Button>
        )}
      </div>

      {/* Growth Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Crescimento da Receita
                </p>
                <h3 className="text-2xl font-bold text-green-600">
                  {formatPercentage(data.monthlyGrowth.revenue)}
                </h3>
                <p className="text-xs text-muted-foreground">vs. mês anterior</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Crescimento de Consultas
                </p>
                <h3 className="text-2xl font-bold text-blue-600">
                  {formatPercentage(data.monthlyGrowth.appointments)}
                </h3>
                <p className="text-xs text-muted-foreground">vs. mês anterior</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Users className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  Ticket Médio
                </p>
                <h3 className="text-2xl font-bold text-purple-600">
                  {formatPercentage(data.monthlyGrowth.averageTicket)}
                </h3>
                <p className="text-xs text-muted-foreground">vs. mês anterior</p>
              </div>
              <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Goals */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Meta Mensal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Progresso</span>
                <span className="font-semibold">
                  {data.revenueGoals.monthly.percentage.toFixed(1)}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(data.revenueGoals.monthly.percentage, 100)}%` }}
                />
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Atual: {formatCurrency(data.revenueGoals.monthly.current)}
                </span>
                <span className="font-medium">
                  Meta: {formatCurrency(data.revenueGoals.monthly.target)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Meta Anual
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Progresso</span>
                <span className="font-semibold">
                  {data.revenueGoals.yearly.percentage.toFixed(1)}%
                </span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(data.revenueGoals.yearly.percentage, 100)}%` }}
                />
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">
                  Atual: {formatCurrency(data.revenueGoals.yearly.current)}
                </span>
                <span className="font-medium">
                  Meta: {formatCurrency(data.revenueGoals.yearly.target)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Method Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Análise de Métodos de Pagamento
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.paymentMethodAnalysis.map((method, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{method.method}</span>
                    {getTrendIcon(method.trend)}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {method.percentage.toFixed(1)}%
                  </Badge>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{formatCurrency(method.revenue)}</div>
                  <div className="text-xs text-muted-foreground">receita</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Insights Financeiros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.insights.map((insight, index) => (
              <div 
                key={index} 
                className={cn(
                  "flex items-start space-x-3 p-4 rounded-lg border",
                  getInsightColor(insight.type)
                )}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {getInsightIcon(insight.type)}
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold mb-1">{insight.title}</h4>
                  <p className="text-sm opacity-90">{insight.description}</p>
                  {insight.value && (
                    <div className="mt-2 font-semibold">{insight.value}</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
