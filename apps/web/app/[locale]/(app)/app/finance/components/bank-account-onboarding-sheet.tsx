'use client';

import { useState } from 'react';
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@ui/components/sheet";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import {
  CreditCard,
  DollarSign,
  Info,
  CheckCircle,
  Loader2,
  AlertTriangle,
  Building2,
  User,
  FileText
} from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Schema de validação para dados bancários
const bankAccountSchema = z.object({
  bankName: z.string().min(1, "Nome do banco é obrigatório"),
  bankCode: z.string().min(3, "Código do banco é obrigatório"),
  accountType: z.enum(["CHECKING", "SAVINGS"], {
    required_error: "Tipo de conta é obrigatório"
  }),
  agency: z.string().min(1, "Agência é obrigatória"),
  accountNumber: z.string().min(1, "Número da conta é obrigatório"),
  holderName: z.string().min(1, "Nome do titular é obrigatório"),
  holderDocument: z.string().min(11, "CPF/CNPJ é obrigatório"),
  isDefault: z.boolean().default(true)
});

type BankAccountFormData = z.infer<typeof bankAccountSchema>;

interface BankAccountOnboardingSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// Lista de bancos brasileiros mais comuns
const BRAZILIAN_BANKS = [
  { code: "001", name: "Banco do Brasil" },
  { code: "341", name: "Itaú Unibanco" },
  { code: "033", name: "Santander" },
  { code: "104", name: "Caixa Econômica Federal" },
  { code: "237", name: "Bradesco" },
  { code: "260", name: "Nu Pagamentos (Nubank)" },
  { code: "336", name: "Banco C6" },
  { code: "422", name: "Banco Safra" },
  { code: "070", name: "BRB - Banco de Brasília" },
  { code: "756", name: "Sicoob" },
  { code: "748", name: "Sicredi" },
  { code: "041", name: "Banrisul" },
  { code: "077", name: "Banco Inter" },
  { code: "212", name: "Banco Original" },
  { code: "290", name: "PagSeguro" },
  { code: "323", name: "Mercado Pago" },
];

export function BankAccountOnboardingSheet({
  isOpen,
  onClose,
  onSuccess
}: BankAccountOnboardingSheetProps) {
  const [step, setStep] = useState<'form' | 'confirmation' | 'success'>('form');
  const [loading, setLoading] = useState(false);

  const form = useForm<BankAccountFormData>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      bankName: "",
      bankCode: "",
      accountType: "CHECKING",
      agency: "",
      accountNumber: "",
      holderName: "",
      holderDocument: "",
      isDefault: true
    }
  });

  const handleBankCodeChange = (bankCode: string) => {
    const bank = BRAZILIAN_BANKS.find(b => b.code === bankCode);
    if (bank) {
      form.setValue('bankCode', bankCode);
      form.setValue('bankName', bank.name);
    }
  };

  const handleDocumentChange = (value: string) => {
    // Remove caracteres não numéricos
    const numericValue = value.replace(/\D/g, '');

    // Formatar CPF (11 dígitos) ou CNPJ (14 dígitos)
    let formattedValue = numericValue;
    if (numericValue.length <= 11) {
      // Formatar como CPF: 000.000.000-00
      formattedValue = numericValue.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
    } else {
      // Formatar como CNPJ: 00.000.000/0000-00
      formattedValue = numericValue.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
    }

    form.setValue('holderDocument', formattedValue);
  };

  const onSubmit = async (data: BankAccountFormData) => {
    setLoading(true);
    try {
      const response = await fetch('/api/finance/doctor/bank-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Erro ao salvar dados bancários');
      }

      setStep('success');
      onSuccess();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Erro ao salvar dados bancários');
      console.error('Bank account setup error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setStep('form');
    form.reset();
    onClose();
  };

  const handleConfirm = () => {
    form.handleSubmit(onSubmit)();
  };

  const selectedBank = BRAZILIAN_BANKS.find(b => b.code === form.watch('bankCode'));

  return (
    <Sheet open={isOpen} onOpenChange={handleClose}>
      <SheetContent className="sm:max-w-[600px] overflow-y-auto">
        {step === 'form' && (
          <>
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-blue-600" />
                Configurar Conta Bancária
              </SheetTitle>
              <SheetDescription>
                Configure sua conta bancária para receber pagamentos e solicitar saques.
                <br />
                <span className="text-sm text-muted-foreground">
                  Você pode atender pacientes sem configurar, mas precisará desta informação para sacar seus ganhos.
                </span>
              </SheetDescription>
            </SheetHeader>

            <div className="space-y-6 py-6">
              {/* Informação importante */}
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-800 mb-1">
                        Por que preciso configurar minha conta bancária?
                      </p>
                      <ul className="text-blue-700 space-y-1">
                        <li>• Para receber pagamentos das consultas</li>
                        <li>• Para solicitar saques do seu saldo</li>
                        <li>• Para cumprir com as regulamentações financeiras</li>
                        <li>• Você pode atender pacientes mesmo sem configurar</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <form onSubmit={form.handleSubmit(() => setStep('confirmation'))} className="space-y-4">
                {/* Seleção do Banco */}
                <div className="space-y-2">
                  <Label htmlFor="bankCode">Banco</Label>
                  <Select value={form.watch('bankCode')} onValueChange={handleBankCodeChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione seu banco" />
                    </SelectTrigger>
                    <SelectContent>
                      {BRAZILIAN_BANKS.map((bank) => (
                        <SelectItem key={bank.code} value={bank.code}>
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4" />
                            <div>
                              <div className="font-medium">{bank.name}</div>
                              <div className="text-xs text-muted-foreground">
                                Código: {bank.code}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {form.formState.errors.bankCode && (
                    <p className="text-sm text-red-600">{form.formState.errors.bankCode.message}</p>
                  )}
                </div>

                {/* Tipo de Conta */}
                <div className="space-y-2">
                  <Label htmlFor="accountType">Tipo de Conta</Label>
                  <Select value={form.watch('accountType')} onValueChange={(value) =>
                    form.setValue('accountType', value as "CHECKING" | "SAVINGS")
                  }>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CHECKING">Conta Corrente</SelectItem>
                      <SelectItem value="SAVINGS">Conta Poupança</SelectItem>
                    </SelectContent>
                  </Select>
                  {form.formState.errors.accountType && (
                    <p className="text-sm text-red-600">{form.formState.errors.accountType.message}</p>
                  )}
                </div>

                {/* Agência e Conta */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="agency">Agência</Label>
                    <Input
                      id="agency"
                      placeholder="Ex: 1234"
                      {...form.register('agency')}
                    />
                    {form.formState.errors.agency && (
                      <p className="text-sm text-red-600">{form.formState.errors.agency.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountNumber">Conta</Label>
                    <Input
                      id="accountNumber"
                      placeholder="Ex: 12345-6"
                      {...form.register('accountNumber')}
                    />
                    {form.formState.errors.accountNumber && (
                      <p className="text-sm text-red-600">{form.formState.errors.accountNumber.message}</p>
                    )}
                  </div>
                </div>

                {/* Dados do Titular */}
                <div className="space-y-2">
                  <Label htmlFor="holderName">Nome do Titular</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="holderName"
                      placeholder="Nome completo do titular da conta"
                      className="pl-10"
                      {...form.register('holderName')}
                    />
                  </div>
                  {form.formState.errors.holderName && (
                    <p className="text-sm text-red-600">{form.formState.errors.holderName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="holderDocument">CPF/CNPJ do Titular</Label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="holderDocument"
                      placeholder="000.000.000-00 ou 00.000.000/0000-00"
                      className="pl-10"
                      value={form.watch('holderDocument')}
                      onChange={(e) => handleDocumentChange(e.target.value)}
                    />
                  </div>
                  {form.formState.errors.holderDocument && (
                    <p className="text-sm text-red-600">{form.formState.errors.holderDocument.message}</p>
                  )}
                </div>

                {/* Informações de Segurança */}
                <Card className="bg-amber-50 border-amber-200">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                      <div className="text-sm">
                        <p className="font-medium text-amber-800 mb-1">
                          Informações de Segurança
                        </p>
                        <ul className="text-amber-700 space-y-1">
                          <li>• Seus dados bancários são criptografados e seguros</li>
                          <li>• Usamos apenas para processar seus pagamentos</li>
                          <li>• Você pode alterar essas informações a qualquer momento</li>
                          <li>• Verificamos a titularidade da conta antes de aprovar saques</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </form>
            </div>

            <SheetFooter>
              <Button variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button
                onClick={() => setStep('confirmation')}
                disabled={!form.formState.isValid}
              >
                Continuar
              </Button>
            </SheetFooter>
          </>
        )}

        {step === 'confirmation' && (
          <>
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                Confirmar Dados Bancários
              </SheetTitle>
              <SheetDescription>
                Revise os dados da sua conta bancária antes de confirmar.
              </SheetDescription>
            </SheetHeader>

            <div className="space-y-4 py-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <Building2 className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">{selectedBank?.name}</div>
                      <div className="text-sm text-muted-foreground">
                        Código: {form.watch('bankCode')}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Tipo:</span>
                      <div className="font-medium">
                        {form.watch('accountType') === 'CHECKING' ? 'Conta Corrente' : 'Conta Poupança'}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Agência:</span>
                      <div className="font-medium">{form.watch('agency')}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Conta:</span>
                      <div className="font-medium">{form.watch('accountNumber')}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Titular:</span>
                      <div className="font-medium">{form.watch('holderName')}</div>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t">
                    <span className="text-muted-foreground text-sm">Documento:</span>
                    <div className="font-medium">{form.watch('holderDocument')}</div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-green-800 mb-1">
                        Próximos Passos
                      </p>
                      <ul className="text-green-700 space-y-1">
                        <li>• Sua conta será verificada em até 2 dias úteis</li>
                        <li>• Você receberá um email de confirmação</li>
                        <li>• Após aprovação, poderá solicitar saques</li>
                        <li>• Continue atendendo pacientes normalmente</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <SheetFooter>
              <Button variant="outline" onClick={() => setStep('form')}>
                Voltar
              </Button>
              <Button onClick={handleConfirm} disabled={loading}>
                {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Confirmar e Salvar
              </Button>
            </SheetFooter>
          </>
        )}

        {step === 'success' && (
          <>
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-5 w-5" />
                Conta Configurada!
              </SheetTitle>
              <SheetDescription>
                Sua conta bancária foi configurada com sucesso.
              </SheetDescription>
            </SheetHeader>

            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">Configuração Concluída</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Sua conta bancária foi salva e está sendo verificada.
                <br />
                Você receberá um email de confirmação em breve.
              </p>
              <div className="space-y-2 text-xs text-muted-foreground">
                <p>• Continue atendendo pacientes normalmente</p>
                <p>• Após aprovação, poderá solicitar saques</p>
                <p>• Verifique seu email para atualizações</p>
              </div>
            </div>

            <SheetFooter>
              <Button onClick={handleClose} className="w-full">
                Fechar
              </Button>
            </SheetFooter>
          </>
        )}
      </SheetContent>
    </Sheet>
  );
}
