'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { PaymentMethod } from 'database';

interface PaymentMethodData {
  method: PaymentMethod;
  count: number;
  amount: number;
}

interface PaymentMethodChartProps {
  data: PaymentMethodData[];
  height?: number;
  showAmount?: boolean;
}

const PAYMENT_METHOD_LABELS = {
  CREDIT_CARD: 'Cartão de Crédito',
  PIX: 'PIX',
  BOLETO: 'Boleto',
  DEBIT_CARD: 'Cartão de Débito',
};

const COLORS = {
  CREDIT_CARD: '#3b82f6',
  PIX: '#10b981',
  BOLETO: '#f59e0b',
  DEBIT_CARD: '#8b5cf6',
};

export function PaymentMethodChart({ 
  data, 
  height = 300, 
  showAmount = false 
}: PaymentMethodChartProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const chartData = data.map(item => ({
    name: PAYMENT_METHOD_LABELS[item.method] || item.method,
    value: showAmount ? item.amount : item.count,
    count: item.count,
    amount: item.amount,
    method: item.method,
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium mb-1">{data.name}</p>
          <p className="text-sm text-muted-foreground">
            Transações: {data.count}
          </p>
          <p className="text-sm text-muted-foreground">
            Valor: {formatCurrency(data.amount)}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    if (percent < 0.05) return null; // Não mostrar label se for menos de 5%
    
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        className="text-xs font-medium"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-[300px] text-muted-foreground">
        Nenhum dado disponível para o período selecionado
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={CustomLabel}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {chartData.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={COLORS[entry.method as PaymentMethod] || '#6b7280'} 
            />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
        <Legend 
          verticalAlign="bottom" 
          height={36}
          formatter={(value, entry: any) => (
            <span style={{ color: entry.color }}>
              {value}
            </span>
          )}
        />
      </PieChart>
    </ResponsiveContainer>
  );
}
