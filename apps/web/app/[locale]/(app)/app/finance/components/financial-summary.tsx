'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface FinancialSummaryProps {
  title: string;
  data: {
    current: number;
    previous: number;
    label: string;
    format?: 'currency' | 'number' | 'percentage';
  }[];
  className?: string;
}

export function FinancialSummary({ title, data, className }: FinancialSummaryProps) {
  const formatValue = (value: number, format: 'currency' | 'number' | 'percentage' = 'number') => {
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
        }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString('pt-BR');
    }
  };

  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return { percentage: 0, direction: 'neutral' as const };
    
    const percentage = ((current - previous) / previous) * 100;
    const direction = percentage > 0 ? 'up' : percentage < 0 ? 'down' : 'neutral';
    
    return { percentage: Math.abs(percentage), direction };
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return <Minus className="h-3 w-3" />;
    }
  };

  const getTrendColor = (direction: 'up' | 'down' | 'neutral') => {
    switch (direction) {
      case 'up':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'down':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item, index) => {
            const change = calculateChange(item.current, item.previous);
            
            return (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-card">
                <div className="flex-1">
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {item.label}
                  </p>
                  <p className="text-2xl font-bold">
                    {formatValue(item.current, item.format)}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant="outline" 
                    className={`${getTrendColor(change.direction)} flex items-center space-x-1`}
                  >
                    {getTrendIcon(change.direction)}
                    <span className="text-xs font-medium">
                      {change.percentage.toFixed(1)}%
                    </span>
                  </Badge>
                </div>
              </div>
            );
          })}
        </div>
        
        {data.length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            Nenhum dado disponível para o período selecionado
          </div>
        )}
      </CardContent>
    </Card>
  );
}
