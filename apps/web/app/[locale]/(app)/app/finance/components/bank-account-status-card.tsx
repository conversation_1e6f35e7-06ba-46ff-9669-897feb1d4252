'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  CreditCard,
  CheckCircle,
  Clock,
  AlertTriangle,
  Settings,
  ExternalLink
} from "lucide-react";

interface BankAccountStatusCardProps {
  bankAccountStatus: string;
  bankAccounts: Array<{
    id: string;
    bankName: string;
    accountType: string;
    accountNumber: string;
    agency: string;
    holderName: string;
    status?: string;
  }>;
  onConfigureAccount: () => void;
  onViewDetails: () => void;
}

export function BankAccountStatusCard({
  bankAccountStatus,
  bankAccounts,
  onConfigureAccount,
  onViewDetails
}: BankAccountStatusCardProps) {

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          badge: 'bg-green-100 text-green-800',
          title: 'Conta Aprovada',
          description: 'Sua conta bancária está verificada e aprovada para saques.',
          action: 'Ver Detalhes'
        };
      case 'PENDING_VERIFICATION':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          badge: 'bg-yellow-100 text-yellow-800',
          title: 'Verificação Pendente',
          description: 'Sua conta está sendo verificada. Aguarde até 2 dias úteis.',
          action: 'Ver Status'
        };
      case 'REJECTED':
        return {
          icon: AlertTriangle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          badge: 'bg-red-100 text-red-800',
          title: 'Conta Rejeitada',
          description: 'Sua conta foi rejeitada. Verifique os dados e tente novamente.',
          action: 'Reconfigurar'
        };
      default:
        return {
          icon: CreditCard,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          badge: 'bg-blue-100 text-blue-800',
          title: 'Conta Não Configurada',
          description: 'Configure sua conta bancária para receber pagamentos e solicitar saques.',
          action: 'Configurar Conta'
        };
    }
  };

  const statusInfo = getStatusInfo(bankAccountStatus);
  const StatusIcon = statusInfo.icon;
  const hasBankAccount = bankAccounts.length > 0;
  const primaryAccount = bankAccounts[0];

  return (
    <Card className={`${statusInfo.bgColor} ${statusInfo.borderColor} border-l-4`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${statusInfo.bgColor}`}>
              <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                {statusInfo.title}
              </CardTitle>
              <Badge className={`${statusInfo.badge} text-xs`}>
                {bankAccountStatus === 'NOT_CONFIGURED' ? 'Não Configurado' :
                 bankAccountStatus === 'PENDING_VERIFICATION' ? 'Pendente' :
                 bankAccountStatus === 'APPROVED' ? 'Aprovado' :
                 bankAccountStatus === 'REJECTED' ? 'Rejeitado' : 'Desconhecido'}
              </Badge>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onConfigureAccount}
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            {bankAccountStatus === 'NOT_CONFIGURED' ? 'Configurar' : 'Gerenciar'}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-4">
          {statusInfo.description}
        </p>

        {hasBankAccount && primaryAccount && (
          <div className="bg-white/50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium text-sm">Conta Principal</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onViewDetails}
                className="h-8 px-2 text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Ver
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">Banco:</span>
                <div className="font-medium">{primaryAccount.bankName || 'N/A'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Tipo:</span>
                <div className="font-medium">{primaryAccount.accountType || 'N/A'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Agência:</span>
                <div className="font-medium">{primaryAccount.agency || 'N/A'}</div>
              </div>
              <div>
                <span className="text-muted-foreground">Conta:</span>
                <div className="font-medium">
                  {primaryAccount.accountNumber ?
                    `****${primaryAccount.accountNumber.slice(-4)}` :
                    'N/A'
                  }
                </div>
              </div>
            </div>
          </div>
        )}

        {bankAccountStatus === 'PENDING_VERIFICATION' && (
          <div className="bg-yellow-100/50 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <Clock className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-xs text-yellow-800">
                <p className="font-medium mb-1">Verificação em Andamento</p>
                <p>Nossa equipe está verificando seus dados bancários. Você receberá um email quando a verificação for concluída.</p>
              </div>
            </div>
          </div>
        )}

        {bankAccountStatus === 'REJECTED' && (
          <div className="bg-red-100/50 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
              <div className="text-xs text-red-800">
                <p className="font-medium mb-1">Conta Rejeitada</p>
                <p>Verifique se os dados estão corretos e tente configurar novamente. Entre em contato conosco se precisar de ajuda.</p>
              </div>
            </div>
          </div>
        )}

        {bankAccountStatus === 'NOT_CONFIGURED' && (
          <div className="bg-blue-100/50 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <CreditCard className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-xs text-blue-800">
                <p className="font-medium mb-1">Configure sua Conta</p>
                <p>Você pode atender pacientes normalmente, mas precisará configurar sua conta bancária para solicitar saques dos seus ganhos.</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
