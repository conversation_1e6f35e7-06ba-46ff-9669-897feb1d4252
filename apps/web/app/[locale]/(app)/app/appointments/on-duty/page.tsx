
import { Button } from '@ui/components/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Badge } from '@ui/components/badge';
import { db } from 'database';
import { DashboardHeader } from '@ui/components/header';

// Tipos de componentes - podem ser movidos para um arquivo separado
interface PageProps {
  params: { locale: string };
  searchParams?: {
    page?: string;
    status?: string;
  };
}

export default async function OnDutyAppointmentsPage({ searchParams = {} }: PageProps) {
  // Verificar se o usuário é um médico
  // No futuro, adicionar lógica para verificar se o médico está registrado para plantão

  try {
    // Buscar consultas de plantão na fila
    // Este é um exemplo de como poderia ser feito - necessário adaptar para o modelo real
    const onDutyQueue = await db.appointment.findMany({
      where: {
        isOnDuty: true,
        status: 'PENDING',
      },
      orderBy: [
        {
          urgencyLevel: 'desc', // Priorizar por nível de urgência
        },
        {
          createdAt: 'asc', // Dentro do mesmo nível, priorizar pela ordem de criação
        },
      ],
      include: {
        patient: {
          include: {
            user: {
              select: {
                name: true,
                avatarUrl: true,
              },
            },
          },
        },
      },
    });

    // Função de utilitário para mapear nível de urgência para texto e cor
    const getUrgencyInfo = (level: string) => {
      switch (level) {
        case 'high':
          return { name: 'Muito Urgente', color: 'bg-red-500 text-white' };
        case 'medium':
          return { name: 'Urgente', color: 'bg-amber-500 text-white' };
        case 'low':
          return { name: 'Pouco Urgente', color: 'bg-green-500 text-white' };
        default:
          return { name: 'Normal', color: 'bg-blue-500 text-white' };
      }
    };

    // Agrupar consultas por nível de urgência
    const highUrgency = onDutyQueue.filter(a => a.urgencyLevel === 'high');
    const mediumUrgency = onDutyQueue.filter(a => a.urgencyLevel === 'medium');
    const lowUrgency = onDutyQueue.filter(a => a.urgencyLevel === 'low');

    return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <DashboardHeader heading="Plantão Médico" text="Fila de atendimento por urgência." />
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">Todos ({onDutyQueue.length})</TabsTrigger>
            <TabsTrigger value="high" className="flex gap-2">
              Muito Urgente
              {highUrgency.length > 0 && (
                <Badge variant="destructive">{highUrgency.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="medium">
              Urgente
              {mediumUrgency.length > 0 && (
                <Badge variant="warning">{mediumUrgency.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="low">
              Pouco Urgente
              {lowUrgency.length > 0 && (
                <Badge variant="success">{lowUrgency.length}</Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4 mt-4">
            {onDutyQueue.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <p className="text-muted-foreground text-center mb-4">
                    Não há pacientes na fila de plantão no momento.
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Quando um paciente solicitar uma consulta de plantão, ela aparecerá aqui.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {onDutyQueue.map((appointment) => (
                  <Card key={appointment.id} className="overflow-hidden">
                    <CardHeader className={`py-3 ${getUrgencyInfo(appointment.urgencyLevel || '').color}`}>
                      <CardTitle className="text-sm flex justify-between items-center">
                        <span>{getUrgencyInfo(appointment.urgencyLevel || '').name}</span>
                        <span className="text-xs opacity-90">
                          {new Date(appointment.createdAt).toLocaleTimeString('pt-BR', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {appointment.patient.user.avatarUrl ? (
                            <img
                              src={appointment.patient.user.avatarUrl}
                              alt={appointment.patient.user.name || 'Paciente'}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <span className="text-lg font-semibold text-gray-500">
                              {(appointment.patient.user.name || 'P').charAt(0)}
                            </span>
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">{appointment.patient.user.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Aguardando desde {new Date(appointment.createdAt).toLocaleTimeString('pt-BR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      </div>
                      <div className="mt-4 flex justify-end">
                        <Button size="sm">
                          Iniciar Atendimento
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="high" className="space-y-4 mt-4">
            {highUrgency.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <p className="text-muted-foreground">
                    Não há pacientes com urgência alta na fila.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {highUrgency.map((appointment) => (
                  <Card key={appointment.id} className="overflow-hidden">
                    <CardHeader className="py-3 bg-red-500 text-white">
                      <CardTitle className="text-sm flex justify-between items-center">
                        <span>Muito Urgente</span>
                        <span className="text-xs opacity-90">
                          {new Date(appointment.createdAt).toLocaleTimeString('pt-BR', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                          {appointment.patient.user.avatarUrl ? (
                            <img
                              src={appointment.patient.user.avatarUrl}
                              alt={appointment.patient.user.name || 'Paciente'}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <span className="text-lg font-semibold text-gray-500">
                              {(appointment.patient.user.name || 'P').charAt(0)}
                            </span>
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium">{appointment.patient.user.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Aguardando desde {new Date(appointment.createdAt).toLocaleTimeString('pt-BR', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </p>
                        </div>
                      </div>
                      <div className="mt-4 flex justify-end">
                        <Button size="sm">
                          Iniciar Atendimento
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Conteúdos para as outras abas seguem o mesmo padrão */}
          <TabsContent value="medium" className="space-y-4 mt-4">
            {/* Similar ao conteúdo "high" mas para urgência média */}
            {mediumUrgency.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <p className="text-muted-foreground">
                    Não há pacientes com urgência média na fila.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Mapear as consultas de urgência média */}
              </div>
            )}
          </TabsContent>

          <TabsContent value="low" className="space-y-4 mt-4">
            {/* Similar ao conteúdo "high" mas para urgência baixa */}
            {lowUrgency.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-10">
                  <p className="text-muted-foreground">
                    Não há pacientes com urgência baixa na fila.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Mapear as consultas de urgência baixa */}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    );
  } catch (error) {
    console.error("Error loading on-duty appointments:", error);
    return (
      <div className="container mx-auto p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Erro ao carregar fila de plantão</h2>
        <p className="text-muted-foreground mb-6">
          Ocorreu um erro ao tentar carregar a fila de plantão. Por favor,
          tente novamente mais tarde.
        </p>
      </div>
    );
  }
}
