import { DashboardHeader } from "@ui/components/header";
import { AppointmentCardSkeleton } from "./components/appointment-card-skeleton";

export default function AppointmentsLoading() {
	return (
		<>
			<DashboardHeader
				heading="Consultas"
				text="<PERSON><PERSON><PERSON><PERSON> todas as consultas do sistema."
			/>
			<div className="grid grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
				{Array.from({ length: 9 }).map((_, i) => (
					<AppointmentCardSkeleton key={i} />
				))}
			</div>
		</>
	);
}
