"use client";

import { useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { FileText, Download, Eye, EyeOff, RefreshCw } from "lucide-react";

interface PDFPreviewProps {
  file: File;
  onRemove: () => void;
  onReplace: (file: File) => void;
  className?: string;
}

export function PDFPreview({
  file,
  onRemove,
  onReplace,
  className
}: PDFPreviewProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handlePreviewToggle = async () => {
    if (!showPreview) {
      setIsLoadingPreview(true);
      try {
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);
        setShowPreview(true);
      } catch (error) {
        console.error("Erro ao gerar preview:", error);
      } finally {
        setIsLoadingPreview(false);
      }
    } else {
      setShowPreview(false);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    }
  };

  const handleDownload = () => {
    const url = URL.createObjectURL(file);
    const a = document.createElement("a");
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleReplaceClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onReplace(files[0]);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <FileText className="w-5 h-5 text-blue-600" />
            PDF Selecionado
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File Info */}
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900 truncate max-w-xs">
                  {file.name}
                </p>
                <p className="text-sm text-gray-500">
                  {formatFileSize(file.size)}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handlePreviewToggle}
              disabled={isLoadingPreview}
              className="flex items-center gap-2"
            >
              {isLoadingPreview ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : showPreview ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
              {isLoadingPreview ? "Carregando..." : showPreview ? "Ocultar" : "Visualizar"}
            </Button>

            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDownload}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Download
            </Button>

            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleReplaceClick}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Substituir
            </Button>

            <Button
              type="button"
              variant="destructive"
              size="sm"
              onClick={onRemove}
            >
              Remover
            </Button>
          </div>

          {/* PDF Preview */}
          {showPreview && previewUrl && (
            <div className="border rounded-lg overflow-hidden">
              <iframe
                src={previewUrl}
                className="w-full h-96"
                title="Preview do PDF"
              />
            </div>
          )}

          {/* Hidden File Input for Replace */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf,application/pdf"
            onChange={handleFileChange}
            className="hidden"
          />
        </CardContent>
      </Card>
    </div>
  );
}
