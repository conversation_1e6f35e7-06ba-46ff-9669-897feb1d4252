import { AppointmentDetails } from "../components/appointment-details";
import { createApiCaller } from "api/trpc/caller";

async function fetchAppointment(id: string) {
	const apiCaller = await createApiCaller();

	// Use the new appointment by ID procedure
	return apiCaller.appointments.getAppointmentById({ id });
}

export default async function AppointmentPage({
	params,
}: { params: { id: string } }) {
	// Buscar dados da consulta usando o ID
	const appointment = await fetchAppointment(params.id);

	return (
		<div className="container mx-auto py-6">
			<AppointmentDetails appointment={appointment} />
		</div>
	);
}
