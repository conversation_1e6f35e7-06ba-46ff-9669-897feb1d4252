'use server';

import { createApiCaller } from "api/trpc/caller";
import type { AppointmentStatus } from "@prisma/client";
import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";

export async function fetchAppointments(options: {
  page: number;
  perPage: number;
  status?: AppointmentStatus;
  startDate?: string;
}) {
  try {
    console.log("[Server Action - fetchAppointments] Options:", options);

    // Get current user for debugging purposes
    const { user } = await currentUser();
    console.log(`[Server Action - fetchAppointments] User: ${user?.id}, Role: ${user?.role}`);

    // Check for doctor profile if user is a doctor
    if (user?.role === "DOCTOR") {
      const doctorProfile = await db.doctor.findUnique({
        where: { userId: user.id },
        select: { id: true, userId: true, crm: true }
      });

      console.log(`[Server Action - fetchAppointments] Doctor profile lookup:`, doctorProfile);

      if (!doctorProfile) {
        console.log(`[Server Action - fetchAppointments] WARNING: No doctor profile found for userId ${user.id}`);

        // Check if there are any doctors in the database for debugging
        const sampleDoctor = await db.doctor.findFirst({
          select: { id: true, userId: true }
        });
        console.log(`[Server Action - fetchAppointments] Sample doctor in database:`, sampleDoctor);

        return {
          success: false,
          error: "Perfil de médico não encontrado. Entre em contato com o suporte."
        };
      }
    }

    const apiCaller = await createApiCaller();
    const result = await apiCaller.appointments.list(options);

    console.log(`[Server Action - fetchAppointments] Success: Found ${result.appointments.length} appointments`);
    return { success: true, data: result.appointments };
  } catch (error) {
    console.error("[Server Action - fetchAppointments] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch appointments"
    };
  }
}
