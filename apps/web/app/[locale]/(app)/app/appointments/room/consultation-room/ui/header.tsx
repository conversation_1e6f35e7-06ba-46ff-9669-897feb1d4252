import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { ArrowLeft, CheckCircle, Loader2, Video, VideoOff } from "lucide-react";
// components/consuluitation-room/header.tsx
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";

import { toast } from "sonner";
import { useMediaQuery } from "../hooks/use-media-query";
import { MedicalSheet } from "../medical/medical-sheet";

interface ConsultationHeaderProps {
	appointment: {
		id: string;
		scheduledAt: string;
		status?: string;
		doctor: {
			user: {
				name: string;
				image?: string;
			};
		};
		patient: {
			user: {
				name: string;
				image?: string;
			};
		};
		medicalRecord?: {
			mainComplaint?: string;
			diagnosis?: string;
			conduct?: string;
		};
		prescription?: {
			id: string;
			content?: any;
			status: string;
		};
	};
	userRole: "DOCTOR" | "PATIENT";
	showVideo: boolean;
	isActive: boolean;
	isCompleted?: boolean;
	isLoading?: boolean;
	onToggleVideo: () => void;
	onDisconnect: () => void;
	onCompleteAppointment?: () => void;
	statusUpdateTime?: Date; // Novo: hora da última atualização de status
}

export function ConsultationHeader({
	appointment,
	userRole,
	showVideo,
	isActive,
	isCompleted = false,
	isLoading = false,
	onToggleVideo,
	onDisconnect,
	onCompleteAppointment,
	statusUpdateTime,
}: ConsultationHeaderProps) {
	const isDoctor = userRole === "DOCTOR";
	const otherUser = isDoctor
		? appointment.patient.user
		: appointment.doctor.user;

	const router = useRouter();

	const handleBack = () => {
		router.back();
	};

	const { isMobile } = useMediaQuery();

	// Função wrapper para adicionar logs ao onToggleVideo
	const handleToggleVideo = () => {
		console.log("🔍 ConsultationHeader: botão Iniciar/Entrar Consulta clicado");
		console.log("isDoctor:", isDoctor, "showVideo:", showVideo, "isActive:", isActive, "isCompleted:", isCompleted);

		// Chamar a função original
		if (onToggleVideo) {
			onToggleVideo();
		} else {
			console.error("🔴 ERROR: onToggleVideo não está definido!");
		}
	};

	// Novo: Efeito para notificar sobre alterações importantes no status da consulta
	useEffect(() => {
		if (!statusUpdateTime) return;

		// Se o paciente recebe uma atualização e a consulta está ativa,
		// mostrar uma notificação mais visível
		if (!isDoctor && isActive && !isCompleted && !showVideo) {
			toast.success("O médico iniciou a consulta", {
				duration: 5000,
				action: {
					label: "Entrar agora",
					onClick: onToggleVideo,
				},
			});
		}
		// Se a consulta foi encerrada
		else if (isCompleted && showVideo) {
			const message = isDoctor
				? "Você encerrou a consulta"
				: "A consulta foi finalizada pelo médico";
			toast.info(message, {
				duration: 5000,
				action: isDoctor ? {
					label: "Reiniciar",
					onClick: onToggleVideo,
				} : undefined,
			});
			// Desconectar automaticamente do vídeo
			onDisconnect();
		}
	}, [isActive, isCompleted, showVideo, statusUpdateTime, isDoctor, onToggleVideo, onDisconnect]);

	const handleSaveNotes = async (notes: string) => {
		try {
			const response = await fetch(
				`/api/appointments/${appointment.id}/notes`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ content: notes }),
				},
			);

			if (!response.ok) {
				throw new Error("Failed to save notes");
			}

			toast.success("Anotações salvas com sucesso");
		} catch (error) {
			console.error("Error saving notes:", error);
			toast.error("Erro ao salvar anotações");
		}
	};

	const handleCreatePrescription = async () => {
		try {
			const response = await fetch(
				`/api/appointments/${appointment.id}/prescription`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);

			if (!response.ok) {
				throw new Error("Failed to create prescription");
			}

			toast.success("Prescrição criada com sucesso");
			// Trigger Memed integration
			if (typeof window !== 'undefined' && window.MdHub) {
				window.MdHub.command.send("webview:prescription:create");
			}
		} catch (error) {
			console.error("Error creating prescription:", error);
			toast.error("Erro ao criar prescrição");
		}
	};

	// Determine appointment status text and badge variant
	const getStatusInfo = () => {
		if (isLoading) {
			return { text: "Carregando...", variant: "outline" };
		} else if (isCompleted) {
			return { text: "Finalizada", variant: "secondary" };
		} else if (isActive && showVideo) {
			return { text: "Em Andamento", variant: "default" };
		} else if (isActive) {
			return { text: "Aguardando Participantes", variant: "default" };
		} else {
			return { text: "Agendada", variant: "outline" };
		}
	};

	const statusInfo = getStatusInfo();

	// Log dos valores para depuração quando o componente renderiza
	console.log("🔄 ConsultationHeader rendering:", {
		appointmentId: appointment.id,
		isDoctor,
		showVideo,
		isActive,
		isCompleted,
		isLoading
	});

	return (
		<div className="mb-4 flex items-center justify-between border-b py-4">
			<div className="mr-4 border-r border-gray-200">
				<Button variant="ghost" onClick={handleBack}>
					<ArrowLeft className="h-4 w-4" />
					{!isMobile && <span className="ml-2">Voltar</span>}
				</Button>
			</div>
			<div className="flex w-full items-center justify-between">
				<div className="flex items-center gap-4">
					<Avatar>
						<AvatarImage src={otherUser.image} />
						<AvatarFallback>{otherUser.name.substring(0, 2)}</AvatarFallback>
					</Avatar>

					<div>
						<div className="flex items-center gap-2">
							<h1 className="font-semibold">
								{isDoctor ? otherUser.name : `${otherUser.name}`}
							</h1>
							<Badge className={statusInfo.variant === "default" ? "" : statusInfo.variant === "secondary" ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
								{statusInfo.text}
							</Badge>
						</div>

						<p className="text-sm text-muted-foreground">
							{appointment?.scheduledAt &&
								format(
									new Date(appointment.scheduledAt),
									"dd 'de' MMMM', às' HH:mm",
									{
										locale: ptBR,
									},
								)}
						</p>
					</div>
				</div>

				{/* Botões de controle para o médico */}
				{isDoctor && (
					<div className="flex items-center gap-2">
						<MedicalSheet
							appointment={appointment}
							onCreatePrescription={handleCreatePrescription}
						/>

						{/* Botão de encerrar consulta */}
						{isDoctor && isActive && !isCompleted && onCompleteAppointment && showVideo && (
							<Button
								variant="outline"
								className="mr-2"
								onClick={onCompleteAppointment}
								disabled={isLoading}
							>
								{isLoading ? (
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								) : (
									<CheckCircle className="mr-2 h-4 w-4" />
								)}
								{!isMobile && "Encerrar Consulta"}
							</Button>
						)}

						{/* Botão de reiniciar consulta */}
						{isCompleted && !showVideo && (
							<Button
								variant="default"
								onClick={onToggleVideo}
								disabled={isLoading}
							>
								{isLoading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										{!isMobile && <span>Carregando...</span>}
									</>
								) : (
									<>
										<Video className="mr-2 h-4 w-4" />
										{!isMobile && "Reiniciar Consulta"}
									</>
								)}
							</Button>
						)}

						{/* Botão de desconexão */}
						{showVideo && !isCompleted && (
							<Button
								variant="error"
								onClick={onDisconnect}
								disabled={isLoading}
							>
								{isLoading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										{!isMobile && <span>Carregando...</span>}
									</>
								) : (
									<>
										<VideoOff className="mr-2 h-4 w-4" />
										{!isMobile && <span>Desconectar</span>}
									</>
								)}
							</Button>
						)}
					</div>
				)}

				{/* Botões para o paciente */}
				{!isDoctor && (
					<div className="flex items-center gap-2">


						{/* Botão de desconexão quando já estiver na chamada */}
						{showVideo && !isCompleted && (
							<Button
								variant="error"
								onClick={onDisconnect}
								disabled={isLoading}
							>
								{isLoading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										{!isMobile && <span>Carregando...</span>}
									</>
								) : (
									<>
										<VideoOff className="mr-2 h-4 w-4" />
										{!isMobile && <span>Desconectar</span>}
									</>
								)}
							</Button>
						)}
					</div>
				)}
			</div>
		</div>
	);
}

// Add window.MdHub type declaration
declare global {
  interface Window {
    MdHub?: {
      command: {
        send: (command: string) => void;
      };
    };
  }
}
