// app/[locale]/(saas)/app/chats/[id]/page.tsx
import { currentUser } from "@saas/auth/lib/current-user";
import { getAppointment } from "../../../../../../../actions/appointments/get-appointment";
import { ConsultationRoom } from "../consultation-room";
import { UserRole } from "../consultation-room/types";

interface AppointmentParams {
  id: string;
}

export default async function DigitalRoomPage({
  params,
}: any) {
  // Resolve os parâmetros assincronamente
  const resolvedParams = params instanceof Promise ? await params : params;
  const { id } = resolvedParams;

  if (!id) {
    return null;
  }

  const session = await currentUser();
  const appointmentData = await getAppointment(id);

  if (!appointmentData) {
    return <div>Consulta não encontrada</div>;
  }

  // Adaptar o formato do appointment para o formato esperado pelo componente
  const appointment = {
    id: appointmentData.id,
    scheduled_at: appointmentData.scheduledAt.toISOString(),
    doctor: {
      user: {
        id: appointmentData.doctor.user.id,
        name: appointmentData.doctor.user.name || "",
        image: appointmentData.doctor.user.avatarUrl || undefined,
      }
    },
    patient: {
      user: {
        id: appointmentData.patient.user.id,
        name: appointmentData.patient.user.name || "",
        image: appointmentData.patient.user.avatarUrl || undefined,
      }
    }
  };

  // Converter o tipo de UserRole do banco para o tipo esperado pelo componente
  const userRole: UserRole = (session?.user?.role === "DOCTOR" || session?.user?.role === "PATIENT")
    ? session.user.role as UserRole
    : "PATIENT";

  return (
    <ConsultationRoom
      appointment={appointment}
      userRole={userRole}
    />
  );
}
