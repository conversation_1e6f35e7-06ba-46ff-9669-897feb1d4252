// components/consultation-room/types.ts

export interface User {
    id: string;
    name: string;
    image?: string;
  }

  export interface Appointment {
    id: string;
    scheduled_at: string;
    doctor: {
      user: User;
    };
    patient: {
      user: User;
    };
  }

  export type UserRole = "DOCTOR" | "PATIENT";

  export interface Message {
    id: string;
    content: string;
    type: "TEXT" | "AUDIO" | "FILE";
    senderId: string;
    sender_name: string;
    createdAt: string;
    metadata?: {
      fileName?: string;
      contentType?: string;
      size?: number;
    };
  }
