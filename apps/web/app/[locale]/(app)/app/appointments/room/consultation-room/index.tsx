// components/consultation-room/index.tsx
"use client";

import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";
import { Button } from "@ui/components/button";
import { Video, Loader2 } from "lucide-react";

import { ChatInterface } from "./chat/chat-interface";
import { MobileLayout } from "./layout/mobile-consultation-layout";
import { ConsultationHeader } from "./ui/header";
import { CustomPreJoin } from "./video/custom-pre-join";
import { CustomVideoRoom } from "./video/custom-video-room";
import { RealtimeDebug } from "./diagnostic/realtime-debug";

import { cn } from "@ui/lib";
import { getLiveKitToken } from "../../../../../../../actions/appointments/livekit/livekit";
import { useMediaQuery } from "./hooks/use-media-query";
import { useVideoCallStatus } from "./hooks/use-video-call-status";
import { UserRole } from "./types";

interface ConsultationRoomProps {
	appointment: {
		id: string;
		scheduled_at: string;
		status?: string;
		doctor: {
			user: {
				id: string;
				name: string;
				image?: string;
			};
		};
		patient: {
			user: {
				id: string;
				name: string;
				image?: string;
			};
		};
	};
	userRole: UserRole;
}

interface TokenData {
	token: string;
	url: string;
}

export function ConsultationRoom({
	appointment,
	userRole,
}: ConsultationRoomProps) {
	// Estados básicos
	const [showVideo, setShowVideo] = useState(false);
	const [showChat, setShowChat] = useState(true);
	const [showPreJoin, setShowPreJoin] = useState(true); // Show prejoin for both roles by default
	const [connectionData, setConnectionData] = useState<TokenData | null>(null);
	// Add isConnecting state
	const [isConnecting, setIsConnecting] = useState(false);
	const [isCompleted, setIsCompleted] = useState(
		appointment.status === "COMPLETED"
	);
	// Track if doctor has restarted the call after completion
	const [doctorRestartedCall, setDoctorRestartedCall] = useState(false);
	// Add loading states for buttons
	const [isButtonLoading, setIsButtonLoading] = useState(false);
	// Status update timestamp (for notification triggers)
	const [statusUpdateTime, setStatusUpdateTime] = useState<Date | null>(null);

	// Verificar se devemos mostrar o diagnóstico de realtime
	const searchParams = useSearchParams();
	const showDebug = searchParams?.get("debug") === "true" && userRole === "DOCTOR";

	// Adaptar o formato do appointment para o formato esperado pelo ConsultationHeader
	const headerAppointment = {
		id: appointment.id,
		scheduledAt: appointment.scheduled_at,
		status: appointment.status,
		doctor: appointment.doctor,
		patient: appointment.patient,
	};

	// Hooks customizados
	const { isMobile } = useMediaQuery();
	const { isActive, updateStatus, completeAppointment, reactivateAppointment } = useVideoCallStatus(
		appointment.id,
		userRole,
	);
	const isDoctor = userRole === "DOCTOR";

	// For patients, show completed status even if doctor restarts the call
	const effectiveIsCompleted = isDoctor
		? (isCompleted && !doctorRestartedCall)
		: isCompleted;

	// Log para debug quando o estado de isActive muda
	useEffect(() => {
		console.log(`👀 [ConsultationRoom] isActive mudou: ${isActive}`, {
			isDoctor,
			showVideo,
			showPreJoin,
			isCompleted
		});
	}, [isActive, isDoctor, showVideo, showPreJoin, isCompleted]);

	// Função para obter token do LiveKit com retry
	const getTokenWithRetry = async (retries = 3): Promise<TokenData> => {
		for (let i = 0; i < retries; i++) {
			try {
				console.log(`Attempt ${i + 1} to get token for appointment ${appointment.id}`);
				const data = await getLiveKitToken(appointment.id);

				if (!data?.token || !data?.url) {
					console.error(`Token data incomplete on attempt ${i + 1}:`, data);
					throw new Error("Dados do token incompletos");
				}

				console.log(`Token obtained successfully on attempt ${i + 1}`);
				return {
					token: String(data.token),
					url: String(data.url),
				};
			} catch (err) {
				console.error(`Attempt ${i + 1} failed:`, err);
				if (i === retries - 1) throw err;
				// Increase backoff time for each retry
				await new Promise(resolve => setTimeout(resolve, 1500 * (i + 1)));
			}
		}
		throw new Error("Falha ao obter token após tentativas");
	};

	// Handler para erros da videochamada
	const handleError = useCallback(
		(error: Error) => {
			console.error("Erro na videochamada:", error);
			toast.error(`Erro na videochamada: ${error.message}`);
			setShowVideo(false);
			setConnectionData(null);
			setIsButtonLoading(false);
			setIsConnecting(false);

			// Return to prejoin for any errors
			setShowPreJoin(true);
		},
		[],
	);

	// Handler para encerrar a consulta completamente
	const handleCompleteAppointment = async () => {
		try {
			setIsButtonLoading(true);
			const success = await completeAppointment();
			if (success) {
				toast.success("Consulta encerrada com sucesso");
				setShowVideo(false);
				setConnectionData(null);
				setIsCompleted(true);
				setDoctorRestartedCall(false);
				setShowPreJoin(false);
				// Marcar timestamp para notificar o outro participante
				setStatusUpdateTime(new Date());

				// Desconectar automaticamente após encerrar
				handleDisconnect();
			}
		} catch (error) {
			console.error("Erro ao encerrar consulta:", error);
			toast.error("Erro ao encerrar consulta");
		} finally {
			setIsButtonLoading(false);
		}
	};

	// Handler para desconexão
	const handleDisconnect = useCallback(() => {
		setShowVideo(false);
		setConnectionData(null);
		setIsButtonLoading(false);
		setShowPreJoin(true);

		// Se for médico, atualizar status para não ativo
		if (isDoctor) {
			updateStatus(false).catch((error) => {
				console.error("Erro ao atualizar status:", error);
				toast.error("Erro ao encerrar consulta");
			});
		}

		// Notificar o outro participante
		const message = isDoctor
			? "O médico encerrou a chamada"
			: "O paciente saiu da chamada";
		toast.info(message);
	}, [isDoctor, updateStatus]);

	// Função para iniciar/entrar na videochamada
	const handleStartVideo = async () => {
		console.log("🎯 handleStartVideo: iniciando consulta", {
			isDoctor,
			isActive,
			isCompleted,
			doctorRestartedCall,
			showPreJoin,
			showVideo
		});

		try {
			setIsButtonLoading(true);

			// Médicos sempre podem iniciar consulta
			if (isDoctor) {
				console.log("👨‍⚕️ Médico iniciando consulta...");

				// Se a consulta está finalizada, tentar reativar
				if (isCompleted) {
					console.log("Tentando reativar consulta finalizada...");
					const reactivated = await reactivateAppointment();
					if (!reactivated) {
						toast.error("Não foi possível reativar a consulta");
						return;
					}
					toast.success("Consulta reativada com sucesso");
					setIsCompleted(false);
					setDoctorRestartedCall(true);
				}

				// Se a consulta já estiver ativa, apenas entrar na sala
				if (isActive) {
					console.log("Consulta já está ativa, entrando na sala...");
					handleJoinVideo({ audioEnabled: true, videoEnabled: true });
					return;
				}

				// Atualizar status da consulta para ativa
				console.log("Atualizando status da consulta...");
				const success = await updateStatus(true);

				if (!success) {
					console.error("Falha ao atualizar status da consulta");
					toast.error("Não foi possível iniciar a consulta. Tente novamente.");
					return;
				}

				console.log("✅ Status da consulta atualizado com sucesso");
				toast.success("Consulta iniciada com sucesso");

				// Marcar timestamp para notificar paciente via realtime
				setStatusUpdateTime(new Date());

				// Iniciar processo de conexão com a sala
				handleJoinVideo({ audioEnabled: true, videoEnabled: true });
				return;
			}

			// Pacientes só podem entrar em consultas ativas
			if (!isActive && !isDoctor) {
				console.log("👤 Paciente tentou entrar, mas consulta não está ativa");
				toast.error("Aguarde o médico iniciar a consulta antes de entrar.");
				return;
			}

			// Se chegou aqui, é um paciente e a consulta está ativa
			handleJoinVideo({ audioEnabled: true, videoEnabled: true });

		} catch (error) {
			console.error("❌ Erro ao iniciar consulta:", error);
			toast.error("Ocorreu um erro ao iniciar a consulta");
		} finally {
			setIsButtonLoading(false);
		}
	};

	// Função para entrar na videochamada
	const handleJoinVideo = async (data: { audioEnabled: boolean; videoEnabled: boolean }) => {
		console.log("🎥 handleJoinVideo: entrando na videochamada", {
			isDoctor,
			isActive,
			data
		});

		try {
			setIsConnecting(true);

			// Obter token do LiveKit
			console.log("🔄 Obtendo token do LiveKit...");
			const tokenData = await getTokenWithRetry();

			if (!tokenData) {
				throw new Error("Não foi possível obter token de conexão");
			}

			console.log("✅ Token obtido com sucesso");
			setConnectionData(tokenData);
			setShowPreJoin(false);
			setShowVideo(true);

		} catch (error) {
			console.error("❌ Erro ao entrar na videochamada:", error);
			toast.error("Erro ao conectar à sala de consulta");
			setShowPreJoin(true);
			setShowVideo(false);
		} finally {
			setIsConnecting(false);
		}
	};

	// Handler for returning to pre-join screen
	const handleReturnToPreJoin = useCallback(() => {
		setShowVideo(false);
		setConnectionData(null);
		setShowPreJoin(true);
	}, []);

	// Add this effect after the existing hooks in the ConsultationRoom component
	useEffect(() => {
		// Check if appointment status changed to IN_PROGRESS
		if (appointment.status === "IN_PROGRESS" || isActive) {
			// If we're showing prejoin screen for a patient, and the appointment is now active,
			// automatically update our internal state to reflect that
			if (!isDoctor && showPreJoin) {
				console.log("Detected active appointment for patient while in prejoin");
			}
		}

		// Nota: O hook useVideoCallStatus já gerencia o estado isActive com base no status
		// Log para debug
		if (appointment.status === "IN_PROGRESS" && !isActive) {
			console.log("Appointment status is IN_PROGRESS but isActive is false - this should sync automatically");
		}

	}, [appointment.status, isActive, isDoctor, showPreJoin]);

	// Layout Mobile
	if (isMobile) {
		return (
			<div className="flex h-dvh flex-col ">
				<ConsultationHeader
					appointment={headerAppointment}
					userRole={userRole}
					showVideo={showVideo}
					isActive={isActive}
					isCompleted={effectiveIsCompleted}
					isLoading={isButtonLoading}
					onToggleVideo={handleStartVideo}
					onDisconnect={handleDisconnect}
					onCompleteAppointment={handleCompleteAppointment}
					statusUpdateTime={statusUpdateTime || undefined}
				/>

				{/* {showDebug && (
					<div className="px-4 pb-4">
						<RealtimeDebug appointmentId={appointment.id} />
					</div>
				)} */}

				<MobileLayout
					showVideo={showVideo}
					showChat={showChat}
					onToggleChat={() => setShowChat(!showChat)}
					videoComponent={
						showPreJoin ? (
							<div className="flex h-full items-center justify-center p-4 flex-col">
								<CustomPreJoin
									isActive={isActive || doctorRestartedCall}
									onJoin={handleJoinVideo}
									userName={
										isDoctor
											? appointment.doctor.user.name
											: appointment.patient.user.name
									}
									userRole={userRole}
									isLoading={isConnecting}
									appointmentId={appointment.id}
								/>

								{/* Botão centralizado para iniciar/entrar na consulta */}
								{isDoctor && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											onClick={handleStartVideo}
											disabled={isButtonLoading || isConnecting}
										>
											{isButtonLoading || isConnecting ? (
												<>
													<Loader2 className="mr-2 h-5 w-5 animate-spin" />
													Carregando...
												</>
											) : (
												<>
													<Video className="mr-2 h-5 w-5" />
													Iniciar Consulta
												</>
											)}
										</Button>
									</div>
								)}

								{!isDoctor && isActive && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											onClick={handleStartVideo}
											disabled={isButtonLoading || isConnecting}
										>
											{isButtonLoading || isConnecting ? (
												<>
													<Loader2 className="mr-2 h-5 w-5 animate-spin" />
													Carregando...
												</>
											) : (
												<>
													<Video className="mr-2 h-5 w-5" />
													Entrar na Consulta
												</>
											)}
										</Button>
									</div>
								)}

								{!isDoctor && !isActive && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											variant="outline"
											disabled={true}
										>
											<Video className="mr-2 h-5 w-5" />
											Aguardando médico iniciar a consulta
										</Button>
									</div>
								)}
							</div>
						) : (
							<CustomVideoRoom
								token={connectionData?.token || null}
								serverUrl={connectionData?.url || null}
								isDoctor={isDoctor}
								isCompleted={effectiveIsCompleted}
								onError={handleError}
								onDisconnected={handleDisconnect}
								onReturnToPreJoin={handleReturnToPreJoin}
								showChat={showChat}
								onToggleChat={() => setShowChat(!showChat)}
							/>
						)
					}
					chatComponent={
						<ChatInterface
							appointment={appointment}
							userRole={userRole}
						/>
					}
				/>
			</div>
		);
	}

	// Layout Desktop
	return (
		<div className="relative flex h-full flex-col  ">
			<ConsultationHeader
				appointment={headerAppointment}
				userRole={userRole}
				showVideo={showVideo}
				isActive={isActive}
				isCompleted={effectiveIsCompleted}
				isLoading={isButtonLoading}
				onToggleVideo={handleStartVideo}
				onDisconnect={handleDisconnect}
				onCompleteAppointment={handleCompleteAppointment}
				statusUpdateTime={statusUpdateTime || undefined}
			/>

			{process.env.NODE_ENV === 'development' && showDebug && (
				<div className="mb-4 px-4">
					<RealtimeDebug appointmentId={appointment.id} />
				</div>
			)}

			<div className="relative flex flex-1 gap-6 overflow-hidden pr-4">
				<div
					className={cn(
						"relative flex h-full transition-all duration-300 ease-in-out",
						showChat ? "w-[calc(100%-400px)]" : "w-full",
					)}
				>
					<div className="h-full w-full rounded-xl border bg-muted/10">
						{showPreJoin ? (
							<div className="flex h-full items-center justify-center p-4 flex-col">
								<CustomPreJoin
									isActive={isActive || doctorRestartedCall}
									onJoin={handleJoinVideo}
									userName={
										isDoctor
											? appointment.doctor.user.name
											: appointment.patient.user.name
									}
									userRole={userRole}
								/>

								{/* Botão centralizado para iniciar/entrar na consulta para desktop */}
								{isDoctor && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											onClick={handleStartVideo}
											disabled={isButtonLoading || isConnecting}
										>
											{isButtonLoading || isConnecting ? (
												<>
													<Loader2 className="mr-2 h-5 w-5 animate-spin" />
													Carregando...
												</>
											) : (
												<>
													<Video className="mr-2 h-5 w-5" />
													Iniciar Consulta
												</>
											)}
										</Button>
									</div>
								)}

								{!isDoctor && isActive && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											onClick={handleStartVideo}
											disabled={isButtonLoading || isConnecting}
										>
											{isButtonLoading || isConnecting ? (
												<>
													<Loader2 className="mr-2 h-5 w-5 animate-spin" />
													Carregando...
												</>
											) : (
												<>
													<Video className="mr-2 h-5 w-5" />
													Entrar na Consulta
												</>
											)}
										</Button>
									</div>
								)}

								{!isDoctor && !isActive && !showVideo && !effectiveIsCompleted && (
									<div className="mt-6 w-full max-w-md mx-auto">
										<Button
											size="lg"
											className="w-full"
											variant="outline"
											disabled={true}
										>
											<Video className="mr-2 h-5 w-5" />
											Aguardando médico iniciar a consulta
										</Button>
									</div>
								)}
							</div>
						) : (
							<CustomVideoRoom
								token={connectionData?.token || null}
								serverUrl={connectionData?.url || null}
								isDoctor={isDoctor}
								isCompleted={effectiveIsCompleted}
								onError={handleError}
								onDisconnected={handleDisconnect}
								onReturnToPreJoin={handleReturnToPreJoin}
								showChat={showChat}
								onToggleChat={() => setShowChat(!showChat)}
							/>
						)}
					</div>
				</div>

				<div
					className={cn(
						"absolute top-0 right-0 h-full w-[400px] rounded-xl border  transition-transform duration-300 ease-in-out",
						showChat ? "translate-x-0" : "translate-x-full",
					)}
				>
					<ChatInterface appointment={appointment} userRole={userRole} />
				</div>
			</div>
		</div>
	);
}
