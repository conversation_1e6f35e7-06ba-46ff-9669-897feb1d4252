import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

interface AppointmentStatus {
  isActive: boolean;
  isCompleted: boolean;
  lastChanged?: Date;
  doctorName?: string;
  status?: string;
}

// Response payload from the API
interface AppointmentPayload {
  id: string;
  status?: string;
  scheduledAt?: string;
  updatedAt?: string;
  doctor?: {
    user?: {
      name?: string;
    }
  };
  [key: string]: any; // Allow other fields
}

/**
 * Hook for getting real-time appointment status updates
 * Uses Supabase Realtime to listen for appointment changes
 */
export function useAppointmentStatus(appointmentId: string) {
  const [status, setStatus] = useState<AppointmentStatus>({
    isActive: false,
    isCompleted: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Supabase client and channel for realtime updates
  const supabase = useRef(createRealtimeClient());
  const channelRef = useRef<any>(null);

  // Track initial load status
  const isInitialLoadComplete = useRef(false);

  // Track if component is mounted
  const isMountedRef = useRef(true);

  // Function to fetch appointment status (initial or manual refresh)
  const fetchStatus = useCallback(async (noCache = false): Promise<boolean> => {
    if (!appointmentId) return false;

    try {
      setIsLoading(true);

      const headers: HeadersInit = {
        "Content-Type": "application/json",
      };

      // Add cache-busting if needed
      if (noCache) {
        headers["Cache-Control"] = "no-cache, no-store, must-revalidate";
        headers["Pragma"] = "no-cache";
      }

      // Use a timestamp to avoid browser caching
      const timestamp = new Date().getTime();
      const url = `/api/appointments/${appointmentId}/status?_t=${timestamp}`;

      console.log(`🔄 Fetching status for appointment ${appointmentId}...`);

      const response = await fetch(url, { headers });

      if (!response.ok) {
        // Get detailed error info if available
        let errorDetail = "";
        try {
          const errorData = await response.json();
          errorDetail = errorData.error || "";
        } catch (e) {
          // Ignore JSON parsing error
        }

        throw new Error(`Status API returned ${response.status}: ${errorDetail}`);
      }

      const data = await response.json();

      // Validate the response
      if (data && typeof data === "object") {
        console.log(`✅ Appointment status received:`, data);

        // Update the status
        const newStatus: AppointmentStatus = {
          isActive: !!data.isActive || data.status === "IN_PROGRESS",
          isCompleted: !!data.isCompleted || data.status === "COMPLETED",
          status: data.status,
          lastChanged: new Date(),
          doctorName: data.doctorName,
        };

        if (isMountedRef.current) {
          setStatus(newStatus);
          setError(null);
        }

        return true;
      } else {
        throw new Error("Invalid response format from status API");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error fetching appointment status";
      console.error(`❌ Error fetching appointment status: ${errorMessage}`);

      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error(errorMessage));
        toast.error("Erro ao verificar status da consulta");
      }

      return false;
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  }, [appointmentId]);

  // Load initial appointment status
  useEffect(() => {
    isMountedRef.current = true;

    const loadInitialStatus = async () => {
      const success = await fetchStatus(true);
      if (success) {
        isInitialLoadComplete.current = true;
      }
    };

    loadInitialStatus();

    return () => {
      isMountedRef.current = false;
    };
  }, [fetchStatus]);

  // Set up Realtime subscription after initial load
  useEffect(() => {
    if (!isInitialLoadComplete.current || !appointmentId) {
      return;
    }

    const channelName = `appointment-${appointmentId}`;

    // Limpar canal existente se houver
    if (channelRef.current) {
      supabase.current.removeChannel(channelRef.current);
      channelRef.current = null;
    }

    try {
      console.log(`🔌 Setting up realtime channel for appointment ${appointmentId}...`);

      channelRef.current = supabase.current
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE', // Listen for updates
            schema: 'public',
            table: 'appointments',
            filter: `id=eq.${appointmentId}`
          },
          (payload) => {
            console.log('📢 Received appointment update:', payload);

            if (payload.new && isMountedRef.current) {
              const appointmentData = payload.new as any;

              // Determine active status based on appointment status
              const isActive = appointmentData.status === 'IN_PROGRESS' || !!appointmentData.isActive;
              const isCompleted = appointmentData.status === 'COMPLETED' || !!appointmentData.isCompleted;

              // Log para depuração
              console.log("🔄 [Realtime] Status da consulta atualizado:", {
                appointmentData,
                wasActive: status.isActive,
                nowActive: isActive,
                status: appointmentData.status
              });

              // Update the status state
              setStatus(prevStatus => ({
                ...prevStatus,
                isActive,
                isCompleted,
                status: appointmentData.status,
                lastChanged: new Date()
              }));

              // Notify user about important status changes
              if (isActive && !status.isActive) {
                toast.success("O médico iniciou a consulta");
              } else if (isCompleted && !status.isCompleted) {
                toast.info("A consulta foi finalizada");
              }
            }
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('✅ Connected to appointment realtime updates');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Error connecting to appointment channel');

            // Try to reconnect after a short delay
            setTimeout(() => {
              if (channelRef.current && isMountedRef.current) {
                channelRef.current.subscribe();
              }
            }, 3000);
          }
        });
    } catch (error) {
      console.error('Error setting up realtime channel:', error);

      if (isMountedRef.current) {
        toast.error('Erro ao configurar atualizações em tempo real');
        setError(error instanceof Error ? error : new Error('Erro na configuração de realtime'));
      }
    }

    // Cleanup on unmount
    return () => {
      if (channelRef.current) {
        console.log('Removing appointment realtime channel');
        supabase.current.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [appointmentId, status.isActive, status.isCompleted]);

  // Utility for manual refresh
  const refreshStatus = useCallback(async (): Promise<boolean> => {
    return fetchStatus(true); // Force no-cache for manual refresh
  }, [fetchStatus]);

  return {
    status,
    isLoading,
    error,
    refreshStatus,
  };
}
