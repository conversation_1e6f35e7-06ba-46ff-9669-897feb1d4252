"use client";

import { useEffect, useState, useRef } from "react";
import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardFooter } from "@ui/components/card";

import {
  checkRealtimeConfiguration,
  enableRealtimeForMessages,
  checkRealtimeSecurityMode,
  resetRealtimeSettings
}  from "../../../../../../../../actions/supabase/realtime-sql";
import { AlertCircle, CheckCircle, RefreshCw } from "lucide-react";
import { sendTextMessage } from "../../../../../../../../actions/chats/messages";

export function RealtimeDebug({ appointmentId }: { appointmentId: string }) {
  const [status, setStatus] = useState("Disconnected");
  const [messages, setMessages] = useState<string[]>([]);
  const [eventCount, setEventCount] = useState(0);
  const [realtimeStatus, setRealtimeStatus] = useState<{
    checked: boolean;
    hasMessagesTable: boolean;
    error?: string;
    securityInfo?: any;
  }>({
    checked: false,
    hasMessagesTable: false
  });
  const [isLoading, setIsLoading] = useState(false);

  const supabase = createRealtimeClient();
  const channelRef = useRef<any>(null);

  // Verificar configuração do realtime
  const checkRealtime = async () => {
    try {
      setIsLoading(true);
      setMessages(prev => [...prev, "Verificando configuração do realtime..."]);

      // Verificar tabelas na publicação
      const tableResult = await checkRealtimeConfiguration();

      // Verificar modo de segurança
      const securityResult = await checkRealtimeSecurityMode();

      setRealtimeStatus({
        checked: true,
        hasMessagesTable: tableResult.hasMessagesTable,
        error: tableResult.error,
        securityInfo: securityResult.securityInfo
      });

      // Adicionar informações ao log
      setMessages(prev => [
        ...prev,
        `Tabelas: ${JSON.stringify(tableResult, null, 2)}`,
        `Segurança: ${JSON.stringify(securityResult, null, 2)}`
      ]);
    } catch (error) {
      console.error("Error checking realtime:", error);
      setRealtimeStatus({
        checked: true,
        hasMessagesTable: false,
        error: error instanceof Error ? error.message : String(error)
      });
      setMessages(prev => [...prev, `Erro: ${error instanceof Error ? error.message : String(error)}`]);
    } finally {
      setIsLoading(false);
    }
  };

  // Ativar realtime para a tabela messages
  const enableRealtime = async () => {
    try {
      setIsLoading(true);
      setMessages(prev => [...prev, "Ativando realtime para messages..."]);

      const result = await enableRealtimeForMessages();

      // Adicionar informações ao log
      setMessages(prev => [
        ...prev,
        `Resultado: ${JSON.stringify(result, null, 2)}`
      ]);

      // Verificar status novamente
      await checkRealtime();
    } catch (error) {
      console.error("Error enabling realtime:", error);
      setMessages(prev => [
        ...prev,
        `Erro: ${error instanceof Error ? error.message : String(error)}`
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Resetar configurações de realtime
  const resetRealtime = async () => {
    try {
      setIsLoading(true);
      setMessages(prev => [...prev, "Resetando configurações de realtime..."]);

      const result = await resetRealtimeSettings();

      // Adicionar informações ao log
      setMessages(prev => [
        ...prev,
        `Resultado do reset: ${JSON.stringify(result, null, 2)}`
      ]);

      // Verificar status novamente
      await checkRealtime();

      // Reconectar ao canal
      refreshConnection();
    } catch (error) {
      console.error("Error resetting realtime:", error);
      setMessages(prev => [
        ...prev,
        `Erro: ${error instanceof Error ? error.message : String(error)}`
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  // Inicializar com logging detalhado
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    console.log("Setting up realtime debug for appointment:", appointmentId);
    const channelName = `debug:${appointmentId}`;

    console.log(`Creating debug channel: ${channelName}`);
    const channel = supabase.channel(channelName);

    channel
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `appointmentId=eq.${appointmentId}`,
      }, (payload) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime event received:', payload);
        }
        setMessages(prev => [...prev, `Evento recebido: ${JSON.stringify(payload, null, 2)}`]);
        setEventCount(prev => prev + 1);
      })
      .on('broadcast', { event: '*' }, (payload) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime event received:', payload);
        }
        setMessages(prev => [...prev, `Evento recebido: ${JSON.stringify(payload, null, 2)}`]);
        setEventCount(prev => prev + 1);
      })
      .subscribe((status) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime channel status:', status);
        }
        setStatus(status);
        setMessages(prev => [...prev, `Status do canal: ${status}`]);
      });

    channelRef.current = channel;
    if (process.env.NODE_ENV === 'development') {
      console.log("Debug channel created:", channelRef.current);
    }

    // Verificar status do realtime ao carregar
    checkRealtime();

    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log("Cleaning up debug channel");
      }
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [appointmentId, supabase]);

  const testInsert = async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log("Sending test message for debug");
    }
    try {
      const testContent = `Test message ${Date.now()}`;

      setMessages(prev => [...prev, `Enviando mensagem: ${testContent}`]);

      const result = await sendTextMessage(appointmentId, testContent);
      if (process.env.NODE_ENV === 'development') {
        console.log("Test message result:", result);
      }

      if (!result.success) {
        setMessages(prev => [...prev, `Erro: ${result.error}`]);
      } else {
        setMessages(prev => [...prev, `Mensagem enviada: ${testContent}`]);
        if (process.env.NODE_ENV === 'development') {
          console.log("Test message sent successfully, waiting for realtime event...");
        }
      }
    } catch (error) {
      console.error("Error sending test message:", error);
      setMessages(prev => [...prev, `Erro: ${error instanceof Error ? error.message : String(error)}`]);
    }
  };

  const refreshConnection = () => {
    const channelName = `debug:${appointmentId}`;

    if (process.env.NODE_ENV === 'development') {
      console.log(`Recreating debug channel: ${channelName}`);
    }

    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
    }

    const channel = supabase.channel(channelName);

    channel
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'messages',
        filter: `appointmentId=eq.${appointmentId}`,
      }, (payload) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime event received after refresh:', payload);
        }
        setMessages(prev => [...prev, `Evento recebido após reconexão: ${JSON.stringify(payload, null, 2)}`]);
        setEventCount(prev => prev + 1);
      })
      .on('broadcast', { event: '*' }, (payload) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime event received after refresh:', payload);
        }
        setMessages(prev => [...prev, `Evento recebido após reconexão: ${JSON.stringify(payload, null, 2)}`]);
        setEventCount(prev => prev + 1);
      })
      .subscribe((status) => {
        if (process.env.NODE_ENV === 'development') {
          console.log('Debug: Realtime channel status after refresh:', status);
        }
        setStatus(status);
        setMessages(prev => [...prev, `Status do canal após reconexão: ${status}`]);
      });

    channelRef.current = channel;
    if (process.env.NODE_ENV === 'development') {
      console.log("Debug channel recreated:", channelRef.current);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-md flex items-center justify-between">
          <span>Diagnóstico de Realtime</span>
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              {realtimeStatus.hasMessagesTable ? (
                <CheckCircle className="h-4 w-4 text-green-600 mr-1" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600 mr-1" />
              )}
              <span className="text-xs">
                {realtimeStatus.hasMessagesTable
                  ? "Realtime Ativo"
                  : realtimeStatus.checked ? "Realtime Inativo" : "Status Desconhecido"}
              </span>
            </div>

            <div className={`text-xs px-2 py-1 rounded ${
              status === 'SUBSCRIBED' ? 'bg-green-100 text-green-800' :
              status === 'CHANNEL_ERROR' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {status} {status === 'SUBSCRIBED' &&
                <CheckCircle className="h-3 w-3 inline ml-1" />}
              {status === 'CHANNEL_ERROR' &&
                <AlertCircle className="h-3 w-3 inline ml-1" />}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-xs">
          <p>AppointmentId: {appointmentId}</p>
          <p>Eventos recebidos: {eventCount}</p>
          <div className="mt-2 max-h-48 overflow-y-auto border p-2 rounded text-xs">
            {messages.length === 0 ? (
              <p className="text-muted-foreground">Nenhum evento recebido ainda</p>
            ) : (
              messages.map((msg, idx) => (
                <div key={idx} className="border-b py-1">
                  <pre className="whitespace-pre-wrap">{msg}</pre>
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-wrap gap-2">
        <Button size="sm" variant="outline" onClick={refreshConnection} disabled={isLoading}>
          <RefreshCw className="h-3 w-3 mr-1" />
          Reconectar
        </Button>
        <Button size="sm" variant="outline" onClick={checkRealtime} disabled={isLoading}>
          Verificar Status
        </Button>
        {!realtimeStatus.hasMessagesTable && (
          <Button size="sm" variant="default" onClick={enableRealtime} disabled={isLoading}>
            Ativar Realtime
          </Button>
        )}
        <Button size="sm" variant="outline" onClick={resetRealtime} disabled={isLoading}>
          Reset Completo
        </Button>
        <Button size="sm" onClick={testInsert} disabled={isLoading}>
          Enviar Mensagem Teste
        </Button>
      </CardFooter>
    </Card>
  );
}
