// types/chat.ts
export type MessageType = "TEXT" | "AUDIO" | "FILE" | "SYSTEM";

export interface Message {
	id: string;
	appointmentId: string;
	senderId: string;
	type: MessageType;
	content: string;
	createdAt: string;
	metadata?: {
		fileName?: string;
		contentType?: string;
		size?: number;
		path?: string;
		duration?: number;
	};
}

export interface ChatMessage {
	id: string;
	appointmentId: string;
	type: MessageType;
	content: string;
	metadata?: {
		fileName?: string;
		contentType?: string;
		size?: number;
		path?: string;
	};
	createdAt: string;
	senderId: string;
}

export interface ChatState {
	messages: Message[];
	isLoading: boolean;
	error: Error | null;
}

import { createClient } from "@supabase/supabase-js";
import { getSupabaseConfig } from "../../../../../../../../lib/config/supabase.config";

// Usar a configuração centralizada do Supabase
const config = getSupabaseConfig();
export const supabase = createClient(config.url, config.anonKey);

export interface ChatParticipant {
	id: string;
	name: string;
	avatarUrl?: string | null;
}

export interface ChatRoom {
	id: string;
	doctorId: string;
	doctor: {
		userId: string;
		user: ChatParticipant;
	};
	patientId: string;
	patient: {
		user: ChatParticipant;
	};
	scheduledAt: string;
	status: string;
}
