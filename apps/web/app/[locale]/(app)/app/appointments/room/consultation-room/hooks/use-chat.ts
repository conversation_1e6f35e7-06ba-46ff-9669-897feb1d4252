// hooks/features/chat/use-chat.ts
import { useCallback, useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import type { Message, MessageType } from "../chat/chat";
import {
  getAppointmentMessages,
  sendTextMessage,
  sendFileMessage as sendAttachmentAction,
  sendAudioMessage
} from "../../../../../../../../actions/chats/messages";

export function useChat(appointmentId: string, userId?: string) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const supabase = useRef(createRealtimeClient());
  const channelRef = useRef<any>(null);
  const messageCache = useRef(new Set<string>()); // Cache para evitar duplicatas
  const isInitialLoadComplete = useRef(false);

  // Carregar mensagens iniciais
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false);
        setError(new Error('Tempo limite atingido ao carregar mensagens. Tente novamente.'));
      }
    }, 10000);

    async function loadInitialMessages() {
      try {
        if (!appointmentId) {
          throw new Error('ID da consulta não fornecido');
        }

        const result = await getAppointmentMessages(appointmentId);

        // Validar a resposta
        if (!result) {
          throw new Error('Resposta inválida do servidor');
        }

        const { messages: data, error: fetchError } = result;

        if (fetchError) {
          throw new Error(fetchError);
        }

        // Verificar se data é realmente um array
        if (!Array.isArray(data)) {
          console.error("Formato de dados inválido:", data);
          throw new Error('Formato de dados inválido recebido do servidor');
        }

        if (data && data.length > 0) {
          // Atualizar o cache com as mensagens existentes
          messageCache.current.clear(); // Clear first to avoid potential duplicates
          data.forEach(msg => {
            if (msg && msg.id) {
              messageCache.current.add(msg.id);
            } else {
              console.warn("Mensagem inválida recebida:", msg);
            }
          });

          // Filtrar mensagens inválidas antes de definir o estado
          const validMessages = data.filter(msg => msg && msg.id);
          setMessages(validMessages);
        } else {
          setMessages([]);
        }

        isInitialLoadComplete.current = true;
        setIsLoading(false);
        setError(null);
      } catch (err) {
        console.error("Error loading initial messages:", err);
        setError(err instanceof Error ? err : new Error('Erro ao carregar mensagens'));
        setIsLoading(false);
      }
    }

    loadInitialMessages();

    return () => {
      clearTimeout(loadingTimeout);
    };
  }, [appointmentId]);

  // Configurar Realtime subscription
  useEffect(() => {
    if (!isInitialLoadComplete.current || !appointmentId) {
      return; // Don't set up subscription until initial messages are loaded
    }

    const channelName = `chat:${appointmentId}`;

    // Limpar canal existente se houver
    if (channelRef.current) {
      supabase.current.removeChannel(channelRef.current);
      channelRef.current = null;
    }

    try {
      channelRef.current = supabase.current
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `appointment_id=eq.${appointmentId}` // Usando snake_case para o filtro do Supabase
          },
          (payload) => {
            if (payload.new) {
              const newMessage = payload.new as Message;

              // Validar a mensagem recebida
              if (!newMessage || !newMessage.id) {
                console.warn("Mensagem inválida recebida via realtime:", newMessage);
                return;
              }

              // Verificar se já temos esta mensagem no cache
              if (!messageCache.current.has(newMessage.id)) {
                messageCache.current.add(newMessage.id);
                setMessages(prev => {
                  // Only add if not already present (extra safety check)
                  if (prev.some(msg => msg.id === newMessage.id)) {
                    return prev;
                  }
                  return [...prev, newMessage];
                });
              }
            }
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('Conectado ao chat em tempo real');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('Erro na conexão do chat');
            // Tentar reconectar em 3 segundos
            setTimeout(() => {
              if (channelRef.current) {
                channelRef.current.subscribe();
              }
            }, 3000);
          }
        });

    } catch (error) {
      console.error('Error setting up realtime channel:', error);
      toast.error('Erro ao configurar chat em tempo real');
    }

    // Cleanup
    return () => {
      if (channelRef.current) {
        supabase.current.removeChannel(channelRef.current);
        channelRef.current = null;
      }
    };
  }, [appointmentId, isInitialLoadComplete.current]);

  // Função para enviar mensagem
  const sendMessage = async (content: string) => {
    try {
      if (!appointmentId) {
        throw new Error('ID da consulta não fornecido');
      }

      const { success, message, error } = await sendTextMessage(appointmentId, content);

      if (!success || error) {
        throw new Error(error || "Erro ao enviar mensagem");
      }

      // Validar a mensagem recebida
      if (!message || !message.id) {
        throw new Error('Formato de resposta inválido do servidor');
      }

      // Adicionar a mensagem ao cache e ao estado imediatamente
      if (!messageCache.current.has(message.id)) {
        messageCache.current.add(message.id);
        setMessages(prev => {
          // Only add if not already present (defensive coding)
          if (prev.some(msg => msg.id === message.id)) {
            return prev;
          }
          return [...prev, message];
        });
      }

      return message;
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Erro ao enviar mensagem");
      throw error;
    }
  };

  const sendAudio = async (audioBlob: Blob) => {
    try {
      if (!appointmentId) {
        throw new Error('ID da consulta não fornecido');
      }

      if (!audioBlob || audioBlob.size === 0) {
        throw new Error("Arquivo de áudio inválido");
      }

      const arrayBuffer = await audioBlob.arrayBuffer();
      const { success, message, error } = await sendAudioMessage(
        appointmentId,
        arrayBuffer
      );

      if (!success || error) {
        throw new Error(error || "Erro ao enviar áudio");
      }

      // Validar a mensagem recebida
      if (!message || !message.id) {
        throw new Error('Formato de resposta inválido do servidor');
      }

      // Adicionar a mensagem ao cache e ao estado imediatamente
      if (!messageCache.current.has(message.id)) {
        messageCache.current.add(message.id);
        setMessages(prev => {
          // Only add if not already present
          if (prev.some(msg => msg.id === message.id)) {
            return prev;
          }
          return [...prev, message];
        });
      }

      return message;
    } catch (error) {
      console.error("Error sending audio:", error);
      toast.error("Erro ao enviar áudio");
      throw error;
    }
  };

  const sendAttachment = async (file: File) => {
    try {
      if (!appointmentId) {
        throw new Error('ID da consulta não fornecido');
      }

      if (!file || file.size === 0) {
        throw new Error("Arquivo inválido");
      }

      const arrayBuffer = await file.arrayBuffer();
      const { success, message, error } = await sendAttachmentAction(
        appointmentId,
        file,
        file.type,
        file.name,
        file.size,
        arrayBuffer
      );

      if (!success || error) {
        throw new Error(error || "Erro ao enviar arquivo");
      }

      // Validar a mensagem recebida
      if (!message || !message.id) {
        throw new Error('Formato de resposta inválido do servidor');
      }

      // Adicionar a mensagem ao cache e ao estado imediatamente
      if (!messageCache.current.has(message.id)) {
        messageCache.current.add(message.id);
        setMessages(prev => {
          // Only add if not already present
          if (prev.some(msg => msg.id === message.id)) {
            return prev;
          }
          return [...prev, message];
        });
      }

      return message;
    } catch (error) {
      console.error("Error sending attachment:", error);
      toast.error("Erro ao enviar arquivo");
      throw error;
    }
  };

  // Provide a way to refresh messages if needed
  const refreshMessages = useCallback(async () => {
    try {
      if (!appointmentId) {
        throw new Error('ID da consulta não fornecido');
      }

      setIsLoading(true);
      const result = await getAppointmentMessages(appointmentId);

      // Validar a resposta
      if (!result) {
        throw new Error('Resposta inválida do servidor');
      }

      const { messages: data, error: fetchError } = result;

      if (fetchError) {
        throw new Error(fetchError);
      }

      // Verificar se data é realmente um array
      if (!Array.isArray(data)) {
        console.error("Formato de dados inválido:", data);
        throw new Error('Formato de dados inválido recebido do servidor');
      }

      if (data && data.length > 0) {
        // Update cache with the messages
        messageCache.current.clear();
        data.forEach(msg => {
          if (msg && msg.id) {
            messageCache.current.add(msg.id);
          }
        });

        // Filtrar mensagens inválidas
        const validMessages = data.filter(msg => msg && msg.id);
        setMessages(validMessages);
      } else {
        setMessages([]);
      }

      setIsLoading(false);
      setError(null);
    } catch (err) {
      console.error("Error refreshing messages:", err);
      toast.error("Erro ao atualizar mensagens");
      setError(err instanceof Error ? err : new Error('Erro ao atualizar mensagens'));
      setIsLoading(false);
    }
  }, [appointmentId]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    sendAudio,
    sendAttachment,
    refreshMessages
  };
}
