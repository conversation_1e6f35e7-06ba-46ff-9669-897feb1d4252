import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

export default function DigitalRoomLoading() {
	return (
		<div className="container p-4">
			<div className="mb-6 flex flex-wrap gap-4">
				<Skeleton className="h-10 w-48" />
				<Skeleton className="h-10 w-[180px]" />
				<Skeleton className="h-10 w-[180px]" />
			</div>

			<div className="space-y-4">
				<div className="flex gap-2">
					<Skeleton className="h-10 w-32" />
					<Skeleton className="h-10 w-32" />
				</div>

				{/* Upcoming Appointments Skeleton */}
				<div className="space-y-4">
					{[1, 2, 3].map((i) => (
						<Card key={i} className="p-4">
							<div className="flex items-center gap-4">
								<Skeleton className="h-12 w-12 rounded-full" />
								<div className="flex-1">
									<div className="flex items-center gap-2">
										<Skeleton className="h-5 w-36" />
										<Skeleton className="h-5 w-20" />
									</div>
									<div className="mt-2 flex items-center gap-2">
										<Skeleton className="h-4 w-24" />
									</div>
								</div>
								<Skeleton className="h-9 w-24" />
							</div>
						</Card>
					))}
				</div>
			</div>
		</div>
	);
}
