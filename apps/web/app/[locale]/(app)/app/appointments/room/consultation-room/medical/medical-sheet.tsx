"use client";

import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { FileText, Loader2, Plus, Stethoscope, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Label } from "@ui/components/label";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Sheet,
	Sheet<PERSON>ontent,
	SheetHeader,
	SheetTitle,
	SheetTrigger,
} from "@ui/components/sheet";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";

interface Note {
	id: string;
	content: string;
	createdAt: string;
	doctor: {
		user: {
			name: string;
		};
	};
}

interface MedicalSheetProps {
	appointment: {
		id: string;
		medical_record?: {
			main_complaint?: string;
			diagnosis?: string;
			conduct?: string;
		};
		prescription?: {
			id: string;
			content?: any;
			status: string;
		};
	};
	onCreatePrescription: () => Promise<void>;
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
}

export function MedicalSheet({
	appointment,
	onCreatePrescription,
	open: externalOpen,
	onOpenChange: externalOnOpenChange,
}: MedicalSheetProps) {
	const [internalOpen, setInternalOpen] = useState(false);
	const isControlled = externalOpen !== undefined && externalOnOpenChange !== undefined;
	const isOpen = isControlled ? externalOpen : internalOpen;
	const setIsOpen = isControlled ? externalOnOpenChange : setInternalOpen;

	const [notes, setNotes] = useState("");
	const [noteHistory, setNoteHistory] = useState<Note[]>([]);
	const [isSaving, setIsSaving] = useState(false);
	const [symptoms, setSymptoms] = useState(
		appointment.medical_record?.main_complaint || "",
	);
	const [diagnosis, setDiagnosis] = useState(
		appointment.medical_record?.diagnosis || "",
	);
	const [treatment, setTreatment] = useState(
		appointment.medical_record?.conduct || "",
	);

	const [isLoadingNotes, setIsLoadingNotes] = useState(false);
	const [isLoadingMedicalRecord, setIsLoadingMedicalRecord] = useState(false);

	// Fetch notes when sheet opens
	useEffect(() => {
		const fetchNotes = async () => {
			try {
				setIsLoadingNotes(true);
				const response = await fetch(
					`/api/appointments/${appointment.id}/notes`,
					{
						headers: {
							'Content-Type': 'application/json',
						},
					}
				);

				if (!response.ok) {
					const errorData = await response.json().catch(() => ({}));
					console.error("Error fetching notes:", errorData);
					throw new Error(errorData.error || "Failed to fetch notes");
				}

				const data = await response.json();
				setNoteHistory(data.notes || []);
			} catch (error) {
				console.error("Error fetching notes:", error);
				toast.error("Erro ao carregar anotações");
			} finally {
				setIsLoadingNotes(false);
			}
		};

		if (isOpen) {
			fetchNotes();
		}
	}, [isOpen, appointment.id]);

	const handleSaveNotes = async () => {
		if (!notes.trim()) {
			toast.error("Por favor, adicione alguma anotação antes de salvar");
			return;
		}

		try {
			setIsSaving(true);
			console.log("Saving note:", {
				appointmentId: appointment.id,
				content: notes,
			});

			const response = await fetch(
				`/api/appointments/${appointment.id}/notes`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({ content: notes }),
				},
			);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				console.error("Error response:", errorData);
				throw new Error(errorData.error || "Failed to save note");
			}

			const newNote = await response.json();
			console.log("Saved note:", newNote);

			setNoteHistory((prev) => [newNote, ...prev]);
			setNotes("");
			toast.success("Anotação salva com sucesso");
		} catch (error) {
			console.error("Error saving note:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao salvar anotação");
		} finally {
			setIsSaving(false);
		}
	};

	const handleDeleteNote = async (noteId: string) => {
		try {
			const response = await fetch(
				`/api/appointments/${appointment.id}/notes?noteId=${noteId}`,
				{
					method: "DELETE",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				console.error("Error response:", errorData);
				throw new Error(errorData.error || "Failed to delete note");
			}

			setNoteHistory((prev) => prev.filter((note) => note.id !== noteId));
			toast.success("Anotação excluída com sucesso");
		} catch (error) {
			console.error("Error deleting note:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao excluir anotação");
		}
	};

	return (
		<Sheet open={isOpen} onOpenChange={setIsOpen}>
			{!isControlled && (
				<SheetTrigger asChild>
					<Button variant="outline" className="ml-4">
						<Stethoscope className="mr-2 h-5 w-5" />
						Prontuário
					</Button>
				</SheetTrigger>
			)}
			<SheetContent side="right" className="w-full min-w-[700px]">
				<SheetHeader>
					<SheetTitle>Prontuário do Paciente</SheetTitle>
				</SheetHeader>

				<Tabs defaultValue="notes" className="mt-6">
					<TabsList className="grid w-full grid-cols-2">
						<TabsTrigger value="notes">Anotações</TabsTrigger>

						<TabsTrigger value="prescription">Prescrição</TabsTrigger>
					</TabsList>

					<TabsContent value="notes" className="mt-4 space-y-4">
						<div className="space-y-4">
							<Textarea
								value={notes}
								onChange={(e) => setNotes(e.target.value)}
								placeholder="Adicione suas anotações sobre o paciente..."
								className="min-h-[200px]"
							/>
							<Button
								onClick={handleSaveNotes}
								disabled={isSaving}
								className="w-full"
							>
								{isSaving ? "Salvando..." : "Salvar Anotação"}
							</Button>
						</div>

						{isLoadingNotes ? (
							<div className="flex h-[200px] items-center justify-center">
								<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
							</div>
						) : noteHistory.length > 0 ? (
							<div className="mt-6">
								<h4 className="mb-4 font-medium text-sm">
									Histórico de Anotações
								</h4>
								<ScrollArea className="h-[400px]">
									<div className="space-y-4">
										{noteHistory.map((note) => (
											<Card key={note.id} className="p-4">
												<div className="space-y-2">
													<div className="flex items-center justify-between">
														<div className="flex flex-1 items-center justify-between">
															<span className="font-medium text-sm">
																{note.doctor.user.name}
															</span>
															<span className="text-muted-foreground text-xs">
																{format(
																	new Date(note.createdAt),
																	"dd/MM/yyyy HH:mm",
																	{
																		locale: ptBR,
																	},
																)}
															</span>
														</div>
														<AlertDialog>
															<AlertDialogTrigger asChild>
																<Button
																	variant="ghost"
																	size="icon"
																	className="ml-2 h-8 w-8 text-destructive"
																>
																	<Trash2 className="h-4 w-4" />
																</Button>
															</AlertDialogTrigger>
															<AlertDialogContent>
																<AlertDialogHeader>
																	<AlertDialogTitle>
																		Excluir anotação?
																	</AlertDialogTitle>
																	<AlertDialogDescription>
																		Esta ação não pode ser desfeita.
																	</AlertDialogDescription>
																</AlertDialogHeader>
																<AlertDialogFooter>
																	<AlertDialogCancel>
																		Cancelar
																	</AlertDialogCancel>
																	<AlertDialogAction
																		onClick={() => handleDeleteNote(note.id)}
																		className="bg-destructive text-destructive-foreground"
																	>
																		Excluir
																	</AlertDialogAction>
																</AlertDialogFooter>
															</AlertDialogContent>
														</AlertDialog>
													</div>
													<p className="text-sm">{note.content}</p>
												</div>
											</Card>
										))}
									</div>
								</ScrollArea>
							</div>
						) : (
							<div className="flex h-[200px] items-center justify-center">
								<p className="text-sm text-muted-foreground">
									Nenhuma anotação encontrada.
								</p>
							</div>
						)}
					</TabsContent>

					<TabsContent value="prescription" className="mt-4">
						{appointment.prescription ? (
							<div className="space-y-4">
								<div className="rounded-lg border p-4">
									<div className="flex items-center gap-2">
										<FileText className="h-5 w-5 text-muted-foreground" />
										<span className="font-medium">
											Prescrição{" "}
											{appointment.prescription.status === "active"
												? "Ativa"
												: "Inativa"}
										</span>
									</div>
									<div className="mt-4 space-y-2">
										<p className="text-sm text-muted-foreground">
											Clique em editar para modificar a prescrição usando o
											Memed
										</p>
									</div>
								</div>
							</div>
						) : (
							<div className="flex flex-col items-center justify-center space-y-4 py-8">
								<Button
									onClick={onCreatePrescription}
									className="w-full max-w-sm"
								>
									<Plus className="mr-2 h-4 w-4" />
									Criar Nova Prescrição
								</Button>
								<p className="text-muted-foreground text-sm">
									Crie uma nova prescrição usando o Memed
								</p>
							</div>
						)}
					</TabsContent>
				</Tabs>
			</SheetContent>
		</Sheet>
	);
}
