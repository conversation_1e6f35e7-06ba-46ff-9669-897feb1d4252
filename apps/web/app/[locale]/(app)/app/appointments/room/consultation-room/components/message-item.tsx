import { useState } from 'react';
import Image from 'next/image';
import { Message } from '../chat/chat';
import { Avatar } from '@ui/components/avatar';
import { cn } from '@ui/lib';

interface MessageItemProps {
  message: Message;
  isCurrentUser: boolean;
}

export function MessageItem({ message, isCurrentUser }: MessageItemProps) {
  const [isImageLoading, setIsImageLoading] = useState(true);
  const [isAudioLoading, setIsAudioLoading] = useState(true);

  const renderContent = () => {
    switch (message.type) {
      case 'TEXT':
        return <p className="text-sm">{message.content}</p>;

      case 'FILE':
        // Verificar se é uma imagem pelo tipo do arquivo
        const isImage = message.metadata?.contentType?.startsWith('image/');
        if (isImage) {
          return (
            <div className="relative max-w-[300px] rounded-lg overflow-hidden">
              <Image
                src={message.content}
                alt="Imagem anexada"
                width={300}
                height={200}
                className={cn(
                  "object-cover transition-opacity duration-300",
                  isImageLoading ? "opacity-0" : "opacity-100"
                )}
                onLoad={() => setIsImageLoading(false)}
                onError={() => setIsImageLoading(false)}
              />
              {isImageLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted">
                  <span className="text-sm text-muted-foreground">Carregando imagem...</span>
                </div>
              )}
            </div>
          );
        }
        // Para outros tipos de arquivo
        return (
          <div className={cn(
            "flex items-center text-primary gap-2 p-2 rounded-lg",
            isCurrentUser ? "bg-primary/50" : "bg-primary/50"
          )}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-foreground"
            >
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
              <polyline points="14 2 14 8 20 8" />
              <line x1="12" y1="18" x2="12" y2="12" />
              <line x1="9" y1="15" x2="15" y2="15" />
            </svg>
            <a
              href={message.content}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-foreground hover:underline"
            >
              {message.metadata?.fileName || 'Arquivo anexado'}
            </a>
          </div>
        );

      case 'AUDIO':
        return (
          <div className={cn(
            "relative min-w-[200px] p-2 rounded-lg",
            isCurrentUser ? "bg-primary/30" : "bg-primary/50"
          )}>
            <audio
              controls
              className={cn(
                "w-full rounded-lg transition-opacity duration-300",
                isAudioLoading ? "opacity-0" : "opacity-100",
                "[&::-webkit-media-controls-panel]:bg-transparent",
                "[&::-webkit-media-controls-current-time-display]:text-foreground",
                "[&::-webkit-media-controls-time-remaining-display]:text-foreground",
                "[&::-webkit-media-controls-timeline]:text-foreground",
                "[&::-webkit-media-controls-play-button]:text-foreground",
                "[&::-webkit-media-controls-mute-button]:text-foreground"
              )}
              onLoadedData={() => setIsAudioLoading(false)}
              onError={() => setIsAudioLoading(false)}
            >
              <source src={message.content} type="audio/webm" />
              Seu navegador não suporta o elemento de áudio.
            </audio>
            {isAudioLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted rounded-lg">
                <span className="text-sm text-muted-foreground">
                  Carregando áudio...
                </span>
              </div>
            )}
          </div>
        );

      default:
        return <p className="text-sm">{message.content}</p>;
    }
  };

  return (
    <div
      className={cn(
        "flex w-full gap-2 px-4",
        isCurrentUser ? "flex-row-reverse" : "flex-row"
      )}
    >
      <Avatar
        image={message.sender?.avatarUrl}
        fallback={message.sender?.name?.[0] || "U"}
        className="w-8 h-8"
      />
      <div
        className={cn(
          "flex flex-col max-w-[70%] gap-1",
          isCurrentUser ? "items-end" : "items-start"
        )}
      >
        <div
          className={cn(
            "rounded-lg",
            message.type === 'TEXT' ? "p-3" : "p-0",
            message.type === 'TEXT' && (
              isCurrentUser
                ? "bg-primary text-primary-foreground"
                : "bg-primary text-primary-foreground"
            )
          )}
        >
          {renderContent()}
        </div>
        <span className="text-xs text-muted-foreground">
          {new Date(message.createdAt).toLocaleTimeString()}
        </span>
      </div>
    </div>
  );
}
