import { useCallback, useRef, useState } from "react";

interface AudioRecorderState {
  isRecording: boolean;
  recordingTime: number;
  audioBlob: Blob | null;
  error: string | null;
}

export const useAudioRecorder = () => {
  const [state, setState] = useState<AudioRecorderState>({
    isRecording: false,
    recordingTime: 0,
    audioBlob: null,
    error: null,
  });

  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const chunks = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout>();

  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      mediaRecorder.current = new MediaRecorder(stream);
      chunks.current = [];

      mediaRecorder.current.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.current.push(e.data);
        }
      };

      mediaRecorder.current.onstop = () => {
        const blob = new Blob(chunks.current, { type: "audio/webm" });
        setState((prev) => ({ ...prev, audioBlob: blob }));

        // Stop all tracks
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.current.start();
      setState((prev) => ({
        ...prev,
        isRecording: true,
        recordingTime: 0,
        error: null,
      }));

      // Start timer
      timerRef.current = setInterval(() => {
        setState((prev) => ({
          ...prev,
          recordingTime: prev.recordingTime + 1,
        }));
      }, 1000);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          "Erro ao acessar microfone. Por favor, permita o acesso ao microfone.",
      }));
      console.error("Error accessing microphone:", error);
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (mediaRecorder.current && state.isRecording) {
      mediaRecorder.current.stop();
      clearInterval(timerRef.current);
      setState((prev) => ({ ...prev, isRecording: false }));
    }
  }, [state.isRecording]);

  const cancelRecording = useCallback(() => {
    if (mediaRecorder.current && state.isRecording) {
      mediaRecorder.current.stop();
      clearInterval(timerRef.current);
      chunks.current = [];
      setState((prev) => ({
        ...prev,
        isRecording: false,
        recordingTime: 0,
        audioBlob: null,
      }));
    }
  }, [state.isRecording]);

  const resetRecording = useCallback(() => {
    setState((prev) => ({
      ...prev,
      recordingTime: 0,
      audioBlob: null,
      error: null,
    }));
  }, []);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  return {
    isRecording: state.isRecording,
    recordingTime: formatTime(state.recordingTime),
    audioBlob: state.audioBlob,
    error: state.error,
    startRecording,
    stopRecording,
    cancelRecording,
    resetRecording,
  };
};
