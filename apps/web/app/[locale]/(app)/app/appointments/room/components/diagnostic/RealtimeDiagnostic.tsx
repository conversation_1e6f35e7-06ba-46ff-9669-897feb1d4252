// components/diagnostic/RealtimeDiagnostic.tsx
"use client";

import { useEffect, useState, useRef } from "react";
import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { Button } from "@ui/components/button";
import { Card, CardHeader, CardTitle, CardContent } from "@ui/components/card";

export function RealtimeDiagnostic({ appointmentId }: { appointmentId: string }) {
  const [status, setStatus] = useState("Disconnected");
  const [messages, setMessages] = useState<string[]>([]);
  const [eventCount, setEventCount] = useState(0);
  const supabase = createRealtimeClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    channelRef.current = supabase
      .channel('diagnostic-channel')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          console.log('Diagnostic received realtime event:', payload);
          setMessages(prev => [...prev, JSON.stringify(payload, null, 2)]);
          setEventCount(prev => prev + 1);
        }
      )
      .subscribe((status) => {
        console.log('Diagnostic channel status:', status);
        setStatus(status);
      });

    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [supabase]);

  const testInsert = async () => {
    const { error } = await supabase
      .from('messages')
      .insert({
        appointmentId: appointmentId,
        senderId: 'diagnostic-test',
        type: 'TEXT',
        content: 'Diagnostic test message ' + Date.now()
      });

    if (error) {
      console.error('Error inserting test message:', error);
      setMessages(prev => [...prev, `Error: ${error.message}`]);
    } else {
      setMessages(prev => [...prev, `Test message inserted successfully!`]);
    }
  };

  const refreshConnection = () => {
    if (channelRef.current) {
      supabase.removeChannel(channelRef.current);
    }

    setMessages([]);
    setEventCount(0);
    setStatus("Reconnecting...");

    channelRef.current = supabase
      .channel('diagnostic-channel-' + Date.now())
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'messages'
        },
        (payload) => {
          console.log('Diagnostic received realtime event:', payload);
          setMessages(prev => [...prev, JSON.stringify(payload, null, 2)]);
          setEventCount(prev => prev + 1);
        }
      )
      .subscribe((status) => {
        console.log('Diagnostic channel status:', status);
        setStatus(status);
      });
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Realtime Diagnostic</span>
          <span className={`text-sm px-2 py-1 rounded ${
            status === 'SUBSCRIBED' ? 'bg-green-100 text-green-800' :
            status === 'CHANNEL_ERROR' ? 'bg-red-100 text-red-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {status}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-4">
          <Button onClick={testInsert} size="sm" variant="outline">
            Insert Test Message
          </Button>
          <Button onClick={refreshConnection} size="sm" variant="outline">
            Reconnect
          </Button>
        </div>

        <div className="text-sm mb-2">Events received: {eventCount}</div>

        <div className="h-40 overflow-y-auto text-xs border rounded p-2 bg-gray-50 dark:bg-gray-900">
          {messages.length === 0 ? (
            <p className="text-gray-500">No events received yet</p>
          ) : (
            messages.map((msg, i) => (
              <div key={i} className="border-b py-1 whitespace-pre-wrap">{msg}</div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
