// components/consultation-room/layout/mobile-layout.tsx
import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { ChevronLeft, MessageSquare, Video } from "lucide-react";

interface MobileLayoutProps {
	showVideo: boolean;
	showChat: boolean;
	onToggleChat: () => void;
	videoComponent: React.ReactNode;
	chatComponent: React.ReactNode;
}

export function MobileLayout({
	showVideo,
	showChat,
	onToggleChat,
	videoComponent,
	chatComponent,
}: MobileLayoutProps) {
	return (
		<div className="relative flex flex-1 flex-col overflow-hidden">
			{/* Barra de controle */}
			<div className="z-10 flex items-center justify-between border-b bg-background px-4 py-2">
				{showChat ? (
					<Button
						variant="ghost"
						size="sm"
						onClick={onToggleChat}
						className="gap-2"
					>
						<ChevronLeft className="h-4 w-4" />
						<Video className="h-4 w-4" />
						Voltar para vídeo
					</Button>
				) : (
					<Button
						variant="ghost"
						size="sm"
						onClick={onToggleChat}
						className="gap-2"
					>
						<MessageSquare className="h-4 w-4" />
						Chat
					</Button>
				)}
			</div>

			{/* Container do conteúdo com transições */}
			<div className="relative flex flex-1 overflow-hidden">
				{/* Container do Vídeo */}
				<div
					className={cn(
						"absolute h-full w-full transition-transform duration-300 ease-in-out",
						showChat ? "-translate-x-full" : "translate-x-0",
					)}
				>
					{videoComponent}
				</div>

				{/* Container do Chat */}
				<div
					className={cn(
						"absolute h-full w-full bg-background transition-transform duration-300 ease-in-out",
						showChat ? "translate-x-0" : "translate-x-full",
					)}
				>
					{chatComponent}
				</div>
			</div>
		</div>
	);
}
