// hooks/features/video-call/use-video-call-status.ts
import { createRealtimeClient } from "@shared/lib/supabase/realtime-client";
import { useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import { getAppointmentStatus } from "../../../../../../../../actions/appointments/status/status";
import { updateAppointmentStatus } from "../../../../../../../../actions/appointments/update-status";


export function useVideoCallStatus(
  appointmentId: string,
  userRole: "DOCTOR" | "PATIENT",
) {
  const [isActive, setIsActive] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [status, setStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const supabase = createRealtimeClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    // Buscar status inicial
    async function fetchInitialStatus() {
      try {
        if (!appointmentId) {
          console.error("Missing appointment ID");
          setError("ID da consulta não encontrado");
          toast.error("ID da consulta não encontrado");
          setIsLoading(false);
          return;
        }

        console.log(`Fetching initial status for appointment ${appointmentId}`);
        const response = await getAppointmentStatus(appointmentId);

        if (response.error) {
          console.error("Error fetching initial status:", response.error);
          setError(response.error);
          toast.error("Erro ao buscar status da consulta");
          setIsLoading(false);
          return;
        }

        console.log("Initial status:", response.status);
        setStatus(response.status);
        setIsActive(!!response.isActive);
        setError(null);
      } catch (err) {
        console.error("Exception fetching initial status:", err);
        setError(err instanceof Error ? err.message : "Erro desconhecido");
        toast.error("Erro ao buscar status da consulta");
      } finally {
        setIsLoading(false);
      }
    }

    fetchInitialStatus();

    // Configurar o canal Realtime
    if (!appointmentId) {
      console.error("Cannot set up Realtime channel: Missing appointment ID");
      return;
    }

    const channelName = `appointment-status-${appointmentId}-${Date.now()}`;
    console.log(`Setting up status channel: ${channelName}`);

    try {
      channelRef.current = supabase
        .channel(channelName)
        .on(
          "postgres_changes",
          {
            event: "UPDATE",
            schema: "public",
            table: "appointments",
            filter: `id=eq.${appointmentId}`,
          },
          (payload) => {
            console.log("Realtime appointment update received:", payload);
            // O status está na coluna status
            const newStatus = payload.new.status;
            console.log("New appointment status:", newStatus);
            setStatus(newStatus);
            setIsActive(newStatus === "IN_PROGRESS");
          },
        )
        .subscribe((status) => {
          console.log(`Status channel subscription status:`, status);
          if (status === 'SUBSCRIBED') {
            console.log('🟢 Successfully subscribed to appointment status updates');
          } else if (status === "CHANNEL_ERROR") {
            console.error("🔴 Error connecting to appointment status channel");
            setError("Erro ao monitorar status da consulta");
            toast.error("Erro ao monitorar status da consulta");
          }
        });
    } catch (err) {
      console.error("Error setting up realtime channel:", err);
      setError("Erro ao configurar canal de status");
      toast.error("Erro ao configurar atualizações em tempo real");
    }

    // Cleanup
    return () => {
      console.log("Cleaning up appointment status subscription");
      if (channelRef.current) {
        try {
          supabase.removeChannel(channelRef.current);
        } catch (err) {
          console.error("Error removing channel:", err);
        }
      }
    };
  }, [appointmentId, supabase]);

  const updateStatus = async (active: boolean) => {
    console.log("🔄 [useVideoCallStatus] updateStatus chamado com active =", active);
    console.log("🔑 Appointment ID:", appointmentId);
    console.log("👨‍⚕️ User role:", userRole);

    try {
      if (!appointmentId) {
        console.error("Missing appointment ID when trying to update status");
        toast.error("ID da consulta não encontrado");
        return false;
      }

      // Log detalhado
      console.log(`🔄 Atualizando status da consulta ${appointmentId}...`);
      console.log(`Tentando mudar para status: ${active ? "IN_PROGRESS" : "SCHEDULED"}`);
      console.log(`Estado atual: isActive=${isActive}, status=${status}`);

      // Iniciar temporizador para medir tempo de resposta da API
      const startTime = Date.now();

      const response = await updateAppointmentStatus(appointmentId, active);

      // Calcular tempo de resposta
      const elapsedTime = Date.now() - startTime;
      console.log(`⏱️ Tempo de resposta da API: ${elapsedTime}ms`);

      // Log da resposta
      console.log("📄 Resposta completa da API:", response);

      if (!response.success || response.error) {
        console.error("❌ Erro ao atualizar status:", response.error);
        console.error("Detalhes da falha:", response);
        toast.error(response.error || "Erro ao atualizar status da consulta");
        return false;
      }

      console.log("✅ Status atualizado com sucesso:", response.status);
      console.log(`isActive alterado de ${isActive} para ${!!response.isActive}`);

      setStatus(response.status);
      setIsActive(!!response.isActive);
      setError(null);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Erro desconhecido";
      console.error("❌ Exceção ao atualizar status:", errorMessage);
      console.error("Stack trace:", err);
      setError(errorMessage);
      toast.error("Erro ao atualizar status da consulta");
      return false;
    }
  };

  // Nova função para encerrar consulta
  const completeAppointment = async () => {
    try {
      console.log(`Completing appointment ${appointmentId}`);

      const { success, status: newStatus, error } = await updateAppointmentStatus(appointmentId, "COMPLETED");

      if (!success || error) {
        throw new Error(error || "Erro ao encerrar consulta");
      }

      console.log("Appointment completed successfully:", newStatus);
      setStatus(newStatus);
      setIsActive(false);
      return true;
    } catch (err) {
      console.error("Error completing appointment:", err);
      toast.error("Erro ao encerrar consulta");
      return false;
    }
  };

  // Nova função para reativar uma consulta finalizada (apenas para médicos)
  const reactivateAppointment = async () => {
    if (userRole !== "DOCTOR") {
      console.error("Apenas médicos podem reativar uma consulta finalizada");
      toast.error("Não autorizado a reativar consulta");
      return false;
    }

    try {
      console.log(`Reactivating completed appointment ${appointmentId}`);

      // Primeiro, atualizamos o status para agendado (SCHEDULED)
      const { success, error } = await updateAppointmentStatus(appointmentId, false);

      if (!success || error) {
        throw new Error(error || "Erro ao reativar consulta");
      }

      console.log("Appointment successfully reactivated");
      setStatus("SCHEDULED");
      setIsActive(false);
      return true;
    } catch (err) {
      console.error("Error reactivating appointment:", err);
      toast.error("Erro ao reativar consulta");
      return false;
    }
  };

  return {
    isActive,
    isLoading,
    status,
    error,
    updateStatus,
    completeAppointment,
    reactivateAppointment,
  };
}
