// components/consultation-room/video/mobile-video-controls.tsx
import { useState } from "react";
import { Camera, ChevronUp, MessageSquare, Mic, PhoneOff } from "lucide-react";

import { cn } from "@ui/lib";
import { Button } from "@ui/components/button";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@ui/components/sheet";

interface MobileVideoControlsProps {
  isCameraEnabled: boolean;
  isMicEnabled: boolean;
  onToggleCamera: () => void;
  onToggleMic: () => void;
  onShowChat: () => void;
  onDisconnect: () => void;
}

export function MobileVideoControls({
  isCameraEnabled,
  isMicEnabled,
  onToggleCamera,
  onToggleMic,
  onShowChat,
  onDisconnect,
}: MobileVideoControlsProps) {
  const [showFullControls, setShowFullControls] = useState(false);

  return (
    <Sheet>
      <div className="fixed bottom-0 left-0 right-0 flex flex-col">
        {/* Barra de controles principal */}
        <div className="flex items-center justify-between bg-gradient-to-t from-background/95 to-background/60 px-4 py-2 backdrop-blur">
          <div className="flex items-center gap-2">
            <Button
              variant={isMicEnabled ? "default" : "outline"}
              size="icon"
              className="h-10 w-10"
              onClick={onToggleMic}
            >
              <Mic className="h-5 w-5" />
            </Button>

            <Button
              variant={isCameraEnabled ? "default" : "outline"}
              size="icon"
              className="h-10 w-10"
              onClick={onToggleCamera}
            >
              <Camera className="h-5 w-5" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10"
              onClick={onShowChat}
            >
              <MessageSquare className="h-5 w-5" />
            </Button>

            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10"
                onClick={() => setShowFullControls(true)}
              >
                <ChevronUp className="h-5 w-5" />
              </Button>
            </SheetTrigger>

            <Button
              variant="error"
              size="icon"
              className="h-10 w-10"
              onClick={onDisconnect}
            >
              <PhoneOff className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Drawer com controles adicionais */}
      <SheetContent>
        <div className="flex flex-col gap-4 p-4">
          <h4 className="font-medium">Controles da Chamada</h4>

          {/* Aqui podem ser adicionados controles adicionais como:
           - Seleção de dispositivos
           - Configurações de áudio/vídeo
           - Compartilhamento de tela
           - etc.
          */}
        </div>
      </SheetContent>
    </Sheet>
  );
}
