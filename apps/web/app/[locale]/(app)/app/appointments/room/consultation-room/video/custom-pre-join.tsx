"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON>, Check, Loader2, <PERSON>fresh<PERSON>c<PERSON>, Settings, Video } from "lucide-react";

import { UserRole } from "../types";
import { useAppointmentStatus } from "../hooks/use-appointment-status";

import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Switch } from "@ui/components/switch";
import { toast } from "sonner";
import { Badge } from "@ui/components/badge";
import { useRouter } from "next/navigation";

interface CustomPreJoinProps {
	onJoin: (data: { audioEnabled: boolean; videoEnabled: boolean }) => void;
	userName: string;
	isActive: boolean;
	userRole: UserRole;
	isLoading?: boolean;
	appointmentId?: string;
}

export function CustomPreJoin({
	onJoin,
	userName,
	isActive: initialIsActive,
	userRole,
	isLoading: initialIsLoading = false,
	appointmentId,
}: CustomPreJoinProps) {
	const [audioEnabled, setAudioEnabled] = useState(true);
	const [videoEnabled, setVideoEnabled] = useState(true);
	const [hasTrackPermissionError, setHasTrackPermissionError] = useState(false);
	const [mediaDevices, setMediaDevices] = useState<{ hasMicrophone: boolean; hasCamera: boolean }>({
		hasMicrophone: false,
		hasCamera: false,
	});
	const [isManuallyRefreshing, setIsManuallyRefreshing] = useState(false);
	const [showDoctorEnteredNotification, setShowDoctorEnteredNotification] = useState(false);
	const [hasDismissedNotification, setHasDismissedNotification] = useState(false);
	const [lastStatusCheck, setLastStatusCheck] = useState<Date | null>(null);
	const router = useRouter();

	// Extract appointment ID from the URL if not provided
	const resolvedAppointmentId = appointmentId || (typeof window !== 'undefined' ? window.location.pathname.split('/').pop() : undefined);

	// Use the appointment status hook if we're a patient
	const shouldUseStatusHook = userRole === "PATIENT" && resolvedAppointmentId;
	const appointmentStatus = shouldUseStatusHook
		? useAppointmentStatus(resolvedAppointmentId!)
		: { status: { isActive: initialIsActive, isCompleted: false }, isLoading: initialIsLoading, error: null, refreshStatus: null };

	// Combine passed props with real-time status
	const isActive = shouldUseStatusHook ? appointmentStatus.status.isActive : initialIsActive;
	const isLoading = shouldUseStatusHook ? (appointmentStatus.isLoading || isManuallyRefreshing) : initialIsLoading;

	// DEBUG: Log status changes for debugging
	useEffect(() => {
		if (userRole === "PATIENT" && shouldUseStatusHook) {
			console.log("👨‍⚕️ [CustomPreJoin] Status da consulta:", {
				receivedStatus: appointmentStatus.status,
				isActive,
				initialIsActive,
				rawStatus: 'status' in appointmentStatus.status ? appointmentStatus.status.status : undefined,
			});
		}
	}, [appointmentStatus.status, isActive, initialIsActive, shouldUseStatusHook, userRole]);

	// Check for permissions and available devices
	useEffect(() => {
		if (typeof navigator === "undefined") return;

		const checkPermissions = async () => {
			try {
				// First check what devices are available
				const devices = await navigator.mediaDevices.enumerateDevices();
				const hasMicrophone = devices.some(device => device.kind === 'audioinput');
				const hasCamera = devices.some(device => device.kind === 'videoinput');

				setMediaDevices({ hasMicrophone, hasCamera });

				// Request camera and microphone permissions for devices that exist
				const constraints: MediaStreamConstraints = {
					audio: hasMicrophone,
					video: hasCamera
				};

				await navigator.mediaDevices.getUserMedia(constraints);
				setHasTrackPermissionError(false);
			} catch (error) {
				console.error("Error requesting media permissions:", error);
				setHasTrackPermissionError(true);

				// Provide specific error message based on error type
				if ((error as DOMException).name === 'NotAllowedError') {
					toast.error('Permissão para câmera/microfone negada. Por favor, permita o acesso para participar da consulta.');
				} else if ((error as DOMException).name === 'NotFoundError') {
					toast.error('Não foi possível encontrar câmera ou microfone. Verifique se os dispositivos estão conectados.');
				}
			}
		};

		checkPermissions();
	}, []);

	const isDoctor = userRole === "DOCTOR";
	const isPatient = userRole === "PATIENT";

	// Function declarations need to come before they're used in useEffect
	const handleJoin = () => {


		console.log("isActive", isActive);
		console.log("isPatient", isPatient);


		if (isPatient && !isActive) {
			toast.error("Aguarde o médico iniciar a consulta antes de entrar.");
			return;
		}

		// For patients, dismiss notification when joining
		if (isPatient) {
			setHasDismissedNotification(true);
			setShowDoctorEnteredNotification(false);
		}

		onJoin({
			audioEnabled: audioEnabled && mediaDevices.hasMicrophone,
			videoEnabled: videoEnabled && mediaDevices.hasCamera,
		});
	};

	const handleManualRefresh = async () => {
		if (appointmentStatus.refreshStatus === null) return;

		setIsManuallyRefreshing(true);
		try {
			const success = await appointmentStatus.refreshStatus();
			if (success) {
				toast.success("Status da consulta atualizado");
			}
		} catch (error) {
			toast.error("Erro ao atualizar status da consulta");
		} finally {
			setIsManuallyRefreshing(false);
		}
	};

	// Function to reload the page
	const handleReloadPage = () => {
		window.location.reload();
	};

	// Show notification when doctor enters (for patients)
	useEffect(() => {
		if (userRole === "PATIENT" && isActive && !hasDismissedNotification) {
			// Only show notification if status actually changed
			if (!showDoctorEnteredNotification) {
				setShowDoctorEnteredNotification(true);
				// Play a sound to alert the patient
				const audio = new Audio("/sounds/notification.mp3");
				audio.play().catch(err => console.log("Could not play notification sound:", err));

				// Also show toast notification
				const doctorName = shouldUseStatusHook && 'doctorName' in appointmentStatus.status
					? appointmentStatus.status.doctorName
					: undefined;

				toast.success(
					doctorName
						? `Dr. ${doctorName} entrou na consulta`
						: 'O médico está na sala!',
					{
						action: {
							label: 'Entrar Agora',
							onClick: handleJoin,
						},
						duration: 10000, // 10 seconds
					}
				);

				// Vibrate if supported
				if ('vibrate' in navigator) {
					navigator.vibrate([200, 100, 200]);
				}
			}
		}
	}, [isActive, userRole, hasDismissedNotification, showDoctorEnteredNotification, shouldUseStatusHook, appointmentStatus.status]);

	return (
		<div className="flex max-w-xl flex-col gap-6">
			{/* Alerta de médico online para pacientes, mesmo que a notificação tenha sido dispensada */}
			{isPatient && isActive && !showDoctorEnteredNotification && (
				<Alert variant="primary" className="bg-primary/10 border-primary">
					<Check className="h-4 w-4 text-primary" />
					<AlertTitle className="text-primary">O médico já iniciou a consulta</AlertTitle>
					<AlertDescription>
						Você pode entrar na consulta a qualquer momento clicando no botão "Entrar na consulta agora" abaixo.
					</AlertDescription>
				</Alert>
			)}

			{/* Doctor Entered Notification for patients */}
			{isPatient && showDoctorEnteredNotification && isActive && (
				<Card className="border-primary bg-primary/5 animate-pulse">
					<CardHeader className="pb-2">
						<div className="flex items-center justify-between">
							<CardTitle className="text-primary flex items-center">
								<Check className="mr-2 h-5 w-5" /> O médico está na sala!
							</CardTitle>
							<Badge className="bg-primary text-white">ONLINE</Badge>
						</div>
						<CardDescription>
							O médico iniciou a consulta e está aguardando você entrar.
						</CardDescription>
					</CardHeader>
				</Card>
			)}

			<Card>
				<CardHeader>
					<CardTitle>Preparação da consulta</CardTitle>
					<CardDescription>
						Configure seus dispositivos antes de entrar na chamada.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-8">
					{/* Alerta para problemas de permissão */}
					{hasTrackPermissionError && (
						<Alert variant="error">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Erro de permissão</AlertTitle>
							<AlertDescription>
								Você precisa permitir o acesso à câmera e ao microfone para
								participar da consulta.
							</AlertDescription>
						</Alert>
					)}

					{/* Alerta para erro de status da consulta */}
					{appointmentStatus.error && (
						<Alert variant="error">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Erro de conexão</AlertTitle>
							<AlertDescription>
								{appointmentStatus.error.message}
								{appointmentStatus.refreshStatus !== null && (
									<Button
										variant="outline"
										size="sm"
										className="mt-2"
										onClick={handleManualRefresh}
										disabled={isManuallyRefreshing}
									>
										{isManuallyRefreshing ? (
											<>
												<Loader2 className="mr-2 h-3 w-3 animate-spin" />
												Atualizando...
											</>
										) : (
											<>
												<RefreshCcw className="mr-2 h-3 w-3" />
												Tentar novamente
											</>
										)}
									</Button>
								)}
							</AlertDescription>
						</Alert>
					)}



					{/* Tabs para configurar câmera e áudio */}
					<Tabs defaultValue="camera" className="w-full">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="camera">Câmera</TabsTrigger>
							<TabsTrigger value="audio">Áudio</TabsTrigger>
						</TabsList>
						<TabsContent value="camera" className="space-y-4">
							<div className="flex h-[200px] items-center justify-center rounded-md border bg-black text-white">
								{videoEnabled && mediaDevices.hasCamera ? (
									<div className="h-full w-full overflow-hidden rounded-md">
										<video
											id="preview"
											className="h-full w-full object-cover"
											autoPlay
											playsInline
											muted
											ref={(node) => {
												if (node && videoEnabled) {
													navigator.mediaDevices
														.getUserMedia({ video: true })
														.then((stream) => {
															node.srcObject = stream;
														})
														.catch((err) => {
															console.error(
																"Error accessing video device:",
																err
															);
														});
												}
											}}
										/>
									</div>
								) : (
									<div className="flex h-full w-full items-center justify-center bg-gray-900">
										<p>{!mediaDevices.hasCamera ? "Nenhuma câmera detectada" : "Câmera desativada"}</p>
									</div>
								)}
							</div>

							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Ativar câmera</span>
								<Switch
									checked={videoEnabled && mediaDevices.hasCamera}
									onCheckedChange={setVideoEnabled}
									disabled={!mediaDevices.hasCamera}
								/>
							</div>
							{!mediaDevices.hasCamera && (
								<p className="text-xs text-muted-foreground">
									Nenhuma câmera detectada. Conecte uma câmera para habilitar o vídeo.
								</p>
							)}
						</TabsContent>
						<TabsContent value="audio" className="space-y-4">
							<div className="flex h-[200px] flex-col items-center justify-center gap-4 rounded-md border bg-muted p-6">
								<div className="flex flex-col items-center">
									<div className="flex items-center justify-between space-x-8">
										<span className="text-sm font-medium">Ativar microfone</span>
										<Switch
											checked={audioEnabled && mediaDevices.hasMicrophone}
											onCheckedChange={setAudioEnabled}
											disabled={!mediaDevices.hasMicrophone}
										/>
									</div>
									{!mediaDevices.hasMicrophone && (
										<p className="mt-2 text-xs text-muted-foreground">
											Nenhum microfone detectado. Conecte um microfone para habilitar o áudio.
										</p>
									)}
								</div>
							</div>
						</TabsContent>
					</Tabs>
				</CardContent>
				<CardFooter className="flex-col gap-2">
					{/* Informações sobre status da consulta baseadas no papel do usuário */}
					{isDoctor && !isActive && (
						<p className="text-center text-sm text-muted-foreground">
							Você deverá iniciar a consulta para que o paciente possa entrar na sala.
						</p>
					)}


						{/* Status da consulta para pacientes */}
						{isPatient && (
						<div className="rounded-md border p-4">
							<div className="flex items-center justify-between mb-2">
								<h3 className="text-sm font-medium">Status da consulta</h3>
								<Badge
									className={isActive ? "bg-green-600 text-white" : ""}
								>
									{isActive ? "MÉDICO ONLINE" : "AGUARDANDO MÉDICO"}
								</Badge>
							</div>
							<p className="text-sm text-muted-foreground">
								{isActive
									? "O médico já iniciou a consulta e está aguardando você entrar."
									: "Aguarde o médico iniciar a consulta. Esta página será atualizada automaticamente quando o médico entrar."}
							</p>

							{/* Removido botão de entrar, mantendo apenas verificação e recarga */}
							{!isActive && (
								<div className="mt-4 justify-center  flex gap-2">
									<Button
										variant="outline"
										size="sm"
										className="text-xs"
										onClick={handleManualRefresh}
										disabled={isManuallyRefreshing || appointmentStatus.refreshStatus === null}
									>
										{isManuallyRefreshing ? (
											<>
												<Loader2 className="mr-1 h-3 w-3 animate-spin" />
												Verificando...
											</>
										) : (
											<>
												<RefreshCcw className="mr-1 h-3 w-3" />
												Verificar novamente
											</>
										)}
									</Button>

								</div>
							)}
						</div>
					)}


				</CardFooter>
			</Card>
		</div>
	);
}
