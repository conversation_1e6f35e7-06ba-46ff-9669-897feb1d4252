import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

export function ConsultationRoomSkeleton() {
	return (
		<div className="flex h-[calc(100vh-4rem)] flex-col">
			{/* Header Skeleton */}
			<div className="flex items-center justify-between border-b bg-background p-4">
				<div className="flex items-center gap-4">
					<Skeleton className="h-10 w-10 rounded-full" />
					<div className="space-y-2">
						<Skeleton className="h-4 w-32" />
						<Skeleton className="h-3 w-24" />
					</div>
				</div>
				<Skeleton className="h-9 w-24" />
			</div>

			{/* Main Content Skeleton */}
			<div className="flex flex-1 overflow-hidden">
				{/* Video Area Skeleton */}
				<div className="relative flex-1 overflow-hidden bg-muted/10 p-4">
					<Card className="flex h-full items-center justify-center">
						<div className="space-y-4 text-center">
							<Skeleton className="mx-auto h-16 w-16 rounded-full" />
							<div className="space-y-2">
								<Skeleton className="mx-auto h-4 w-48" />
								<Skeleton className="mx-auto h-3 w-32" />
							</div>
						</div>
					</Card>
				</div>

				{/* Chat Area Skeleton */}
				<div className="hidden w-[400px] border-l lg:block">
					<div className="flex h-full flex-col">
						<div className="border-b p-4">
							<Skeleton className="h-6 w-32" />
						</div>
						<div className="flex-1 space-y-4 p-4">
							{[1, 2, 3].map((i) => (
								<div key={i} className="flex gap-3">
									<Skeleton className="h-8 w-8 rounded-full" />
									<div className="space-y-2">
										<Skeleton className="h-3 w-24" />
										<Skeleton className="h-16 w-[280px] rounded-lg" />
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
