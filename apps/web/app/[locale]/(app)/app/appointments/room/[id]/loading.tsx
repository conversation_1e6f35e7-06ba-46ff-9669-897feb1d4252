// app/(dashboard)/dashboard/digital-room/[id]/loading.tsx
import { Loader2 } from "lucide-react";

import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";

export default function DigitalRoomLoading() {
	return (
		<div className="flex h-dvh flex-col">
			{/* Header Skeleton */}
			<div className="border-b p-4">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-4">
						<Skeleton className="h-12 w-12 rounded-full" />
						<div className="space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-3 w-24" />
						</div>
					</div>

					<Skeleton className="h-10 w-[120px]" />
				</div>
			</div>

			{/* Content Skeleton */}
			<div className="flex flex-1 gap-4 p-4">
				<Card className="flex flex-[2] items-center justify-center">
					<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
				</Card>

				<Card className="flex-1 p-4">
					<div className="flex h-full flex-col gap-4">
						<div className="flex-1 space-y-4">
							{Array.from({ length: 3 }).map((_, i) => (
								<div key={i} className="flex items-start gap-3">
									<Skeleton className="h-8 w-8 rounded-full" />
									<div className="space-y-2">
										<Skeleton className="h-4 w-[200px]" />
										<Skeleton className="h-3 w-[100px]" />
									</div>
								</div>
							))}
						</div>

						<Skeleton className="h-10 w-full" />
					</div>
				</Card>
			</div>
		</div>
	);
}
