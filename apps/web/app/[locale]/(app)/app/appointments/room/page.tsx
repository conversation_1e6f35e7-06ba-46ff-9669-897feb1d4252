import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { createApiCaller } from "api/trpc/caller";
import { AppointmentStatus } from "@prisma/client";
import { getLocale } from "next-intl/server";
import { Suspense } from "react";

import Loading from "../../doctors/loading";
import { AppointmentsClient } from "../components/appointments-client";


interface PageProps {
  searchParams: {
    page?: string;
    status?: string;
    date?: string;
  };
}

export default async function AppointmentsPage({ searchParams }: PageProps) {
  const locale = await getLocale();
  const apiCaller = await createApiCaller();
  const { user } = await currentUser();

  if (!user) {
    return redirect({ href: "/auth/login", locale });
  }

  // Parse search params
  const resolvedSearchParams = await searchParams;
  const page = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
  const status = resolvedSearchParams?.status as AppointmentStatus | undefined;
  const date = resolvedSearchParams?.date;

  try {
    const result = await apiCaller.appointments.list({
      page,
      perPage: 9,
      status,
      startDate: date,
    });

    return (
      <Suspense fallback={<Loading />}>
        <AppointmentsClient
          appointments={result.appointments}
          currentStatus={status}
          pagination={{
            page: result.pagination.page,
            pages: result.pagination.pages
          }}
        />
      </Suspense>
    );
  } catch (error) {
    console.error("Error loading appointments:", error);
    return <div>Falha ao carregar consultas</div>;
  }
}
