// components/consultation-room/chat/chat-interface.tsx
"use client";

import { format } from "date-fns";
import {
	Download,
	Eye,
	FileText,
	Loader2,
	MessageSquare,
	Mic,
	Paperclip,
	RefreshCw,
	Send,
	X,
	Video,
	AlertCircle,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { validateFile } from "@shared/lib/file-validations";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { ScrollArea } from "@ui/components/scroll-area";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { useAudioRecorder } from "../hooks/use-audio-recorder";
import { useChat } from "../hooks/use-chat";
import { getCompleteImageUrl } from "@lib/image-utils";
import { Dialog, DialogContent, Di<PERSON>D<PERSON><PERSON>, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { endConsultation } from "../../../../../../../../actions/appointments/messages/messages";
import { SignedAvatar } from "../../../../../../../../components/shared/signed-avatar";

interface ChatInterfaceProps {
	appointment: {
		id: string;
		doctor: {
			user: {
				id: string;
				name: string;
				image?: string | null;
			};
		};
		patient: {
			user: {
				id: string;
				name: string;
				image?: string | null;
			};
		};
	};
	userRole: "DOCTOR" | "PATIENT";
}

export function ChatInterface({ appointment, userRole }: ChatInterfaceProps) {
	const [messageText, setMessageText] = useState("");
	const [isSending, setIsSending] = useState(false);
	const [reconnecting, setReconnecting] = useState(false);
	const [showMedicalRecordDialog, setShowMedicalRecordDialog] = useState(false);
	const [isEndingConsultation, setIsEndingConsultation] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({
			behavior: "smooth",
			block: "end",
		});
	};

	const { messages = [], sendMessage, sendAudio, sendAttachment, isLoading, error, refreshMessages } =
		useChat(appointment?.id || "");
	const {
		isRecording,
		audioBlob,
		startRecording,
		stopRecording,
		cancelRecording,
		resetRecording,
	} = useAudioRecorder();

	// Add loading state check here
	if (!appointment?.id) {
		return (
			<div className="flex h-full items-center justify-center">
				<Loader2 className="h-8 w-8 animate-spin text-primary" />
			</div>
		);
	}

	const handleSendMessage = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!messageText.trim() || isSending) return;

		try {
			setIsSending(true);
			await sendMessage(messageText);
			setMessageText("");
			scrollToBottom();
		} catch (error) {
			console.error("Error sending message:", error);
			toast.error("Erro ao enviar mensagem");
		} finally {
			setIsSending(false);
		}
	};

	const handleReconnect = () => {
		setReconnecting(true);
		try {
			refreshMessages();
			toast.success("Reconexão iniciada");
		} catch (error) {
			console.error("Error reconnecting:", error);
			toast.error("Erro ao reconectar");
		} finally {
			setTimeout(() => setReconnecting(false), 1000);
		}
	};

	const handleFileUpload = async (
		event: React.ChangeEvent<HTMLInputElement>,
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		try {
			validateFile(file);
			setIsSending(true);
			await sendAttachment(file);
			scrollToBottom();

			if (fileInputRef.current) {
				fileInputRef.current.value = "";
			}
		} catch (error) {
			toast.error(
				error instanceof Error ? error.message : "Erro ao enviar arquivo",
			);
		} finally {
			setIsSending(false);
		}
	};

	// Open medical record dialog
	const handleOpenMedicalRecord = () => {
		setShowMedicalRecordDialog(true);
	};

	// End consultation
	const handleEndConsultation = async () => {
		try {
			setIsEndingConsultation(true);
			const { success, error, appointment: updatedAppointment } = await endConsultation(appointment.id);

			if (!success || error) {
				throw new Error(error || "Erro ao finalizar consulta");
			}

			// Recarregar mensagens para mostrar a mensagem do sistema
			await refreshMessages();

			toast.success("Consulta finalizada com sucesso");
		} catch (error) {
			console.error("Error ending consultation:", error);
			toast.error(error instanceof Error ? error.message : "Erro ao finalizar consulta");
		} finally {
			setIsEndingConsultation(false);
		}
	};

	// Add a new useEffect to handle automatic scrolling
	useEffect(() => {
		if (!isLoading && messages?.length > 0) {
			scrollToBottom();
		}
	}, [messages, isLoading]);

	const renderMessageContent = (message: any) => {
		switch (message.type) {
			case "AUDIO":
				return (
					<div className="w-full min-w-[200px] max-w-[300px] overflow-hidden rounded-lg">
						<audio controls preload="metadata" className="h-[36px] w-full">
							<source src={message.content} type="audio/webm" />
							<source src={message.content} type="audio/mpeg" />
							Seu navegador não suporta o elemento de áudio.
						</audio>
					</div>
				);

			case "FILE":
				if (message.metadata?.contentType === "application/pdf") {
					return (
						<div className="flex items-center gap-3 rounded-lg bg-transparent p-3">
							<FileText className="h -8 w-8 shrink-0 text-white" />
							<div className="min-w-0 flex-1">
								<p className="truncate font-medium text-sm">
									{message.metadata?.fileName || "Documento PDF"}
								</p>
								<div className="mt-1 flex gap-2">
									<a
										href={message.content}
										target="_blank"
										rel="noopener noreferrer"
										className="inline-flex items-center gap-1 text-white text-xs hover:underline"
									>
										<Eye className="h-3 w-3" />
										Visualizar
									</a>
									<a
										href={message.content}
										download
										className="inline-flex items-center gap-1 text-xs text-white hover:underline"
									>
										<Download className="h-3 w-3" />
										Download
									</a>
								</div>
							</div>
						</div>
					);
				}

				if (message.metadata?.contentType?.startsWith("image/")) {
					return (
						<div className="relative max-w-sm overflow-hidden rounded-lg">
							<img
								src={getCompleteImageUrl(message.content)}
								alt="Imagem anexada"
								className="h-auto w-full object-cover"
								loading="lazy"
								onError={(e) => {
									// e.currentTarget.src = "/placeholder-image.png";
								}}
							/>
						</div>
					);
				}

				return (
					<div className="flex items-center gap-3 rounded-lg bg-secondary/10 p-3">
						<Paperclip className="h-6 w-6 shrink-0" />
						<div className="min-w-0 flex-1">
							<p className="truncate font-medium text-sm">
								{message.metadata?.fileName || "Arquivo anexado"}
							</p>
							<a
								href={message.content}
								download
								className="inline-flex items-center gap-1 text-primary text-xs hover:underline"
							>
								<Download className="h-3 w-3" />
								Baixar arquivo
							</a>
						</div>
					</div>
				);

			case "SYSTEM":
				return (
					<div className="italic text-muted-foreground">
						{message.content}
					</div>
				);

			default:
				return (
					<p className="whitespace-pre-wrap break-words">{message.content}</p>
				);
		}
	};

	return (
		<div className="flex h-full flex-col">
			{/* Header with buttons */}
			<div className="flex items-center justify-between border-b p-3">
				<div className="flex items-center gap-2">
					<SignedAvatar
						imagePath={userRole === "DOCTOR" ? appointment.patient.user.image ?? null : appointment.doctor.user.image ?? null}
						name={userRole === "DOCTOR" ? appointment.patient.user.name : appointment.doctor.user.name}
						className="h-8 w-8"
					/>
					<div>
						<h3 className="text-sm font-medium">
							{userRole === "DOCTOR" ? appointment.patient.user.name : appointment.doctor.user.name}
						</h3>
					</div>
				</div>
				<div className="flex items-center gap-2">

				</div>
			</div>

			<ScrollArea className="flex-1 p-4">
				{isLoading ? (
					<div className="space-y-4">
						{/* Simple message skeletons for consultation room */}
						{[1, 2, 3, 4, 5].map((i) => (
							<div key={i} className={cn(
								"flex items-start gap-3",
								i % 2 === 0 ? "flex-row-reverse" : "flex-row"
							)}>
								<Skeleton className="h-8 w-8 rounded-full" />
								<div className={cn(
									"max-w-[70%] space-y-1",
									i % 2 === 0 && "text-right"
								)}>
									<Skeleton className="h-3 w-16" />
									<div className="rounded-lg p-3 bg-muted">
										<Skeleton className={cn(
											"h-4",
											i % 2 === 0 ? "w-24" : "w-32"
										)} />
									</div>
									<Skeleton className="h-3 w-12" />
								</div>
							</div>
						))}
					</div>
				) : error ? (
					<div className="flex h-full flex-col items-center justify-center text-center">
						<MessageSquare className="h-12 w-12 text-muted-foreground/50" />
						<h3 className="mt-4 text-base font-medium">Nenhuma mensagem</h3>
						<p className="mt-2 text-sm text-muted-foreground">
							Comece a conversar enviando uma mensagem
						</p>
					</div>
				) : messages?.length === 0 ? (
					<div className="flex h-full flex-col items-center justify-center text-center">
						<MessageSquare className="h-12 w-12 text-muted-foreground/50" />
						<h3 className="mt-4 text-base font-medium">Nenhuma mensagem</h3>
						<p className="mt-2 text-sm text-muted-foreground">
							Comece a conversar enviando uma mensagem
						</p>
					</div>
				) : (
					<div className="space-y-4">
						{messages.map((message) => {
							const isSender =
								message.senderId ===
								(userRole === "DOCTOR"
									? appointment.doctor.user.id
									: appointment.patient.user.id);

							// Special case for SYSTEM messages
							if (message.type === "SYSTEM") {
								return (
									<div key={message.id} className="flex justify-center my-4">
										<div className="px-4 py-2 bg-muted rounded-md text-sm">
											{renderMessageContent(message)}
										</div>
									</div>
								);
							}

							return (
								<div
									key={message.id}
									className={cn(
										"flex items-start gap-3",
										isSender ? "flex-row-reverse" : "flex-row",
									)}
								>
									<SignedAvatar
										imagePath={
											(isSender
												? userRole === "DOCTOR" ? appointment.doctor.user.image : appointment.patient.user.image
												: userRole === "DOCTOR" ? appointment.patient.user.image : appointment.doctor.user.image
											) ?? null
										}
										name={
											(isSender
												? userRole === 'DOCTOR' ? appointment.doctor.user.name : appointment.patient.user.name
												: userRole === 'DOCTOR' ? appointment.patient.user.name : appointment.doctor.user.name
											) ?? 'Usuário'
										}
										className="h-8 w-8"
									/>

									<div
										className={cn(
											"max-w-[70%] space-y-1",
											isSender && "text-right",
										)}
									>
										<p className="text-muted-foreground text-xs">
											{isSender ? "Você" : (userRole === "DOCTOR" ? appointment.patient.user.name : appointment.doctor.user.name)}
										</p>

										<div
											className={cn(
												"rounded-lg p-3",
												isSender
													? "bg-primary text-primary-foreground"
													: "bg-secondary/90 text-primary-foreground"
											)}
										>
											{renderMessageContent(message)}
										</div>

										<span className="text-xs text-muted-foreground">
											{format(new Date(message.createdAt), "HH:mm")}
										</span>
									</div>
								</div>
							);
						})}
						<div ref={messagesEndRef} style={{ height: "1px" }} />
					</div>
				)}
			</ScrollArea>

			{/* Realtime status bar */}
			<div className="border-t px-4 py-1 flex justify-between items-center">
				<span className="text-xs text-muted-foreground">
					{messages.length} mensagens
				</span>
				{!isLoading && (
					<Button
						type="button"
						variant="ghost"
						size="sm"
						onClick={handleReconnect}
						disabled={reconnecting}
					>
						{reconnecting ? (
							<Loader2 className="h-3 w-3 animate-spin" />
						) : (
							<RefreshCw className="h-3 w-3" />
						)}
					</Button>
				)}
			</div>

			{/* Input Area */}
			<div className="border-t p-4">
				<form onSubmit={handleSendMessage} className="flex items-center gap-2">
					<input
						type="file"
						ref={fileInputRef}
						onChange={handleFileUpload}
						className="hidden"
						accept="image/*,.pdf"
					/>

					<Button
						type="button"
						variant="ghost"
						size="icon"
						className="shrink-0"
						onClick={() => fileInputRef.current?.click()}
					>
						<Paperclip className="h-5 w-5" />
					</Button>

					<Button
						type="button"
						variant="ghost"
						size="icon"
						className="shrink-0"
						onClick={isRecording ? stopRecording : startRecording}
					>
						<Mic className={cn("h-5 w-5", isRecording && "text-destructive")} />
					</Button>

					<div className="flex-1">
						<textarea
							value={messageText}
							onChange={(e) => setMessageText(e.target.value)}
							onKeyDown={(e) => {
								if (e.key === "Enter" && !e.shiftKey) {
									e.preventDefault();
									handleSendMessage(e);
								}
							}}
							placeholder="Digite sua mensagem..."
							className="w-full text-sm resize-none rounded-md border bg-background px-3 py-3"
							rows={1}
						/>
					</div>

					<Button
						type="submit"
						size="icon"
						className="mb-1 h-12"
						disabled={!messageText.trim() || isSending}
					>
						{isSending ? (
							<Loader2 className="h-5 w-5 animate-spin" />
						) : (
							<Send className="h-5 w-5" />
						)}
					</Button>
				</form>

				{/* Audio Recording Preview */}
				{audioBlob && (
					<div className="mt-2 flex items-center gap-2 rounded-lg bg-muted p-2">
						<audio
							src={URL.createObjectURL(audioBlob)}
							controls
							className="flex-1"
						/>
						<Button
							type="button"
							variant="ghost"
							size="sm"
							onClick={resetRecording}
						>
							<X className="h-4 w-4" />
						</Button>
						<Button
							type="button"
							size="sm"
							onClick={() => {
								sendAudio(audioBlob);
								resetRecording();
							}}
						>
							<Send className="h-4 w-4" />
						</Button>
					</div>
				)}
			</div>

			{/* Medical Record Dialog */}
			<Dialog open={showMedicalRecordDialog} onOpenChange={setShowMedicalRecordDialog}>
				<DialogContent className="sm:max-w-3xl">
					<DialogHeader>
						<DialogTitle>Prontuário</DialogTitle>
						<DialogDescription>
							Preencha as informações do prontuário para esta consulta
						</DialogDescription>
					</DialogHeader>
					<div className="space-y-4 py-4">
						<div className="space-y-2">
							<label htmlFor="complaint" className="text-sm font-medium">Queixa Principal</label>
							<textarea
								id="complaint"
								className="w-full resize-none rounded-md border bg-background px-3 py-2"
								rows={3}
								placeholder="Descreva a queixa principal do paciente..."
							/>
						</div>
						<div className="space-y-2">
							<label htmlFor="diagnosis" className="text-sm font-medium">Diagnóstico</label>
							<textarea
								id="diagnosis"
								className="w-full resize-none rounded-md border bg-background px-3 py-2"
								rows={3}
								placeholder="Descreva o diagnóstico..."
							/>
						</div>
						<div className="space-y-2">
							<label htmlFor="conduct" className="text-sm font-medium">Conduta</label>
							<textarea
								id="conduct"
								className="w-full resize-none rounded-md border bg-background px-3 py-2"
								rows={3}
								placeholder="Descreva a conduta médica..."
							/>
						</div>
					</div>
					<div className="flex justify-end gap-2">
						<Button variant="outline" onClick={() => setShowMedicalRecordDialog(false)}>Cancelar</Button>
						<Button onClick={() => {
							toast.success("Prontuário salvo com sucesso");
							setShowMedicalRecordDialog(false);
						}}>Salvar</Button>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
