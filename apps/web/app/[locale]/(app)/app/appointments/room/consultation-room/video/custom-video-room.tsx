import {
	Grid<PERSON>ayout,
	LiveKitRoom,
	ParticipantTile,
	RoomAudioRenderer,
	StartAudio,
	useLocalParticipant,
	useParticipants,
	useRoomContext,
	useTracks,
} from "@livekit/components-react";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { Track } from "livekit-client";
import {
	Camera,
	Loader2,
	MessageSquare,
	Mic,
	MonitorOff,
	PhoneOff,
	Settings,
} from "lucide-react";
import { useEffect, useState } from "react";

interface CustomVideoRoomProps {
	token: string | null;
	serverUrl: string | null;
	isDoctor: boolean;
	isCompleted?: boolean;
	onError?: (error: Error) => void;
	onDisconnected?: () => void;
	showChat: boolean;
	onToggleChat: () => void;
	onReturnToPreJoin?: () => void;
}

export function CustomVideoRoom({
	token,
	serverUrl,
	isDoctor,
	isCompleted = false,
	onError,
	onDisconnected,
	showChat,
	onToggleChat,
	onReturnToPreJoin,
}: CustomVideoRoomProps) {
	const [isConnecting, setIsConnecting] = useState(true);

	// Se a consulta foi finalizada e não é o médico, mostrar mensagem
	if (!isDoctor && isCompleted) {
		return (
			<Card className="flex h-full items-center justify-center bg-muted/30">
				<div className="text-center">
					<MonitorOff className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h2 className="font-medium text-lg">
						Consulta finalizada pelo médico
					</h2>
					<p className="text-sm text-muted-foreground mt-2">
						Você ainda pode acessar o chat para visualizar o histórico e as informações compartilhadas.
					</p>
				</div>
			</Card>
		);
	}

	// Mostrar placeholder quando não há conexão
	if (!token || !serverUrl) {
		return (
			<Card className="flex h-full items-center justify-center bg-muted/30">
				<div className="text-center px-4">
					<MonitorOff className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
					<h2 className="font-medium text-lg">
						{isDoctor
							? "Clique para iniciar a videochamada"
							: "Aguardando médico iniciar a consulta"}
					</h2>

					<div className="w-full flex flex-col gap-3 justify-center p-4">
						{onReturnToPreJoin && (
							<Button variant="outline" onClick={onReturnToPreJoin}>
								<Settings className="mr-2 h-4 w-4" />
								Configurar dispositivos
							</Button>
						)}
						<Button variant="secondary" onClick={() => window.location.reload()}>
							Atualizar página
						</Button>
					</div>
				</div>
			</Card>
		);
	}

	return (
		<LiveKitRoom
			token={token}
			serverUrl={serverUrl}
			connect={true}
			className="flex h-full flex-col overflow-hidden rounded-lg bg-muted/30"
			data-lk-theme="default"
			style={
				{
					"--lk-bg": "transparent",
					"--lk-control-bg": "var(--background)",
					"--lk-text-color": "var(--foreground)",
					"--lk-video-bg": "var(--muted)",
				} as React.CSSProperties
			}
			onConnected={() => {
				console.log("Connected to room");
				setIsConnecting(false);
			}}
			onError={(error) => {
				console.error("Room connection error:", error);
				setIsConnecting(false);
				onError?.(error);
			}}
			onDisconnected={() => {
				console.log("Disconnected from room");
				onDisconnected?.();
			}}
		>
			{/* Loading State */}
			{isConnecting && (
				<div className="absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
					<div className="text-center">
						<Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" />
						<p className="mt-2 text-muted-foreground text-sm">
							Conectando à sala...
						</p>
					</div>
				</div>
			)}

			{/* Main Video Area */}
			<div className="relative flex-1">
				<AdaptiveVideoLayout />
			</div>

			{/* Control Bar - Always visible, fixed at bottom */}
			<div className="border-t bg-gradient-to-t from-background/80 to-background/60 p-4 backdrop-blur">
				<div className="flex items-center justify-center gap-4">
					<Controls
						showChat={showChat}
						onToggleChat={onToggleChat}
						isDoctor={isDoctor}
						onDisconnect={onDisconnected}
						onReturnToPreJoin={onReturnToPreJoin}
					/>
				</div>
			</div>

			<RoomAudioRenderer />
			<StartAudio label="Iniciar áudio" />
		</LiveKitRoom>
	);
}

function AdaptiveVideoLayout() {
	const tracks = useTracks([
		{ source: Track.Source.Camera, withPlaceholder: true },
		{ source: Track.Source.ScreenShare, withPlaceholder: false },
	]);
	const participants = useParticipants();
	const hasScreenShare = tracks.some(
		(track) => track.source === Track.Source.ScreenShare,
	);

	// Lógica para layout adaptativo baseado no número de participantes
	const layoutClassName = cn(
		"h-full w-full",
		participants.length <= 2
			? "grid grid-cols-1 md:grid-cols-2 gap-2"
			: participants.length <= 4
				? "grid grid-cols-2 gap-2"
				: "grid grid-cols-2 md:grid-cols-3 gap-2",
	);

	if (hasScreenShare) {
		return (
			<div className="grid h-full grid-cols-1 grid-rows-[2fr,1fr] gap-2 md:grid-cols-[2fr,1fr] md:grid-rows-1">
				{/* Screen Share */}
				<GridLayout
					tracks={tracks.filter(
						(track) => track.source === Track.Source.ScreenShare,
					)}
					className="h-full"
				>
					<ParticipantTile className="h-full [&>div]:rounded-lg [&>div]:bg-muted [&>div]:p-2" />
				</GridLayout>

				{/* Camera Feeds */}
				<GridLayout
					tracks={tracks.filter(
						(track) => track.source === Track.Source.Camera,
					)}
					className="h-full"
				>
					<ParticipantTile className="h-full [&>div]:rounded-lg [&>div]:bg-muted [&>div]:p-2" />
				</GridLayout>
			</div>
		);
	}

	return (
		<GridLayout tracks={tracks} className={layoutClassName}>
			<ParticipantTile className="h-full [&>div]:rounded-lg [&>div]:bg-muted [&>div]:p-2" />
		</GridLayout>
	);
}

function Controls({
	isDoctor,
	onDisconnect,
	showChat,
	onToggleChat,
	onReturnToPreJoin,
}: {
	isDoctor: boolean;
	onDisconnect?: () => void;
	showChat: boolean;
	onToggleChat: () => void;
	onReturnToPreJoin?: () => void;
}) {
	const { localParticipant } = useLocalParticipant();
	const room = useRoomContext();

	const toggleMicrophone = async () => {
		try {
			await localParticipant?.setMicrophoneEnabled(
				!localParticipant.isMicrophoneEnabled,
			);
		} catch (error) {
			console.error("Error toggling microphone:", error);
		}
	};

	const toggleCamera = async () => {
		try {
			await localParticipant?.setCameraEnabled(
				!localParticipant.isCameraEnabled,
			);
		} catch (error) {
			console.error("Error toggling camera:", error);
		}
	};

	const handleDisconnect = () => {
		try {
			room?.disconnect();
			onDisconnect?.();
		} catch (error) {
			console.error("Error disconnecting:", error);
		}
	};

	return (
		<>
			<Button
				variant={localParticipant?.isMicrophoneEnabled ? "default" : "outline"}
				size="icon"
				className="h-10 w-10"
				onClick={toggleMicrophone}
				title="Microfone"
			>
				<Mic
					className={cn(
						"h-5 w-5",
						!localParticipant?.isMicrophoneEnabled && "text-muted-foreground",
					)}
				/>
			</Button>

			<Button
				variant={localParticipant?.isCameraEnabled ? "default" : "outline"}
				size="icon"
				className="h-10 w-10"
				onClick={toggleCamera}
				title="Câmera"
			>
				<Camera
					className={cn(
						"h-5 w-5",
						!localParticipant?.isCameraEnabled && "text-muted-foreground",
					)}
				/>
			</Button>

			{onReturnToPreJoin && (
				<Button
					variant="outline"
					size="icon"
					className="h-10 w-10"
					onClick={onReturnToPreJoin}
					title="Configurações"
				>
					<Settings className="h-5 w-5" />
				</Button>
			)}

			<Button
				variant={showChat ? "default" : "outline"}
				size="icon"
				className="h-10 w-10"
				onClick={onToggleChat}
				title={showChat ? "Ocultar chat" : "Mostrar chat"}
			>
				<MessageSquare
					className={cn("h-5 w-5", !showChat && "text-muted-foreground")}
				/>
			</Button>

			<Button
				variant="error"
				size="icon"
				className="h-10 w-10"
				onClick={handleDisconnect}
				title="Sair da chamada"
			>
				<PhoneOff className="h-5 w-5" />
			</Button>
		</>
	);
}
