// apps/web/app/[locale]/(app)/app/appointments/page.tsx
import { redirect } from "@i18n/routing";
import { currentUser } from "@saas/auth/lib/current-user";
import { createApiCaller } from "api/trpc/caller";
import { AppointmentStatus, UserRole } from "@prisma/client";
import { getLocale } from "next-intl/server";
import Loading from "./loading";
import { AppointmentsClient } from "./components/appointments-client";

interface PageProps {
	searchParams: {
		page?: string;
		status?: string;
		date?: string;
	};
}

export default async function AppointmentsPage({ searchParams }: PageProps) {
	const locale = await getLocale();
	const apiCaller = await createApiCaller();
	const { user } = await currentUser();

	if (!user) {
		return redirect({ href: "/auth/login", locale });
	}

	// Parse search params
	const resolvedSearchParams = await searchParams;
	const page = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
	const status = resolvedSearchParams?.status as AppointmentStatus | undefined;
	const date = resolvedSearchParams?.date;

	try {
		// Log helpful debug information
		console.log(`[Server] Fetching appointments for user:`, {
			userId: user.id,
			role: user.role,
			page,
			status
		});

		const result = await apiCaller.appointments.list({
			page,
			perPage: 9,
			status,
		});

		console.log(`[Server] Found ${result.appointments.length} appointments`);

		return (
			<AppointmentsClient
				initialAppointments={result.appointments}
				currentStatus={status}
				pagination={{
					page: result.pagination.page,
					pages: result.pagination.pages
				}}
				userRole={user.role as UserRole}
			/>
		);
	} catch (error) {
		console.error("[Server] Error loading appointments:", error);
		return <div className="p-8 text-center">Falha ao carregar consultas. Tente novamente mais tarde.</div>;
	}
}
