"use client";

import { Button } from "@ui/components/button";
import { useRouter, useSearchParams } from "next/navigation";

export function StatusFilter() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const currentStatus = searchParams.get("status");

	const handleStatusChange = (status: string | null) => {
		const params = new URLSearchParams(searchParams);
		if (status) {
			params.set("status", status);
		} else {
			params.delete("status");
		}
		params.delete("page");
		router.push(`/app/appointments?${params.toString()}`);
		router.refresh();
	};

	return (
		<div className="flex gap-2">
			<Button
				variant={!currentStatus ? "default" : "outline"}
				size="sm"
				onClick={() => handleStatusChange(null)}
			>
				Todas
			</Button>
			<Button
				variant={currentStatus === "SCHEDULED" ? "default" : "outline"}
				size="sm"
				className="bg-blue-50 text-blue-800 hover:bg-blue-100"
				onClick={() => handleStatusChange("SCHEDULED")}
			>
				Agendadas
			</Button>
			<Button
				variant={currentStatus === "IN_PROGRESS" ? "default" : "outline"}
				size="sm"
				className="bg-yellow-50 text-yellow-800 hover:bg-yellow-100"
				onClick={() => handleStatusChange("IN_PROGRESS")}
			>
				Em Andamento
			</Button>
			<Button
				variant={currentStatus === "COMPLETED" ? "default" : "outline"}
				size="sm"
				className="bg-green-50 text-green-800 hover:bg-green-100"
				onClick={() => handleStatusChange("COMPLETED")}
			>
				Finalizadas
			</Button>
			<Button
				variant={currentStatus === "CANCELED" ? "default" : "outline"}
				size="sm"
				className="bg-red-50 text-red-800 hover:bg-red-100"
				onClick={() => handleStatusChange("CANCELED")}
			>
				Canceladas
			</Button>
		</div>
	);
}
