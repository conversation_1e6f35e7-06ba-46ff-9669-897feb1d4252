'use client';

import { Button } from '@ui/components/button';
import { Calendar } from '@ui/components/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@ui/components/popover';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale'; // Add ptBR locale
import { Calendar as CalendarIcon } from 'lucide-react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useMediaQuery } from '@ui/hooks/use-media-query';

interface DateFilterProps {
	onDateSelected?: (date: Date | undefined) => void;
}

export function DateFilter({ onDateSelected }: DateFilterProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const pathname = usePathname();
	const date = searchParams.get('date');
	const { isMobile } = useMediaQuery();

	const handleDateSelect = (date: Date | undefined) => {
		const params = new URLSearchParams(searchParams);

		if (date) {
			params.set('date', format(date, 'yyyy-MM-dd'));
		} else {
			params.delete('date');
		}

		// Reset pagination when changing filters
		params.delete('page');

		// Update URL without full navigation
		const newUrl = `${pathname}?${params.toString()}`;
		window.history.pushState({}, '', newUrl);

		// Notify parent component if callback provided
		if (onDateSelected) {
			onDateSelected(date);
		}
	};

	// Reset date filter
	const handleReset = () => {
		const params = new URLSearchParams(searchParams);
		params.delete('date');
		params.delete('page');

		// Update URL without full navigation
		const newUrl = `${pathname}?${params.toString()}`;
		window.history.pushState({}, '', newUrl);

		// Notify parent component
		if (onDateSelected) {
			onDateSelected(undefined);
		}
	};

	return (
		<div className={`flex items-center gap-2 ${isMobile ? 'w-full' : ''}`}>
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant='outline'
						className={`justify-start text-left font-normal ${
							isMobile ? 'w-full' : ''
						}`}
					>
						<CalendarIcon className='mr-2 h-4 w-4' />
						{date
							? format(new Date(date), isMobile ? 'dd/MM/yyyy' : 'PPP', {
									locale: ptBR,
							  })
							: 'Filtrar por data'}
					</Button>
				</PopoverTrigger>
				<PopoverContent className='w-auto p-0' align='start'>
					<Calendar
						mode='single'
						selected={date ? new Date(date) : undefined}
						onSelect={handleDateSelect}
						initialFocus
						locale={ptBR}
						formatters={{
							formatCaption: (date) =>
								format(date, 'LLLL yyyy', { locale: ptBR }),
							formatDay: (date) => format(date, 'd', { locale: ptBR }),
						}}
					/>
				</PopoverContent>
			</Popover>

			{date && (
				<Button
					variant='ghost'
					size='icon'
					onClick={handleReset}
					title='Limpar filtro de data'
					className={isMobile ? 'ml-2' : ''}
				>
					<span className='sr-only'>Limpar filtro de data</span>×
				</Button>
			)}
		</div>
	);
}
