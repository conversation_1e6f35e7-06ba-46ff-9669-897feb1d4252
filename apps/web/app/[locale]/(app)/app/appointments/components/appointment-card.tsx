'use client';

import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
	Building2,
	ClipboardPlus,
	Clock,
	MoreVertical,
	User,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import type { Appointment } from '@prisma/client';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@ui/components/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader } from '@ui/components/card';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';
import {
	Sheet,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON>ead<PERSON>,
	SheetTit<PERSON>,
} from '@ui/components/sheet';
import { AppointmentDetails } from './appointment-details';
import { UserAvatar } from '../../../../../../components/shared/user-avatar';

interface Action {
	label: string;
	onClick: () => void;
	variant?: 'default' | 'destructive';
}

interface AppointmentCardProps {
	appointment: Appointment & {
		doctor?: {
			id?: string;
			user?: {
				id?: string;
				name?: string;
				avatarUrl?: string | null;
				email?: string;
				phone?: string | null;
			};
			specialties?: Array<{ id: string; name: string }>;
		};
		patient?: {
			id?: string;
			cpf?: string;
			user?: {
				id?: string;
				name?: string;
				email?: string;
				phone?: string | null;
				avatarUrl?: string | null;
			};
		};
		hospital?: {
			id?: string;
			name?: string;
			address?: any;
		};
	};
	actions: Action[];
	onCancel?: (appointmentId: string) => Promise<void>;
	isMobile?: boolean;
}

const safeParseDate = (dateValue: Date | string | null) => {
	if (!dateValue) return new Date();

	try {
		if (dateValue instanceof Date) return dateValue;

		// If it's an ISO string (from API/JSON)
		const parsedDate = new Date(dateValue);
		if (!isNaN(parsedDate.getTime())) return parsedDate;

		// Fallback
		return new Date();
	} catch (error) {
		console.error('Error parsing date:', error);
		return new Date();
	}
};

export function AppointmentCard({
	appointment,
	actions,
	onCancel,
	isMobile = false,
}: AppointmentCardProps) {
	const [open, setOpen] = useState(false);
	const [isAlertOpen, setIsAlertOpen] = useState(false);
	const router = useRouter();

	console.log('Appointment data in card:', {
		appointmentId: appointment.id,
		doctor: appointment.doctor,
		patient: appointment.patient,
		doctorName: appointment.doctor?.user?.name || 'Não disponível',
		patientName: appointment.patient?.user?.name || 'Não disponível',
	});

	// Format the appointment data for the detail view
	const formattedAppointment = {
		...appointment,
		doctor: appointment.doctor
			? {
					...appointment.doctor,
					specialties: appointment.doctor.specialties || [],
					user: {
						...appointment.doctor?.user,
						id: appointment.doctor?.user?.id || 'unknown',
						email: appointment.doctor?.user?.email || '<EMAIL>',
						phone: appointment.doctor?.user?.phone || null,
					},
			  }
			: {
					id: appointment.doctorId || 'unknown',
					user: {
						id: 'unknown',
						name: 'Médico não disponível',
						email: '<EMAIL>',
						avatarUrl: null,
						phone: null,
					},
					specialties: [],
			  },
		patient: appointment.patient
			? {
					...appointment.patient,
					user: {
						...appointment.patient?.user,
						id: appointment.patient?.user?.id || 'unknown',
						email: appointment.patient?.user?.email || '<EMAIL>',
						phone: appointment.patient?.user?.phone || null,
					},
					cpf: appointment.patient.cpf || '00000000000',
			  }
			: {
					id: appointment.patientId || 'unknown',
					user: {
						id: 'unknown',
						name: 'Paciente não disponível',
						email: '<EMAIL>',
						avatarUrl: null,
						phone: null,
					},
					cpf: '00000000000',
			  },
		hospital: appointment.hospital || null,
	};

	const handleCancel = async () => {
		try {
			await onCancel?.(appointment.id);
			setIsAlertOpen(false);
			toast.success('Consulta cancelada com sucesso!');
		} catch (error) {
			toast.error('Erro ao cancelar consulta');
		}
	};

	const statusConfig = {
		SCHEDULED: { label: 'Agendada', color: 'bg-blue-100 text-blue-800' },
		IN_PROGRESS: {
			label: 'Em Andamento',
			color: 'bg-yellow-100 text-yellow-800',
		},
		COMPLETED: { label: 'Finalizada', color: 'bg-green-100 text-green-800' },
		CANCELED: { label: 'Cancelada', color: 'bg-red-100 text-red-800' },
		NO_SHOW: { label: 'Não Compareceu', color: 'bg-gray-100 text-gray-800' },
	};

	const paymentStatusConfig = {
		PENDING: { label: 'Pendente', color: 'bg-yellow-100 text-yellow-800' },
		PAID: { label: 'Pago', color: 'bg-green-100 text-green-800' },
		REFUNDED: { label: 'Reembolsado', color: 'bg-purple-100 text-purple-800' },
		FAILED: { label: 'Falhou', color: 'bg-red-100 text-red-800' },
	};

	const getStatusLabel = () => {
		return statusConfig[appointment.status]?.label || 'Desconhecido';
	};

	const getStatusColor = () => {
		return (
			statusConfig[appointment.status]?.color || 'bg-gray-100 text-gray-800'
		);
	};

	return (
		<>
			<Card
				onClick={() => setOpen(true)}
				className='cursor-pointer transition-shadow hover:shadow-lg w-full'
			>
				<CardHeader className='relative flex flex-row items-center justify-between px-3 sm:px-6 py-3 sm:py-4'>
					<div className='flex items-center gap-4'>
						<UserAvatar
							user={{
								name: appointment.doctor?.user?.name,
								avatarUrl: appointment.doctor?.user?.avatarUrl,
							}}
							className='h-12 w-12'
						/>
						<div>
							<Badge className={getStatusColor()}>{getStatusLabel()}</Badge>
							<p className='text-muted-foreground text-sm'>
								{format(
									safeParseDate(appointment.scheduledAt),
									isMobile ? 'dd/MM' : 'PPP',
									{
										locale: ptBR,
									}
								)}
							</p>
						</div>
					</div>
					{actions.length > 0 && (
						<div className='absolute right-4 top-4'>
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										variant='ghost'
										className='h-8 w-8 p-0'
										onClick={(e) => e.stopPropagation()}
									>
										<MoreVertical className='h-4 w-4' />
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align='end'>
									{actions.map((action) => (
										<DropdownMenuItem
											key={action.label}
											onClick={(e) => {
												e.stopPropagation();
												action.onClick();
											}}
											className={
												action.variant === 'destructive'
													? 'text-destructive'
													: ''
											}
										>
											{action.label}
										</DropdownMenuItem>
									))}
								</DropdownMenuContent>
							</DropdownMenu>
						</div>
					)}
				</CardHeader>
				<CardContent className='space-y-4 px-3 sm:px-6 py-2 sm:py-4'>
					<div className='space-y-2'>
						<div className='flex items-center gap-2'>
							<ClipboardPlus className='h-4 w-4 text-muted-foreground' />
							<span className='text-sm truncate'>
								{appointment.doctor?.user?.name || 'Médico não especificado'}
							</span>
						</div>
						<div className='flex items-center gap-2'>
							<User className='h-4 w-4 text-muted-foreground' />
							<span className='text-sm truncate'>
								{appointment.patient?.user?.name || 'Paciente não especificado'}
							</span>
						</div>
						<div className='flex items-center gap-2'>
							<Clock className='h-4 w-4 text-muted-foreground' />
							<span className='text-sm font-medium'>
								{format(safeParseDate(appointment.scheduledAt), 'HH:mm')}
							</span>
						</div>
					</div>
				</CardContent>
			</Card>

			<Sheet open={open} onOpenChange={setOpen}>
				<SheetContent className='w-full bg-white sm:max-w-xl'>
					<SheetHeader>
						<SheetTitle>Detalhes da Consulta</SheetTitle>
					</SheetHeader>
					<AppointmentDetails appointment={formattedAppointment as any} />
				</SheetContent>
			</Sheet>

			<AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Cancelar consulta</AlertDialogTitle>
						<AlertDialogDescription>
							Tem certeza que deseja cancelar esta consulta? Esta ação não pode
							ser desfeita.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Não, manter consulta</AlertDialogCancel>
						<AlertDialogAction
							className='bg-destructive text-destructive-foreground hover:bg-destructive/90'
							onClick={handleCancel}
						>
							Sim, cancelar consulta
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
