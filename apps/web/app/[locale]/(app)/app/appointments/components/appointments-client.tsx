'use client';

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@ui/components/alert-dialog';
import {
	Sheet,
	She<PERSON><PERSON>ontent,
	Sheet<PERSON>eader,
	SheetTitle,
} from '@ui/components/sheet';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Button } from '@ui/components/button';
import { PaginationButton } from '@ui/components/pagination-button';

import type { AppointmentStatus } from '@prisma/client';
import type { UserRole } from '@prisma/client';
import { useUser } from '@saas/auth/hooks/use-user';
import { apiClient } from '@shared/lib/api-client';
import { DashboardHeader } from '@ui/components/header';
import { Calendar, Loader2, Plus, Filter } from 'lucide-react';
import { useState, useEffect } from 'react';
import { AppointmentCard } from './appointment-card';
import { AppointmentDetails } from './appointment-details';
import { AppointmentSheet } from './appointment-sheet';
import { DateFilter } from './date-filter';
import { useMediaQuery } from '@ui/hooks/use-media-query';

interface AppointmentsClientProps {
	initialAppointments: any[];
	currentStatus?: AppointmentStatus;
	userRole: UserRole;
	pagination: {
		page: number;
		pages: number;
	};
}

// Define a proper type for the action variants
type ActionVariant = 'default' | 'destructive' | undefined;

// Define a type for actions
interface Action {
	label: string;
	onClick: () => void;
	variant?: ActionVariant;
}

const getAppointmentActions = (
	appointment: any,
	userRole: string,
	handlers: {
		handleViewDetails: (appointment: any) => void;
		handleCancel: (appointment: any) => void;
	}
): Action[] => {
	const baseActions: Action[] = [
		{
			label: 'Ver detalhes',
			onClick: () => handlers.handleViewDetails(appointment),
		},
	];

	// Se a consulta não estiver agendada, retorna apenas ações básicas
	if (appointment.status !== 'SCHEDULED') {
		return baseActions;
	}

	switch (userRole) {
		case 'ADMIN':
		case 'HOSPITAL':
			return [
				...baseActions,
				{
					label: 'Cancelar consulta',
					onClick: () => handlers.handleCancel(appointment),
					variant: 'destructive',
				},
			];

		case 'DOCTOR':
		case 'PATIENT':
			return [
				...baseActions,
				{
					label: 'Cancelar consulta',
					onClick: () => handlers.handleCancel(appointment),
					variant: 'destructive',
				},
			];

		default:
			return baseActions;
	}
};

export function AppointmentsClient({
	initialAppointments,
	currentStatus,
	userRole: userRoleFromProps,
	pagination,
}: AppointmentsClientProps) {
	const router = useRouter();
	const session = useUser();
	const { isMobile } = useMediaQuery();
	const [appointments, setAppointments] = useState(initialAppointments || []);
	const [isLoading, setIsLoading] = useState(false);
	const [selectedAppointment, setSelectedAppointment] = useState<any>(null);
	const [isSheetOpen, setIsSheetOpen] = useState(false);
	const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
	const [isAlertOpen, setIsAlertOpen] = useState(false);
	const [isFilterOpen, setIsFilterOpen] = useState(false);

	// Update local appointments when props change
	useEffect(() => {
		console.log('Appointments received from props:', initialAppointments);
		setAppointments(initialAppointments || []);
	}, [initialAppointments]);

	// Mutation para cancelar consulta
	const cancelAppointmentMutation = apiClient.appointments.cancel.useMutation({
		onSuccess: () => {
			toast.success('Consulta cancelada com sucesso!');
			router.refresh();
		},
		onError: (error) => {
			console.error('Error cancelling appointment:', error);
			toast.error(error.message || 'Erro ao cancelar consulta');
		},
	});

	const handleViewDetails = (appointment: any) => {
		console.log('Opening appointment details for:', appointment.id);

		// Format the appointment data for the detail view
		const formattedAppointment = {
			...appointment,
			doctor: appointment.doctor
				? {
						...appointment.doctor,
						specialties: appointment.doctor.specialties || [],
						user: {
							...appointment.doctor?.user,
							id: appointment.doctor?.user?.id || 'unknown',
							email: appointment.doctor?.user?.email || '<EMAIL>',
							phone: appointment.doctor?.user?.phone || null,
						},
				  }
				: {
						id: appointment.doctorId || 'unknown',
						user: {
							id: 'unknown',
							name: appointment.doctor?.user?.name || 'Médico não disponível',
							email: '<EMAIL>',
							avatarUrl: null,
							phone: null,
						},
						specialties: [],
				  },
			patient: appointment.patient
				? {
						...appointment.patient,
						user: {
							...appointment.patient?.user,
							id: appointment.patient?.user?.id || 'unknown',
							email: appointment.patient?.user?.email || '<EMAIL>',
							phone: appointment.patient?.user?.phone || null,
						},
						cpf: appointment.patient.cpf || '00000000000',
				  }
				: {
						id: appointment.patientId || 'unknown',
						user: {
							id: 'unknown',
							name:
								appointment.patient?.user?.name || 'Paciente não disponível',
							email: '<EMAIL>',
							avatarUrl: null,
							phone: null,
						},
						cpf: '00000000000',
				  },
			hospital: appointment.hospital || null,
		};

		setSelectedAppointment(formattedAppointment);
		setIsSheetOpen(true);
	};

	const handleCancel = (appointment: any) => {
		setSelectedAppointment(appointment);
		setIsAlertOpen(true);
	};

	const confirmCancel = async () => {
		if (!selectedAppointment) return;

		try {
			await cancelAppointmentMutation.mutateAsync({
				id: selectedAppointment.id,
			});
			setIsAlertOpen(false);
			setSelectedAppointment(null);
		} catch (error) {
			// Erro já é tratado nos callbacks da mutation
		}
	};

	const handleStatusChange = (newStatus: AppointmentStatus | 'ALL') => {
		const url = new URL(window.location.href);

		if (newStatus === 'ALL') {
			url.searchParams.delete('status');
		} else {
			url.searchParams.set('status', newStatus);
		}

		url.searchParams.set('page', '1');
		window.history.pushState({}, '', url.toString());

		router.refresh();
	};

	const handleDateSelected = (date?: Date) => {
		console.log('Date selected:', date);
		router.refresh();
	};

	const handleAppointmentCreated = async () => {
		toast.success('Consulta agendada com sucesso!');
		router.refresh();
	};

	// Obter o papel do usuário
	const userRole = session?.user?.role || userRoleFromProps;

	// Extrair doctorId com segurança
	const doctorId =
		session?.user?.role === 'DOCTOR' && session?.user?.id
			? session.user.id
			: undefined;

	// Estado para exibição quando não há consultas
	const hasAppointments = appointments?.length > 0;
	const showActionButtons = userRole !== 'PATIENT';

	return (
		<div className='flex flex-col h-full gap-2 sm:gap-6'>
			{/* Header adaptado para mobile */}
			<div
				className={`${
					isMobile ? 'flex-col space-y-3' : 'flex items-center justify-between'
				}`}
			>
				<DashboardHeader
					heading='Consultas'
					text='Gerencie as consultas.'
					className='mb-0 w-full'
				/>
				{showActionButtons && (
					<div
						className={`${
							isMobile ? 'flex w-full' : 'flex items-center gap-4'
						}`}
					>
						{isMobile ? (
							<div className='grid grid-cols-2 gap-2 w-full'>
								<Button
									variant='outline'
									className='flex items-center justify-center'
									onClick={() => setIsFilterOpen(!isFilterOpen)}
								>
									<Filter className='h-4 w-4 mr-2' />
									Filtrar
								</Button>
								<Button
									variant='default'
									className='flex items-center justify-center'
									onClick={() => setIsCreateSheetOpen(true)}
								>
									<Plus className='h-4 w-4 mr-2' />
									Agendar
								</Button>
							</div>
						) : (
							<>
								<DateFilter onDateSelected={handleDateSelected} />
								<Button
									variant='default'
									onClick={() => setIsCreateSheetOpen(true)}
								>
									Agendar Nova Consulta
								</Button>
							</>
						)}

						<AppointmentSheet
							userRole={userRole}
							doctorId={doctorId}
							onAppointmentCreated={handleAppointmentCreated}
							isOpen={isCreateSheetOpen}
							onOpenChange={setIsCreateSheetOpen}
						/>
					</div>
				)}

				{/* Filtro de data para mobile - exibido apenas quando o botão de filtro é clicado */}
				{isMobile && isFilterOpen && (
					<div className='w-full mt-2'>
						<DateFilter onDateSelected={handleDateSelected} />
					</div>
				)}
			</div>

			{isLoading ? (
				<div className='flex justify-center items-center p-8'>
					<Loader2 className='h-8 w-8 animate-spin text-primary mr-2' />
					<p>Carregando consultas...</p>
				</div>
			) : hasAppointments ? (
				<div className='grid grid-cols-1 gap-4 px-2 sm:px-4 md:grid-cols-2 lg:grid-cols-3 mb-16 md:mb-0'>
					{appointments.map((appointment) => (
						<AppointmentCard
							key={appointment.id}
							appointment={appointment}
							actions={getAppointmentActions(appointment, userRole, {
								handleViewDetails,
								handleCancel,
							})}
							isMobile={isMobile}
						/>
					))}
				</div>
			) : (
				<div className='flex flex-col justify-center items-center p-12 text-center space-y-6'>
					<div className='bg-muted rounded-full p-6'>
						<Calendar className='h-16 w-16 text-primary' />
					</div>

					<div className='space-y-2 max-w-md'>
						<h3 className='text-xl font-medium'>Nenhuma consulta encontrada</h3>
						<p className='text-muted-foreground'>
							{userRole === 'PATIENT'
								? 'Você ainda não tem nenhuma consulta agendada. Agende sua primeira consulta para receber atendimento médico.'
								: userRole === 'DOCTOR'
								? 'Você ainda não tem consultas agendadas. Aguarde pacientes agendarem ou crie uma nova consulta.'
								: 'Não há consultas agendadas no sistema. Crie uma nova consulta para começar.'}
						</p>
					</div>
				</div>
			)}

			{/* Sheet para detalhes */}
			<Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
				<SheetContent className='w-full sm:max-w-xl'>
					<SheetHeader>
						<SheetTitle>Detalhes da Consulta</SheetTitle>
					</SheetHeader>
					{selectedAppointment ? (
						<AppointmentDetails appointment={selectedAppointment} />
					) : (
						<div className='mt-4 text-center text-gray-500'>
							<p>Nenhuma consulta selecionada para visualização.</p>
						</div>
					)}
				</SheetContent>
			</Sheet>

			{/* Dialog de confirmação de cancelamento */}
			<AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Cancelar consulta</AlertDialogTitle>
						<AlertDialogDescription>
							Tem certeza que deseja cancelar esta consulta? Esta ação não pode
							ser desfeita.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Não, manter consulta</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmCancel}
							className='bg-destructive text-destructive-foreground hover:bg-destructive/90'
						>
							Sim, cancelar consulta
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{pagination.pages > 1 && (
				<div className='mt-auto pt-4 flex justify-center'>
					<PaginationButton
						currentPage={pagination.page}
						totalPages={pagination.pages}
						baseUrl='/app/appointments'
					/>
				</div>
			)}
		</div>
	);
}
