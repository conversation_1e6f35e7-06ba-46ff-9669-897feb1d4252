"use client";

import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
	ArrowLeft,
	Calendar,
	CalendarClock,
	MapPin,
	MessageSquare,
	Video,
	X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { Link } from "@i18n/routing";
import { AppointmentStatus, ConsultType } from "@prisma/client";
import { DialogClose } from "@radix-ui/react-dialog";
import { useUser } from "@saas/auth/hooks/use-user";
import { apiClient } from "@shared/lib/api-client";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogD<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>ger,
} from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { getCompleteImageUrl } from "../../../../../../lib/image-utils";
import { SignedAvatar } from "../../../../../../components/shared/signed-avatar";
import { UserAvatar } from "../../../../../../components/shared/user-avatar";
import { CalendarDays } from "lucide-react";
import { ChatBubbleIcon } from "@radix-ui/react-icons";

// Helper function to safely parse dates
const safeParseDate = (dateValue: Date | string | null) => {
	if (!dateValue) return new Date();

	try {
		if (dateValue instanceof Date) return dateValue;

		// If it's an ISO string (from API/JSON)
		const parsedDate = new Date(dateValue);
		if (!isNaN(parsedDate.getTime())) return parsedDate;

		// Fallback
		return new Date();
	} catch (error) {
		console.error("Error parsing date:", error);
		return new Date();
	}
};

// Helpers para exibir os status
const statusConfig = {
	SCHEDULED: { label: "Agendada", color: "bg-blue-100 text-blue-800" },
	IN_PROGRESS: {
		label: "Em Andamento",
		color: "bg-yellow-100 text-yellow-800",
	},
	COMPLETED: { label: "Finalizada", color: "bg-green-100 text-green-800" },
	CANCELED: { label: "Cancelada", color: "bg-red-100 text-red-800" },
	NO_SHOW: { label: "Não Compareceu", color: "bg-gray-100 text-gray-800" },
};

const paymentStatusConfig = {
	PENDING: { label: "Pendente", color: "bg-yellow-100 text-yellow-800" },
	PAID: { label: "Pago", color: "bg-green-100 text-green-800" },
	REFUNDED: { label: "Reembolsado", color: "bg-purple-100 text-purple-800" },
	FAILED: { label: "Falhou", color: "bg-red-100 text-red-800" },
	CANCELED: { label: "Cancelado", color: "bg-gray-100 text-gray-800" },
};

const consultTypeIcons = {
	VIDEO: <Video className="h-4 w-4" />,
	AUDIO: <MessageSquare className="h-4 w-4" />,
	CHAT: <MessageSquare className="h-4 w-4" />,
};

type DoctorType = {
	id: string;
	user: {
		id: string;
		name: string | null;
		email: string;
		avatarUrl: string | null;
		phone: string | null;
	};
	specialties: Array<{
		id: string;
		name: string;
	}>;
	consultationPrice?: number | null;
	consultationDuration?: number | null;
};

type PatientType = {
	id: string;
	user: {
		id: string;
		name: string | null;
		email: string;
		avatarUrl: string | null;
		phone: string | null;
	};
	cpf: string;
};

type HospitalType = {
	id: string;
	name: string;
	logoUrl: string | null;
	contactEmail: string | null;
	contactPhone: string | null;
} | null;

type AppointmentWithRelations = {
	id: string;
	status: AppointmentStatus;
	scheduledAt: Date | string;
	symptoms?: string | null;
	consultType: ConsultType;
	amount?: number | null;
	hospitalId: string | null;
	doctor: DoctorType;
	patient: PatientType;
	hospital: HospitalType;
	duration: number;
};

interface AppointmentDetailsProps {
	appointment: AppointmentWithRelations;
	hideBackButton?: boolean;
}

export function AppointmentDetails({ appointment, hideBackButton = false }: AppointmentDetailsProps) {
	const router = useRouter();
	const { user } = useUser();

	const [cancelReason, setCancelReason] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);

	// Debug log to help diagnose issues
	console.log("AppointmentDetails render with data:", {
		appointment,
		appointmentId: appointment?.id,
		status: appointment?.status,
		doctorInfo: appointment?.doctor,
		patientInfo: appointment?.patient,
		timestamp: new Date().toISOString()
	});

	// Mutation para cancelar consulta
	const cancelAppointmentMutation = apiClient.appointments.cancel.useMutation();

	const handleCancel = async () => {
		setIsSubmitting(true);

		try {
			await cancelAppointmentMutation.mutateAsync({
				id: appointment.id,
				reason: cancelReason,
			});

			toast.success("Consulta cancelada com sucesso");
			router.refresh();
		} catch (error: any) {
			toast.error(error.message || "Erro ao cancelar consulta");
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleStartConsultation = () => {
					router.push(`/app/zapchat?appointment=${appointment.id}`);
	};

	// Verificar se a consulta pode ser cancelada
	const canCancel =
		appointment.status === AppointmentStatus.SCHEDULED &&
		(user?.role === "DOCTOR" || user?.role === "ADMIN");

	// Verificar se é possível iniciar a consulta
	const canStart =
		appointment.status === AppointmentStatus.SCHEDULED &&
		(user?.role === "DOCTOR" || user?.role === "PATIENT");

	// Calcular tempo até a consulta
	const now = new Date();
	const appointmentDate = safeParseDate(appointment.scheduledAt);
	const isToday =
		now.getDate() === appointmentDate.getDate() &&
		now.getMonth() === appointmentDate.getMonth() &&
		now.getFullYear() === appointmentDate.getFullYear();

	const timeUntilAppointment = appointmentDate.getTime() - now.getTime();
	const minutesUntil = Math.floor(timeUntilAppointment / (1000 * 60));

	// Verificar se a consulta está próxima (menos de 15 minutos)
	const isUpcoming = isToday && minutesUntil > 0 && minutesUntil <= 15;

	// Verificar se o usuário é médico ou paciente
	const isParticipant = user?.role === "DOCTOR" || user?.role === "PATIENT";

	// Verificar se o usuário pode acessar a consulta
	const canAccess =
		appointment.status !== AppointmentStatus.CANCELED &&
		(user?.role === "DOCTOR" || user?.role === "PATIENT");

	return (
		<>
			{/* {!hideBackButton && (
				<div className="mb-6">
					<div className="flex items-center gap-2">
						<Button variant="outline" size="sm" onClick={() => router.back()}>
							<ArrowLeft className="h-4 w-4" />
						</Button>
						<h2 className="text-lg font-semibold">Detalhes da Consulta</h2>
					</div>
				</div>
			)} */}

			<div className="flex flex-col gap-6 pb-8">
				{/* Card de Informações Gerais */}
				<Card>
					<CardHeader>
						<CardTitle>Informações Gerais</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex items-center justify-between">
							<div>
								<Badge className={statusConfig[appointment.status]?.color || "bg-gray-100 text-gray-800"}>
									{statusConfig[appointment.status]?.label || "Desconhecido"}
								</Badge>
							</div>
						</div>

						<div className="grid gap-4">
							<div className="flex items-center gap-2">
								<CalendarClock className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="text-muted-foreground text-sm">Data e Hora</p>
									<p className="font-muted-foregrounddium">
										{format(
											safeParseDate(appointment.scheduledAt),
											"PPP 'às' HH:mm",
											{
												locale: ptBR,
											},
										)}
									</p>
								</div>
							</div>

							{/* <div className="flex items-center gap-2">
								{consultTypeIcons[appointment.consultType]}
								<div>
									<p className="text-sm text-muted-foreground">
										Tipo de Consulta
									</p>
									<p className="font-medium">
										{appointment.consultType === "VIDEO"
											? "Vídeo"
											: appointment.consultType === "AUDIO"
												? "Áudio"
												: "Chat"}
									</p>
								</div>
							</div>

							<div className="flex items-center gap-2">
								<CreditCard className="h-5 w-5 text-muted-foreground" />
								<div>
									<p className="text-sm text-muted-foreground">Valor</p>
									<p className="font-medium">
										{appointment.amount
											? `R$ ${appointment.amount.toFixed(2)}`
											: "Gratuito"}
									</p>
								</div>
							</div> */}
						</div>

						{appointment.symptoms && (
							<div className="pt-4">
								<p className="mb-2 text-muted-foreground text-sm">
									Sintomas / Observações
								</p>
								<div className="flex-center rounded-md bg-muted/30 p-3">
									<p>{appointment.symptoms}</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Card do Médico */}
				<Card>
					<CardHeader>
						<CardTitle>Médico</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="mb-4 flex items-center gap-4">
							<UserAvatar
								user={appointment.doctor?.user || {}}
								className="h-16 w-16"
							/>
							<div>
								<p className="font-medium text-sm">
									{appointment.doctor?.user?.name || "Médico não identificado"}
								</p>
								<p className="text-muted-foreground text-sm">
									{appointment.doctor?.user?.email || "Email não disponível"}
								</p>
							</div>
						</div>

						{appointment.doctor?.specialties?.length > 0 && (
							<div className="space-y-3">
								<p className="text-muted-foreground text-sm">Especialidades</p>
								<div className="flex flex-wrap gap-2">
									{appointment.doctor?.specialties?.map((specialty) => (
										<Badge key={specialty.id} className="bg-muted">
											{specialty.name}
										</Badge>
									)) || <p className="text-sm text-muted-foreground">Nenhuma especialidade cadastrada</p>}
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Card do Paciente */}
				<Card>
					<CardHeader>
						<CardTitle>Paciente</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="flex items-center gap-4">
							<UserAvatar
								user={appointment.patient?.user || {}}
								className="h-16 w-16"
							/>
							<div>
								<h3 className="font-medium">{appointment.patient?.user?.name || "Paciente não identificado"}</h3>
								<p className="text-sm text-muted-foreground">
									{appointment.patient?.user?.email || "Email não disponível"}
								</p>
							</div>
						</div>


					</CardContent>
				</Card>



				{/* Ações atualizadas */}
				<div className="flex gap-3">
					{canAccess && (
						<Link
							href={`/app/zapchat?appointment=${appointment.id}`}
							className="flex-1"
						>
							<Button className="w-full">
								<MessageSquare className="mr-2 h-4 w-4" />
								{appointment.status === AppointmentStatus.IN_PROGRESS
									? "Continuar Consulta"
									: isUpcoming
										? "Iniciar Consulta"
										: "Acessar consulta"}
							</Button>
						</Link>
					)}

					{canCancel && (
						<Dialog>
							<DialogTrigger asChild>
								<Button className="bg-red-500 hover:bg-red-600 text-white flex-1">
									<X className="h-4 w-4 mr-2" />
									Cancelar Consulta
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Cancelar Consulta</DialogTitle>
									<DialogDescription>
										Tem certeza que deseja cancelar esta consulta? Esta ação não
										pode ser desfeita.
									</DialogDescription>
								</DialogHeader>

								<div className="py-4">
									<label className="font-medium text-sm">
										Motivo do cancelamento (opcional)
									</label>
									<Textarea
										placeholder="Informe o motivo do cancelamento"
										value={cancelReason}
										onChange={(e) => setCancelReason(e.target.value)}
										className="mt-2"
									/>
								</div>

								<DialogFooter>
									<DialogClose asChild>
										<Button variant="outline">Cancelar</Button>
									</DialogClose>
									<Button
										className="bg-red-500 hover:bg-red-600 text-white"
										onClick={handleCancel}
										disabled={isSubmitting}
									>
										{isSubmitting ? "Processando..." : "Confirmar Cancelamento"}
									</Button>
								</DialogFooter>
							</DialogContent>
						</Dialog>
					)}
				</div>
			</div>
		</>
	);
}
