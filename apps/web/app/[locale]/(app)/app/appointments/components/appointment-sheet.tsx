"use client";

import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import FullCalendar from "@fullcalendar/react";
import { AppointmentType, ConsultType, UserRole } from "@prisma/client";
import { useUser } from "@saas/auth/hooks/use-user";
import { apiClient } from "@shared/lib/api-client";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { ScrollArea } from "@ui/components/scroll-area";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetHeader,
	SheetTitle,
} from "@ui/components/sheet";
import { Textarea } from "@ui/components/textarea";
import { Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { UserAvatar } from "../../../../../../components/shared/user-avatar";

interface AppointmentSheetProps {
	userRole: UserRole;
	doctorId?: string;
	onAppointmentCreated?: () => void;
	isOpen?: boolean;
	onOpenChange?: (open: boolean) => void;
}

// Tipo para Doctor
type Doctor = {
	id: string;
	user: {
		name: string | null;
		avatarUrl: string | null;
		id?: string;
		email?: string;
		phone?: string | null;
	};
	specialties: Array<{ name: string; id?: string }>;
	consultationDuration: number | null;
	consultationPrice: number | null;
};

export function AppointmentSheet({
	userRole,
	doctorId: propDoctorId,
	onAppointmentCreated,
	isOpen: propIsOpen,
	onOpenChange,
}: AppointmentSheetProps) {
	// Estados do formulário
	const [isOpen, setIsOpen] = useState(false);
	const [currentStep, setCurrentStep] = useState(userRole === "DOCTOR" ? 2 : 1);
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();
	const { user } = useUser();

	// Estados para seleção
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
	const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
	const [selectedPatient, setSelectedPatient] = useState<any | null>(null);
	const [selectedTimeSlot, setSelectedTimeSlot] = useState<any | null>(null);
	const [selectedConsultType, setSelectedConsultType] = useState<ConsultType>(ConsultType.CHAT);
	const [selectedAppointmentType, setSelectedAppointmentType] = useState<AppointmentType>(AppointmentType.TELEMEDICINE);
	const [symptoms, setSymptoms] = useState<string>("");

	// Estados para filtragem
	const [searchDoctorTerm, setSearchDoctorTerm] = useState("");
	const [searchPatientTerm, setSearchPatientTerm] = useState("");

	// Estados para controle do fluxo de médico
	const [doctorId, setDoctorId] = useState<string | undefined>(propDoctorId);
	const [loadingDoctorProfile, setLoadingDoctorProfile] = useState(false);

	// Query para buscar o perfil do médico
	const { data: currentDoctor, isLoading: isLoadingCurrentDoctor } = apiClient.doctors.getCurrentDoctor.useQuery(
		undefined,
		{
			enabled: isOpen && userRole === "DOCTOR"
		}
	);

	console.log("currentDoctor raw data:", currentDoctor);

	// Efeito para configurar o médico quando os dados são carregados
	useEffect(() => {
		if (currentDoctor && !selectedDoctor) {
			console.log("Setting doctor data from currentDoctor:", currentDoctor);

			try {
				const doctorData = {
					id: currentDoctor.id,
					user: {
						name: currentDoctor.user?.name || "Nome não disponível",
						avatarUrl: currentDoctor.user?.avatarUrl || null,
						email: currentDoctor.user?.email || "",
						phone: currentDoctor.user?.phone || null
					},
					specialties: (currentDoctor.specialties || []).map(s => ({
						name: s.name,
						id: s.id
					})),
					consultationDuration: currentDoctor.consultationDuration || 30,
					consultationPrice: currentDoctor.consultationPrice
						? Number(currentDoctor.consultationPrice)
						: null
				};

				console.log("Processed doctor data:", doctorData);
				setDoctorId(currentDoctor.id);
				setSelectedDoctor(doctorData);
			} catch (error) {
				console.error("Error processing doctor data:", error);
				toast.error("Erro ao processar dados do médico");
			}
		}
	}, [currentDoctor, selectedDoctor]);

	// Efeito para configurar o doctorId inicial
	useEffect(() => {
		if (propDoctorId && !doctorId) {
			console.log("Setting doctorId from prop:", propDoctorId);
			setDoctorId(propDoctorId);
		}
	}, [propDoctorId, doctorId]);

	// Mutation para criar appointment
	const createAppointmentMutation = apiClient.appointments.create.useMutation();

	// Consultas tRPC
	const { data: doctorsData, isLoading: loadingDoctors } =
		apiClient.appointments.getDoctorsBySpecialty.useQuery(
			{ specialtyId: undefined },
			{
				enabled:
					isOpen &&
					(userRole === "ADMIN" ||
						userRole === "HOSPITAL" ||
						userRole === "SECRETARY")
			},
		);

	const { data: patientsData, isLoading: loadingPatients } =
		apiClient.appointments.getPatients.useQuery(
			{ searchTerm: searchPatientTerm },
			{
				enabled:
					isOpen &&
					(userRole === "DOCTOR" || selectedDoctor !== null)
			},
		);

	const { data: timeSlots, isLoading: loadingTimeSlots } =
		apiClient.appointments.getTimeSlots.useQuery(
			{
				doctorId: doctorId || "",
				date: selectedDate?.toISOString().split("T")[0] || "",
			},
			{
				enabled:
					isOpen &&
					!!doctorId &&
					!!selectedDate &&
					currentStep >= 4
			},
		);

	// Sincronizar com as props de isOpen
	useEffect(() => {
		if (propIsOpen !== undefined) {
			console.log("Syncing isOpen with prop:", propIsOpen);
			setIsOpen(propIsOpen);
		}
	}, [propIsOpen]);

	// Atualizar o onOpenChange quando isOpen muda
	useEffect(() => {
		if (onOpenChange) {
			onOpenChange(isOpen);
		}
	}, [isOpen, onOpenChange]);

	// Filtrar médicos
	const filteredDoctors =
		doctorsData?.filter(
			(doctor) =>
				doctor.user.name
					?.toLowerCase()
					.includes(searchDoctorTerm.toLowerCase()) ||
				doctor.specialties.some((spec) =>
					spec.name.toLowerCase().includes(searchDoctorTerm.toLowerCase()),
				),
		) || [];

	const displayedDoctors = searchDoctorTerm
		? filteredDoctors
		: filteredDoctors.slice(0, 6);

	// Filtrar pacientes
	const filteredPatients =
		patientsData?.patients.filter(
			(patient) =>
				patient.user.name
					?.toLowerCase()
					.includes(searchPatientTerm.toLowerCase()) ||
				patient.user.email
					.toLowerCase()
					.includes(searchPatientTerm.toLowerCase()) ||
				patient.cpf.includes(searchPatientTerm),
		) || [];

	// Filtrar horários disponíveis
	const availableTimeSlots =
		timeSlots?.filter((slot) => slot.isAvailable) || [];

	// Função para resetar o formulário
	const resetForm = () => {
		if (userRole !== "DOCTOR") {
			setSelectedDoctor(null);
			setDoctorId(undefined);
		}
		setSelectedPatient(null);
		setSelectedDate(new Date());
		setSelectedTimeSlot(null);
		setCurrentStep(userRole === "DOCTOR" ? 2 : 1);
		setSymptoms("");
		setSearchDoctorTerm("");
		setSearchPatientTerm("");
	};

	// Handlers
	const handleDoctorSelect = (doctor: Doctor) => {
		console.log("Doctor selected:", doctor);
		setSelectedDoctor(doctor);
		setDoctorId(doctor.id);
		setCurrentStep(2);
	};

	const handlePatientSelect = (patient: any) => {
		console.log("Patient selected:", patient);
		setSelectedPatient(patient);
		setCurrentStep(3);
	};

	const handleTimeSlotSelect = (timeSlot: any) => {
		console.log("TimeSlot selected:", timeSlot);
		setSelectedTimeSlot(timeSlot);
		setCurrentStep(5);
	};

	const handleCreateAppointment = async () => {
		if (!selectedDoctor || !selectedTimeSlot || !selectedDate) {
			toast.error("Informações incompletas. Preencha todos os campos.");
			return;
		}

		if (userRole === "DOCTOR" && !selectedPatient) {
			toast.error("Selecione um paciente para a consulta.");
			return;
		}

		// Verifica se o slot ainda está disponível
		try {
			setIsLoading(true);

			// Construir a data e hora da consulta
			const scheduledAt = new Date(
				`${format(selectedDate, "yyyy-MM-dd")}T${selectedTimeSlot.hour}:00`,
			);

			console.log("Creating appointment with data:", {
				doctorId: selectedDoctor.id,
				patientId: selectedPatient?.id,
				scheduledAt: scheduledAt.toISOString(),
				consultType: selectedConsultType,
				appointmentType: selectedAppointmentType,
				symptoms,
				duration: selectedDoctor.consultationDuration || 30,
				timeSlotId: selectedTimeSlot.id
			});

			// Criar consulta via tRPC
			const result = await createAppointmentMutation.mutateAsync({
				doctorId: selectedDoctor.id,
				patientId: selectedPatient?.id,
				scheduledAt: scheduledAt.toISOString(),
				consultType: selectedConsultType,
				appointmentType: selectedAppointmentType,
				symptoms,
				duration: selectedDoctor.consultationDuration || 30,
				timeSlotId: selectedTimeSlot.id
			});

			console.log("Appointment created:", result);

			// Fechar o modal e resetar formulário
			setIsOpen(false);
			resetForm();

			// Usar callback de AppointmentCreated se disponível
			if (onAppointmentCreated) {
				onAppointmentCreated();
			} else {
				// Caso não tenha callback, usar fallback para atualizar a página
				toast.success("Consulta agendada com sucesso!");
				router.refresh();
			}
		} catch (error) {
			console.error("Error creating appointment:", error);
			toast.error("Erro ao agendar consulta. Tente novamente.");
		} finally {
			setIsLoading(false);
		}
	};

	// Modificar a lógica de renderização dos steps no indicador de progresso
	const stepsToShow =
		userRole === "DOCTOR"
			? [2, 3, 4, 5] // Para médicos: Paciente, Data, Horário, Detalhes
			: [1, 2, 3, 4, 5]; // Para outros: Médico, Paciente, Data, Horário, Detalhes

	return (
		<Sheet open={isOpen} onOpenChange={setIsOpen}>
			<SheetContent
				side="right"
				className="overflow-y-auto bg-white sm:max-w-md md:max-w-lg lg:max-w-xl"
			>
				<SheetHeader>
					<SheetTitle>
						{currentStep === 1 && userRole !== "DOCTOR" && "Selecione um Médico"}
						{currentStep === 2 && "Selecione um Paciente"}
						{currentStep === 3 && "Selecione uma Data"}
						{currentStep === 4 && "Selecione um Horário"}
						{currentStep === 5 && "Detalhes da Consulta"}
					</SheetTitle>
					<SheetDescription>
						{currentStep === 1 && "Escolha o profissional para sua consulta"}
						{currentStep === 2 && "Selecione o paciente para atendimento"}
						{currentStep === 3 && "Escolha uma data disponível"}
						{currentStep === 4 && "Escolha um horário disponível"}
						{currentStep === 5 && "Confirme os detalhes e finalize o agendamento"}
					</SheetDescription>
				</SheetHeader>

				<ScrollArea className="mt-6 h-[calc(100vh-10rem)] pr-4">
					<div className="w-full">
						{/* Loading do perfil do médico */}
						{loadingDoctorProfile && (
							<div className="flex justify-center items-center p-8">
								<Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
								<p>Carregando perfil do médico...</p>
							</div>
						)}

						{/* Indicador de progresso */}
						<div className="mb-6">
							<div className="flex justify-between">
								{stepsToShow.map((step) => (
									<div key={step} className="flex flex-col items-center">
										<div
											className={`mb-2 flex h-8 w-8 items-center justify-center rounded-full
												${currentStep >= step ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"}`}
										>
											{userRole === "DOCTOR" ? step - 1 : step}
										</div>
										<div className="text-xs text-muted-foreground">
											{step === 1 && "Médico"}
											{step === 2 && "Paciente"}
											{step === 3 && "Data"}
											{step === 4 && "Horário"}
											{step === 5 && "Detalhes"}
										</div>
									</div>
								))}
							</div>
							<div className="mt-2 flex">
								{stepsToShow.slice(0, -1).map((step) => (
									<div
										key={`line-${step}`}
										className={`h-1 flex-1 ${currentStep > step ? "bg-primary" : "bg-muted"}`}
									/>
								))}
							</div>
						</div>

						{/* Step 1: Seleção de Médico (apenas para ADMIN, HOSPITAL e SECRETARY) */}
						{currentStep === 1 && userRole !== "DOCTOR" && (
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Selecione um Médico</h3>
								</div>

								<Input
									placeholder="Buscar médico por nome ou especialidade"
									value={searchDoctorTerm}
									onChange={(e) => setSearchDoctorTerm(e.target.value)}
									className="mb-4"
								/>

								{loadingDoctors ? (
									<div className="flex justify-center py-8">
										<Loader2 className="h-8 w-8 animate-spin text-primary" />
									</div>
								) : displayedDoctors && displayedDoctors.length > 0 ? (
									<div className="grid gap-4">
										{displayedDoctors.map((doctor) => (
											<Card
												key={doctor.id}
												className="cursor-pointer transition-shadow hover:shadow-md"
												onClick={() => handleDoctorSelect(doctor)}
											>
												<CardContent className="p-4 flex items-center space-x-4">
													<UserAvatar
														user={doctor.user}
														className="h-16 w-16"
													/>
													<div className="flex-1">
														<h3 className="text-lg font-semibold">
															{doctor.user.name}
														</h3>
														<p className="text-sm text-muted-foreground">
															{doctor.specialties.map((s) => s.name).join(", ")}
														</p>
														<div className="mt-1 flex items-center">
															<Badge>
																{doctor.consultationDuration || 30} min
															</Badge>
														</div>
													</div>
												</CardContent>
											</Card>
										))}
									</div>
								) : (
									<p className="text-center py-4 text-muted-foreground">
										Nenhum médico encontrado
									</p>
								)}
							</div>
						)}

						{/* Step 2: Seleção de Paciente */}
						{currentStep === 2 && (
							<div className="space-y-4">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Selecione um Paciente</h3>
									{userRole !== "DOCTOR" && (
										<Button
											variant="outline"
											size="sm"
											onClick={() => setCurrentStep(1)}
										>
											Voltar
										</Button>
									)}
								</div>

								{/* Mostrar o médico selecionado se for médico */}
								{userRole === "DOCTOR" && selectedDoctor && (
									<div className="mb-4 p-3 bg-muted/50 rounded-md">
										<div className="flex items-center gap-3">
											<UserAvatar
												user={selectedDoctor.user}
												className="h-10 w-10"
											/>
											<div>
												<p className="font-medium">{selectedDoctor.user.name || "Médico"}</p>
												<p className="text-xs text-muted-foreground">
													{selectedDoctor.specialties?.map(s => s.name).join(", ") || "Sem especialidades"}
												</p>
											</div>
										</div>
									</div>
								)}

								<Input
									placeholder="Buscar paciente por nome, email ou CPF"
									value={searchPatientTerm}
									onChange={(e) => setSearchPatientTerm(e.target.value)}
									className="mb-4"
								/>

								{loadingPatients ? (
									<div className="flex justify-center py-8">
										<Loader2 className="h-8 w-8 animate-spin text-primary" />
									</div>
								) : filteredPatients && filteredPatients.length > 0 ? (
									<div className="grid gap-4">
										{filteredPatients.map((patient) => (
											<Card
												key={patient.id}
												className="cursor-pointer transition-shadow hover:shadow-md"
												onClick={() => handlePatientSelect(patient)}
											>
												<CardContent className="flex items-center space-x-4 p-4">
													<UserAvatar
														user={patient.user}
														className="h-16 w-16"
													/>
													<div className="flex-1">
														<h3 className="text-lg font-semibold">
															{patient.user.name}
														</h3>
														<p className="text-sm text-muted-foreground">
															{patient.user.email}
														</p>
														<div className="mt-1">
															<Badge>
																CPF:{" "}
																{patient.cpf.replace(
																	/(\d{3})(\d{3})(\d{3})(\d{2})/,
																	"$1.$2.$3-$4",
																)}
															</Badge>
														</div>
													</div>
												</CardContent>
											</Card>
										))}
									</div>
								) : (
									<p className="py-4 text-center text-muted-foreground">
										Nenhum paciente encontrado
									</p>
								)}
							</div>
						)}

						{/* Step 3: Seleção de Data */}
						{currentStep === 3 && (
							<div className="space-y-6">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Selecione uma Data</h3>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentStep(2)}
									>
										Voltar
									</Button>
								</div>

								<div className="bg-background border rounded-md p-1">
									<FullCalendar
										plugins={[dayGridPlugin, interactionPlugin]}
										initialView="dayGridMonth"
										height="auto"
										locale={ptBR}
										headerToolbar={{
											left: "title",
											right: "prev,next",
										}}
										dateClick={(info) => {
											// Verificar se a data não é um dia passado
											const clickedDate = new Date(info.dateStr);
											const today = new Date();
											today.setHours(0, 0, 0, 0);

											if (clickedDate < today) {
												toast.error("Não é possível selecionar datas passadas");
												return;
											}

											// Verificar se é fim de semana
											const dayOfWeek = clickedDate.getDay();
											if (dayOfWeek === 0 || dayOfWeek === 6) {
												toast.error("Consultas não disponíveis em finais de semana");
												return;
											}

											setSelectedDate(clickedDate);
											setCurrentStep(4); // Avança para seleção de horário
										}}
										dayMaxEvents={0}
										selectable={true}
										selectAllow={(selectInfo) => {
											// Não permitir seleção de datas passadas
											const selectedDate = new Date(selectInfo.start);
											const today = new Date();
											today.setHours(0, 0, 0, 0);
											return selectedDate >= today;
										}}
										businessHours={{
											daysOfWeek: [1, 2, 3, 4, 5], // Segunda a Sexta
											startTime: "08:00",
											endTime: "18:00",
										}}
										validRange={{
											start: new Date(), // A partir de hoje
										}}
										contentHeight={400}
									/>
								</div>
							</div>
						)}

						{/* Step 4: Seleção de Horário */}
						{currentStep === 4 && (
							<div className="space-y-6">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Selecione um Horário</h3>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentStep(3)}
									>
										Voltar
									</Button>
								</div>

								<div className="bg-muted/50 p-4 rounded-lg mb-4">
									<div className="flex items-center space-x-4">
										<UserAvatar
											user={selectedDoctor?.user || {}}
											className="h-12 w-12"
										/>
										<div>
											<h3 className="font-semibold">
												{selectedDoctor?.user?.name || "Médico"}
											</h3>
											<p className="text-sm text-muted-foreground">
												{selectedDate &&
													format(selectedDate, "dd 'de' MMMM 'de' yyyy", {
														locale: ptBR,
													})}
											</p>
										</div>
									</div>
								</div>

								{loadingTimeSlots ? (
									<div className="flex justify-center py-8">
										<Loader2 className="h-8 w-8 animate-spin text-primary" />
									</div>
								) : availableTimeSlots.length > 0 ? (
									<div className="grid grid-cols-3 gap-2">
										{availableTimeSlots.map((slot) => (
											<Button
												key={slot.id}
												variant={
													selectedTimeSlot?.id === slot.id
														? "default"
														: "outline"
												}
												onClick={() => {
													handleTimeSlotSelect(slot);
												}}
												className="w-full"
											>
												{format(new Date(slot.startTime), "HH:mm")}
											</Button>
										))}
									</div>
								) : (
									<p className="text-center py-4 text-muted-foreground">
										Nenhum horário disponível nesta data
									</p>
								)}
							</div>
						)}

						{/* Step 5: Detalhes Finais */}
						{currentStep === 5 && (
							<div className="space-y-6">
								<div className="flex items-center justify-between">
									<h3 className="text-lg font-medium">Detalhes da Consulta</h3>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setCurrentStep(4)}
									>
										Voltar
									</Button>
								</div>

								{/* Resumo da consulta */}
								<div className="space-y-4 rounded-lg border-primary bg-muted/50 p-6">
									<div className="grid grid-cols-2 gap-4 text-sm">
										<div>
											<p className="text-muted-foreground">Médico:</p>
											<p className="font-medium">{selectedDoctor?.user.name}</p>
										</div>
										<div>
											<p className="text-muted-foreground">Paciente:</p>
											<p className="font-medium">
												{selectedPatient?.user.name}
											</p>
										</div>
										<div>
											<p className="text-muted-foreground">Data:</p>
											<p className="font-medium">
												{selectedDate && format(selectedDate, "dd/MM/yyyy")}
											</p>
										</div>
										<div>
											<p className="text-muted-foreground">Horário:</p>
											<p className="font-medium">
												{selectedTimeSlot &&
													format(new Date(selectedTimeSlot.startTime), "HH:mm")}
											</p>
										</div>
									</div>
								</div>

								{/* Formulário de detalhes */}
								<div className="space-y-4">
									<div className="p-1">
										<Label>Sintomas ou Observações</Label>
										<Textarea
											placeholder="Descreva os sintomas ou observações importantes"
											value={symptoms}
											onChange={(e) => setSymptoms(e.target.value)}
											className="min-h-[100px] border-none resize-none"
										/>
									</div>
								</div>

								<Button
									className="w-full"
									onClick={handleCreateAppointment}
									disabled={isLoading}
								>
									{isLoading ? (
										<>
											<Loader2 className="mr-2 h-4 w-4 animate-spin" />
											Processando...
										</>
									) : (
										"Confirmar"
									)}
								</Button>
							</div>
						)}
					</div>
				</ScrollArea>
			</SheetContent>
		</Sheet>
	);
}
