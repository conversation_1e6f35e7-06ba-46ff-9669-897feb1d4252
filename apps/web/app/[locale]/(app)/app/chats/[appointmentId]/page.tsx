'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function AppointmentChatPage() {
	const router = useRouter();
	const params = useParams();
	const appointmentId = params.appointmentId as string;

	// Redirect to main chat page with appointment query parameter
	useEffect(() => {
		if (appointmentId) {
			router.replace(`/app/zapchat?appointment=${appointmentId}`);
		}
	}, [appointmentId, router]);

	// Show loading while redirecting
	return (
		<div className='flex h-full items-center justify-center'>
			<div className='animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full'></div>
		</div>
	);
}
