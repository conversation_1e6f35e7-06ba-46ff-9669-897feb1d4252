import { currentUser } from "@saas/auth/lib/current-user";
import { db } from "database";
import { redirect } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { FileText, Download, ArrowLeft, Calendar, User } from "lucide-react";
import Link from "next/link";

interface PrescriptionViewPageProps {
  params: {
    id: string;
  };
}

async function getPrescriptionData(prescriptionId: string, userId: string, userRole: string) {
  const prescription = await db.prescription.findUnique({
    where: { id: prescriptionId },
    include: {
      appointment: {
        include: {
          patient: {
            include: {
              user: true
            }
          },
          doctor: {
            include: {
              user: true,
              specialties: true
            }
          }
        }
      }
    }
  });

  if (!prescription) {
    return null;
  }

  // Verificar se o usuário tem acesso a esta prescrição
  const isDoctor = prescription.appointment.doctor?.userId === userId;
  const isPatient = prescription.appointment.patient.userId === userId;
  const isAdmin = userRole === "ADMIN";
  
  if (!isDoctor && !isPatient && !isAdmin) {
    return null;
  }

  return prescription;
}

export default async function PrescriptionViewPage({ params }: PrescriptionViewPageProps) {
  const { user } = await currentUser();

  if (!user) {
    redirect("/auth/login");
  }

  const prescription = await getPrescriptionData(params.id, user.id, user.role);

  if (!prescription) {
    redirect("/app/prescriptions");
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Ativa</Badge>;
      case 'expired':
        return <Badge variant="secondary">Expirada</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const medications = prescription.content?.medications || [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/app/prescriptions">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prescrição Médica</h1>
          <p className="text-gray-600">Detalhes da prescrição</p>
        </div>
      </div>

      {/* Prescription Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-blue-600" />
              Informações da Prescrição
            </CardTitle>
            {getStatusBadge(prescription.status)}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Paciente:</span>
              <span className="font-medium">{prescription.appointment.patient.user.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Data:</span>
              <span className="font-medium">
                {new Date(prescription.createdAt).toLocaleDateString('pt-BR')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">Médico:</span>
              <span className="font-medium">{prescription.appointment.doctor?.user.name}</span>
            </div>
            {prescription.appointment.doctor?.crm && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">CRM:</span>
                <span className="font-medium">{prescription.appointment.doctor.crm}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Medications */}
      {medications.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Medicamentos Prescritos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {medications.map((medication: any, index: number) => (
                <div key={index} className="p-4 border rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">
                    {medication.name}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Dosagem:</span> {medication.dosage}
                    </div>
                    <div>
                      <span className="font-medium">Frequência:</span> {medication.frequency}
                    </div>
                    <div>
                      <span className="font-medium">Duração:</span> {medication.duration}
                    </div>
                  </div>
                  {medication.instructions && (
                    <div className="mt-2 text-sm text-gray-600">
                      <span className="font-medium">Instruções:</span> {medication.instructions}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* General Instructions */}
      {prescription.content?.instructions && (
        <Card>
          <CardHeader>
            <CardTitle>Instruções Gerais</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">{prescription.content.instructions}</p>
          </CardContent>
        </Card>
      )}

      {/* PDF Download */}
      {prescription.pdfUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Documento PDF</CardTitle>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <a href={prescription.pdfUrl} target="_blank" rel="noopener noreferrer">
                <Download className="w-4 h-4 mr-2" />
                Baixar Prescrição PDF
              </a>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
