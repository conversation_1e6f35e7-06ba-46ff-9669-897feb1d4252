"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@ui/components/card";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { Refresh<PERSON><PERSON>, <PERSON>ert<PERSON>riangle, CheckCircle } from "lucide-react";
import { useState } from "react";

// Script SQL para atualizar o cache de schema
const REFRESH_SCHEMA_SQL = `
NOTIFY pgrst, 'reload schema';
`;

// Função para atualizar o cache de schema
async function refreshSchemaCache() {
  try {
    const response = await fetch('/api/supabase/refresh-schema', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Erro ao atualizar cache de schema');
    }

    return data;
  } catch (error) {
    console.error('Error refreshing schema cache:', error);
    throw error;
  }
}

export default function RefreshSchemaPage() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleUpdateCache = async () => {
    setIsUpdating(true);

    try {
      await refreshSchemaCache();
      setIsSuccess(true);

      // Reset success state after 3 seconds
      setTimeout(() => setIsSuccess(false), 3000);
    } catch (error) {
      console.error("Error refreshing schema cache:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Atualização de Cache de Schema</h1>
        <p className="text-sm text-muted-foreground mt-2">
          Atualize o cache do Supabase para resolver problemas com tabelas ou colunas.
        </p>
      </div>

      <div className="space-y-4">
        <p>
          Esta ação irá forçar o Supabase a atualizar seu cache de schema, resolvendo possíveis
          problemas de colunas ou tabelas não encontradas.
        </p>

        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="font-medium mb-2">Quando você cria ou modifica tabelas no banco de dados, o Supabase mantém um cache de schema que às vezes precisa ser forçado a atualizar.</p>

          <p className="text-sm text-muted-foreground mt-3">Execute esta ação se estiver enfrentando erros como:</p>
          <ul className="list-disc pl-5 mt-2 space-y-1 text-sm text-muted-foreground">
            <li>Could not find column X in table Y</li>
            <li>Could not find table X in schema cache</li>
            <li>PGRST204 errors</li>
          </ul>
        </div>

        <Alert className="border-amber-200 bg-amber-50 text-amber-800">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Atenção</AlertTitle>
          <AlertDescription>
            Esta ação é segura para ser executada a qualquer momento e não afeta os dados existentes.
          </AlertDescription>
        </Alert>

        {isSuccess && (
          <Alert variant="success" className="bg-green-50 text-green-800 border-green-200">
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Sucesso</AlertTitle>
            <AlertDescription>
              O cache de schema foi atualizado com sucesso.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex justify-end">
          <Button
            onClick={handleUpdateCache}
            disabled={isUpdating}
            className="w-full sm:w-auto"
          >
            {isUpdating ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Atualizando...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Atualizar Cache de Schema
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
