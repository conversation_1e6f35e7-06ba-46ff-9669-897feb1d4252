"use client";

import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { useState } from "react";
import { toast } from "sonner";

// Script SQL para criar a função exec_sql
const EXEC_SQL_FUNCTION = `
CREATE OR REPLACE FUNCTION exec_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  EXECUTE sql;
END;
$$;
`;

// Função para configurar a função exec_sql
async function setupExecSqlFunction() {
  try {
    const response = await fetch('/api/supabase/exec-sql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sql: EXEC_SQL_FUNCTION }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Erro ao configurar função RPC');
    }

    return data;
  } catch (error) {
    console.error('Error setting up RPC function:', error);
    throw error;
  }
}

export default function SetupRpcPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSetup = async () => {
    try {
      setIsLoading(true);
      const { success, error } = await setupExecSqlFunction();

      if (!success) {
        throw new Error(error || "Erro ao configurar função RPC");
      }

      toast.success("Função RPC configurada com sucesso!");
    } catch (error) {
      console.error("Error setting up RPC function:", error);
      toast.error(error instanceof Error ? error.message : "Erro ao configurar função RPC");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Configuração de Funções RPC</h1>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Configurar Função exec_sql</CardTitle>
          <CardDescription>
            Esta ação irá criar a função RPC exec_sql necessária para executar scripts SQL a partir do servidor.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Ao clicar no botão abaixo, o sistema irá:
          </p>
          <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1 mb-4">
            <li>Criar a função <code>exec_sql</code> no banco de dados</li>
            <li>Configurar a função com <code>SECURITY DEFINER</code> para garantir que apenas o usuário de serviço possa executá-la</li>
          </ul>
          <p className="text-sm font-medium text-amber-600">
            Atenção: Esta função é necessária para executar o script de criação da tabela de mensagens.
          </p>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSetup} disabled={isLoading}>
            {isLoading ? "Configurando..." : "Configurar Função RPC"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
