"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@ui/components/card";
import { useState } from "react";
import { toast } from "sonner";
import { setupMessagesTable } from "../../../../../../actions/appointments/messages/setup-messages";

export default function SetupMessagesPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSetup = async () => {
    try {
      setIsLoading(true);
      const { success, error } = await setupMessagesTable();

      if (!success) {
        throw new Error(error || "Erro ao configurar tabela de mensagens");
      }

      toast.success("Tabela de mensagens configurada com sucesso!");
    } catch (error) {
      console.error("Error setting up messages table:", error);
      toast.error(error instanceof Error ? error.message : "Erro ao configurar tabela de mensagens");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Configuração do Chat</h1>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>Configurar Tabela de Mensagens</CardTitle>
          <CardDescription>
            Esta ação irá criar a tabela de mensagens e configurar as políticas de segurança necessárias para o funcionamento do chat.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            Ao clicar no botão abaixo, o sistema irá:
          </p>
          <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1 mb-4">
            <li>Criar a tabela <code>messages</code> se ela não existir</li>
            <li>Configurar índices para melhorar a performance</li>
            <li>Configurar políticas de segurança (RLS)</li>
            <li>Criar buckets para armazenamento de arquivos</li>
          </ul>
          <p className="text-sm font-medium text-amber-600">
            Atenção: Esta ação é segura para ser executada múltiplas vezes, pois usa <code>IF NOT EXISTS</code> em todas as operações.
          </p>
        </CardContent>
        <CardFooter>
          <Button onClick={handleSetup} disabled={isLoading}>
            {isLoading ? "Configurando..." : "Configurar Tabela de Mensagens"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
