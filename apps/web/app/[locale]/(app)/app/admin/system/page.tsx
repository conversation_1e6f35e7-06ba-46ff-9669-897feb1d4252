import { currentUser } from "@saas/auth/lib/current-user";
import { UserRoleSchema } from "database";
import { redirect } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
  Settings,
  Database,
  Mail,
  Shield,
  Bell,
  Globe,
  Key,
  Server,
  Users,
  CreditCard
} from "lucide-react";

export default async function SystemSettingsPage() {
  const session = await currentUser();

  if (!session?.user || session.user.role !== UserRoleSchema.Values.ADMIN) {
    redirect("/app/dashboard");
  }

  const systemStatus = {
    database: "Online",
    email: "Configurado",
    payments: "Ativo",
    notifications: "Ativo",
    security: "Ativo"
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Online":
      case "Configurado":
      case "Ativo":
        return <Badge className="bg-green-100 text-green-800">✓ {status}</Badge>;
      case "Offline":
      case "Erro":
        return <Badge className="bg-red-100 text-red-800">✗ {status}</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold flex items-center gap-2">
          <Settings className="h-6 w-6" />
          Configurações do Sistema
        </h1>
        <p className="text-sm text-muted-foreground mt-2">
          Gerencie configurações globais e status dos serviços
        </p>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Status do Sistema
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Banco de Dados</span>
              </div>
              {getStatusBadge(systemStatus.database)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">E-mail</span>
              </div>
              {getStatusBadge(systemStatus.email)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium">Pagamentos</span>
              </div>
              {getStatusBadge(systemStatus.payments)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium">Notificações</span>
              </div>
              {getStatusBadge(systemStatus.notifications)}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium">Segurança</span>
              </div>
              {getStatusBadge(systemStatus.security)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Configurações Gerais
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Nome da plataforma</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">URL base</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Fuso horário</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Segurança
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Autenticação 2FA</span>
              <Button variant="outline" size="sm">Configurar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Política de senhas</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Sessões ativas</span>
              <Button variant="outline" size="sm">Gerenciar</Button>
            </div>
          </CardContent>
        </Card>

        {/* Email Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              E-mail
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">SMTP Server</span>
              <Button variant="outline" size="sm">Configurar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Templates</span>
              <Button variant="outline" size="sm">Gerenciar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Teste de envio</span>
              <Button variant="outline" size="sm">Testar</Button>
            </div>
          </CardContent>
        </Card>

        {/* User Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Usuários
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Registro de usuários</span>
              <Button variant="outline" size="sm">Configurar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Verificação de e-mail</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Política de privacidade</span>
              <Button variant="outline" size="sm">Editar</Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* API Keys */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Chaves de API
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Gerenciamento de chaves de API</p>
            <p className="text-sm">Em desenvolvimento...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
