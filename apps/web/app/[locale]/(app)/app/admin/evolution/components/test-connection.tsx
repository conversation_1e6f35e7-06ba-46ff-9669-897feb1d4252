"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Badge } from "@ui/components/badge";
import { toast } from "sonner";
import { Send, TestTube, CheckCircle, XCircle, Clock } from "lucide-react";

interface TestResult {
  id: string;
  type: "message" | "group" | "connection";
  status: "success" | "error" | "pending";
  message: string;
  timestamp: string;
  details?: string;
}

export function TestConnection() {
  const [testType, setTestType] = useState<"message" | "group" | "connection">("message");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [groupId, setGroupId] = useState("");
  const [testMessage, setTestMessage] = useState("🧪 Esta é uma mensagem de teste da Evolution API");
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const handleTestConnection = async () => {
    setLoading(true);
    try {
      // TODO: Implementar teste real de conexão
      await new Promise(resolve => setTimeout(resolve, 2000));

      const result: TestResult = {
        id: Date.now().toString(),
        type: "connection",
        status: "success",
        message: "Conexão estabelecida com sucesso",
        timestamp: new Date().toISOString(),
        details: "API respondendo em 150ms"
      };

      setTestResults([result, ...testResults]);
      toast.success("Teste de conexão realizado com sucesso!");
    } catch (error) {
      const result: TestResult = {
        id: Date.now().toString(),
        type: "connection",
        status: "error",
        message: "Erro na conexão",
        timestamp: new Date().toISOString(),
        details: "Timeout após 30s"
      };

      setTestResults([result, ...testResults]);
      toast.error("Erro no teste de conexão");
    } finally {
      setLoading(false);
    }
  };

  const handleTestMessage = async () => {
    if (!phoneNumber) {
      toast.error("Digite um número de telefone");
      return;
    }

    setLoading(true);
    try {
      // TODO: Implementar teste real de envio
      await new Promise(resolve => setTimeout(resolve, 3000));

      const result: TestResult = {
        id: Date.now().toString(),
        type: "message",
        status: "success",
        message: "Mensagem enviada com sucesso",
        timestamp: new Date().toISOString(),
        details: `Enviada para ${phoneNumber}`
      };

      setTestResults([result, ...testResults]);
      toast.success("Mensagem de teste enviada!");
    } catch (error) {
      const result: TestResult = {
        id: Date.now().toString(),
        type: "message",
        status: "error",
        message: "Erro ao enviar mensagem",
        timestamp: new Date().toISOString(),
        details: "Número inválido ou API indisponível"
      };

      setTestResults([result, ...testResults]);
      toast.error("Erro ao enviar mensagem de teste");
    } finally {
      setLoading(false);
    }
  };

  const handleTestGroup = async () => {
    if (!groupId) {
      toast.error("Digite um ID de grupo");
      return;
    }

    setLoading(true);
    try {
      // TODO: Implementar teste real de grupo
      await new Promise(resolve => setTimeout(resolve, 2500));

      const result: TestResult = {
        id: Date.now().toString(),
        type: "group",
        status: "success",
        message: "Mensagem enviada para o grupo",
        timestamp: new Date().toISOString(),
        details: `Enviada para grupo ${groupId}`
      };

      setTestResults([result, ...testResults]);
      toast.success("Mensagem enviada para o grupo!");
    } catch (error) {
      const result: TestResult = {
        id: Date.now().toString(),
        type: "group",
        status: "error",
        message: "Erro ao enviar para grupo",
        timestamp: new Date().toISOString(),
        details: "Grupo não encontrado ou sem permissão"
      };

      setTestResults([result, ...testResults]);
      toast.error("Erro ao enviar mensagem para o grupo");
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "error":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-green-100 text-green-800">Sucesso</Badge>;
      case "error":
        return <Badge className="bg-red-100 text-red-800">Erro</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pendente</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Testes de Conexão</h2>
        <p className="text-muted-foreground">
          Teste a conectividade e funcionalidades da Evolution API
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Teste de Conexão */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Teste de Conexão
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Verifica se a API está respondendo e acessível
            </p>
            <Button
              onClick={handleTestConnection}
              disabled={loading}
              className="w-full"
            >
              {loading ? "Testando..." : "Testar Conexão"}
            </Button>
          </CardContent>
        </Card>

        {/* Teste de Mensagem */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Send className="h-5 w-5" />
              Teste de Mensagem
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="testPhone">Número de Telefone</Label>
              <Input
                id="testPhone"
                placeholder="5511999999999"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="testMessage">Mensagem de Teste</Label>
              <Textarea
                id="testMessage"
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                rows={3}
              />
            </div>
            <Button
              onClick={handleTestMessage}
              disabled={loading || !phoneNumber}
              className="w-full"
            >
              {loading ? "Enviando..." : "Enviar Mensagem"}
            </Button>
          </CardContent>
        </Card>

        {/* Teste de Grupo */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Teste de Grupo
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="testGroup">ID do Grupo</Label>
              <Input
                id="testGroup"
                placeholder="<EMAIL>"
                value={groupId}
                onChange={(e) => setGroupId(e.target.value)}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Envia mensagem de teste para um grupo específico
            </p>
            <Button
              onClick={handleTestGroup}
              disabled={loading || !groupId}
              className="w-full"
            >
              {loading ? "Enviando..." : "Enviar para Grupo"}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Resultados dos Testes */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Histórico de Testes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result) => (
                <div key={result.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <p className="font-medium">{result.message}</p>
                      <p className="text-sm text-muted-foreground">
                        {result.details} • {new Date(result.timestamp).toLocaleTimeString('pt-BR')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(result.status)}
                    <Badge variant="outline" className="text-xs">
                      {result.type === "message" ? "Mensagem" :
                       result.type === "group" ? "Grupo" : "Conexão"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Informações de Teste */}
      <Card>
        <CardHeader>
          <CardTitle>Informações de Teste</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Teste de Conexão</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Verifica se a API está respondendo</li>
                <li>• Testa autenticação com API Key</li>
                <li>• Mede tempo de resposta</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Teste de Mensagem</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Envia mensagem para número específico</li>
                <li>• Verifica entrega da mensagem</li>
                <li>• Testa formatação e emojis</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Teste de Grupo</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Envia mensagem para grupo</li>
                <li>• Verifica permissões de acesso</li>
                <li>• Testa notificações em massa</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Dicas</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Use números reais para testes</li>
                <li>• Verifique logs da API</li>
                <li>• Teste em horários de baixo tráfego</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
