'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Switch } from '@ui/components/switch';
import { Separator } from '@ui/components/separator';
import { useToast } from '@ui/hooks/use-toast';
import {
  Settings,
  Key,
  Globe,
  Webhook,
  Save,
  Eye,
  EyeOff,
  TestTube,
  Copy,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

import type { EvolutionConfig } from '@lib/evolution/types';

export function ApiSettings() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [copied, setCopied] = useState('');

  const [config, setConfig] = useState<EvolutionConfig>({
    apiUrl: 'https://your-evolution-server.com',
    apiKey: 'your-api-key-here',
    instanceName: 'zapvida-plantao',
    webhookUrl: 'https://zapvida.com.br/api/webhooks/evolution',
    plantaoGroupId: '<EMAIL>',
  });

  const [webhookEvents, setWebhookEvents] = useState({
    message: true,
    group_update: true,
    connection_update: true,
    status: true,
    presence: false,
    typing: false,
  });

  const [testResults, setTestResults] = useState<{
    connection: 'idle' | 'testing' | 'success' | 'error';
    webhook: 'idle' | 'testing' | 'success' | 'error';
  }>({
    connection: 'idle',
    webhook: 'idle',
  });

  const handleSaveConfig = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // In a real implementation, you would save to your backend/environment
      localStorage.setItem('evolution-config', JSON.stringify(config));
      localStorage.setItem('evolution-webhook-events', JSON.stringify(webhookEvents));

      toast({
        title: 'Configurações salvas!',
        description: 'As configurações da Evolution API foram atualizadas.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao salvar',
        description: 'Não foi possível salvar as configurações.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async () => {
    setTestResults(prev => ({ ...prev, connection: 'testing' }));
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      setTestResults(prev => ({ ...prev, connection: 'success' }));
      toast({
        title: 'Conexão testada com sucesso!',
        description: 'A API está respondendo corretamente.',
      });
    } catch (error) {
      setTestResults(prev => ({ ...prev, connection: 'error' }));
      toast({
        title: 'Erro de conexão',
        description: 'Não foi possível conectar com a API.',
        variant: 'destructive',
      });
    }
  };

  const testWebhook = async () => {
    setTestResults(prev => ({ ...prev, webhook: 'testing' }));
    try {
      // Simulate webhook test
      await new Promise(resolve => setTimeout(resolve, 2000));

      setTestResults(prev => ({ ...prev, webhook: 'success' }));
      toast({
        title: 'Webhook testado com sucesso!',
        description: 'O webhook está configurado corretamente.',
      });
    } catch (error) {
      setTestResults(prev => ({ ...prev, webhook: 'error' }));
      toast({
        title: 'Erro no webhook',
        description: 'Não foi possível verificar o webhook.',
        variant: 'destructive',
      });
    }
  };

  const copyToClipboard = async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(key);
      setTimeout(() => setCopied(''), 2000);
      toast({
        title: 'Copiado!',
        description: 'Texto copiado para a área de transferência.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao copiar',
        description: 'Não foi possível copiar o texto.',
        variant: 'destructive',
      });
    }
  };

  const getTestIcon = (status: 'idle' | 'testing' | 'success' | 'error') => {
    switch (status) {
      case 'testing':
        return <TestTube className="h-4 w-4 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <TestTube className="h-4 w-4" />;
    }
  };

  const generateEnvConfig = () => {
    return `# Evolution API Configuration
EVOLUTION_API_URL=${config.apiUrl}
EVOLUTION_API_KEY=${config.apiKey}
EVOLUTION_INSTANCE=${config.instanceName}
EVOLUTION_WEBHOOK_URL=${config.webhookUrl}
EVOLUTION_GROUP_PLANTAO_ID=${config.plantaoGroupId || ''}

# Webhook Events
EVOLUTION_WEBHOOK_EVENTS=${Object.entries(webhookEvents)
  .filter(([, enabled]) => enabled)
  .map(([event]) => event)
  .join(',')}`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configurações da API
          </CardTitle>
          <CardDescription>
            Configure a conexão com a Evolution API para WhatsApp
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* API Configuration */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="api-url" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                URL da API
              </Label>
              <Input
                id="api-url"
                value={config.apiUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, apiUrl: e.target.value }))}
                placeholder="https://your-evolution-server.com"
              />
              <p className="text-xs text-muted-foreground mt-1">
                URL base do servidor Evolution API
              </p>
            </div>

            <div>
              <Label htmlFor="api-key" className="flex items-center gap-2">
                <Key className="h-4 w-4" />
                API Key
              </Label>
              <div className="flex gap-2">
                <Input
                  id="api-key"
                  type={showApiKey ? 'text' : 'password'}
                  value={config.apiKey}
                  onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                  placeholder="your-api-key-here"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(config.apiKey, 'apiKey')}
                >
                  {copied === 'apiKey' ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Chave de autenticação da Evolution API
              </p>
            </div>

            <div>
              <Label htmlFor="instance-name">Nome da Instância</Label>
              <Input
                id="instance-name"
                value={config.instanceName}
                onChange={(e) => setConfig(prev => ({ ...prev, instanceName: e.target.value }))}
                placeholder="zapvida-plantao"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Nome único para identificar a instância
              </p>
            </div>
          </div>

          <Separator />

          {/* Webhook Configuration */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="webhook-url" className="flex items-center gap-2">
                <Webhook className="h-4 w-4" />
                URL do Webhook
              </Label>
              <Input
                id="webhook-url"
                value={config.webhookUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, webhookUrl: e.target.value }))}
                placeholder="https://zapvida.com.br/api/webhooks/evolution"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Endpoint para receber eventos da Evolution API
              </p>
            </div>

            <div>
              <Label>Eventos do Webhook</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                {Object.entries(webhookEvents).map(([event, enabled]) => (
                  <div key={event} className="flex items-center space-x-2">
                    <Switch
                      id={event}
                      checked={enabled}
                      onCheckedChange={(checked) =>
                        setWebhookEvents(prev => ({ ...prev, [event]: checked }))
                      }
                    />
                    <Label htmlFor={event} className="text-sm">
                      {event.replace('_', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <Separator />

          {/* Plantão Configuration */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="plantao-group">ID do Grupo de Plantão</Label>
              <Input
                id="plantao-group"
                value={config.plantaoGroupId || ''}
                onChange={(e) => setConfig(prev => ({ ...prev, plantaoGroupId: e.target.value }))}
                placeholder="<EMAIL>"
              />
              <p className="text-xs text-muted-foreground mt-1">
                ID do grupo principal para notificações de plantão
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Testing Card */}
      <Card>
        <CardHeader>
          <CardTitle>Teste de Conexão</CardTitle>
          <CardDescription>
            Verifique se as configurações estão funcionando corretamente
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button
              variant="outline"
              onClick={testConnection}
              disabled={testResults.connection === 'testing'}
              className="flex items-center gap-2"
            >
              {getTestIcon(testResults.connection)}
              {testResults.connection === 'testing' ? 'Testando...' : 'Testar Conexão'}
            </Button>

            <Button
              variant="outline"
              onClick={testWebhook}
              disabled={testResults.webhook === 'testing'}
              className="flex items-center gap-2"
            >
              {getTestIcon(testResults.webhook)}
              {testResults.webhook === 'testing' ? 'Testando...' : 'Testar Webhook'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Environment Variables */}
      <Card>
        <CardHeader>
          <CardTitle>Variáveis de Ambiente</CardTitle>
          <CardDescription>
            Configure estas variáveis no seu arquivo .env
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Textarea
              value={generateEnvConfig()}
              readOnly
              rows={10}
              className="font-mono text-sm"
            />
            <Button
              variant="outline"
              size="sm"
              className="absolute top-2 right-2"
              onClick={() => copyToClipboard(generateEnvConfig(), 'env')}
            >
              {copied === 'env' ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveConfig} disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Salvando...' : 'Salvar Configurações'}
        </Button>
      </div>
    </div>
  );
}
