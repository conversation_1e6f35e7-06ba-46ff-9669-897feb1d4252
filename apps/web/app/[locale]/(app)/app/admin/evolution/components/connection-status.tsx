'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Progress } from '@ui/components/progress';
import { useToast } from '@ui/hooks/use-toast';
import {
  RefreshCw,
  Wifi,
  WifiOff,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
  Smartphone,
  Webhook,
  Server
} from 'lucide-react';

interface ConnectionState {
  status: 'open' | 'connecting' | 'close' | 'logout';
  lastCheck: Date;
  uptime: number;
  qrCode?: string;
  instanceName: string;
  webhookStatus: 'active' | 'inactive' | 'error';
  messagesSent: number;
  messagesReceived: number;
}

export function ConnectionStatus() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: 'open',
    lastCheck: new Date(),
    uptime: 99.5,
    instanceName: 'zapvida-plantao',
    webhookStatus: 'active',
    messagesSent: 1247,
    messagesReceived: 856,
  });

  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        checkConnectionStatus();
      }, 30000); // Check every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const checkConnectionStatus = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setConnectionState(prev => ({
        ...prev,
        lastCheck: new Date(),
        uptime: Math.min(99.9, prev.uptime + Math.random() * 0.1),
      }));
    } catch (error) {
      toast({
        title: 'Erro ao verificar status',
        description: 'Não foi possível verificar o status da conexão.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const reconnectInstance = async () => {
    setLoading(true);
    try {
      setConnectionState(prev => ({ ...prev, status: 'connecting' }));
      await new Promise(resolve => setTimeout(resolve, 3000));

      setConnectionState(prev => ({
        ...prev,
        status: 'open',
        lastCheck: new Date(),
        uptime: 100,
      }));

      toast({
        title: 'Reconectado com sucesso!',
        description: 'A instância foi reconectada ao WhatsApp.',
      });
    } catch (error) {
      setConnectionState(prev => ({ ...prev, status: 'close' }));
      toast({
        title: 'Erro na reconexão',
        description: 'Não foi possível reconectar a instância.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const restartInstance = async () => {
    setLoading(true);
    try {
      setConnectionState(prev => ({ ...prev, status: 'connecting' }));
      await new Promise(resolve => setTimeout(resolve, 5000));

      setConnectionState(prev => ({
        ...prev,
        status: 'open',
        lastCheck: new Date(),
        uptime: 100,
        messagesSent: prev.messagesSent + Math.floor(Math.random() * 10),
      }));

      toast({
        title: 'Instância reiniciada!',
        description: 'A instância foi reiniciada com sucesso.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao reiniciar',
        description: 'Não foi possível reiniciar a instância.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = () => {
    switch (connectionState.status) {
      case 'open':
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle className="w-3 h-3 mr-1" />
            Conectado
          </Badge>
        );
      case 'connecting':
        return (
          <Badge variant="secondary">
            <Clock className="w-3 h-3 mr-1 animate-spin" />
            Conectando
          </Badge>
        );
      case 'close':
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            Desconectado
          </Badge>
        );
      case 'logout':
        return (
          <Badge variant="outline">
            <WifiOff className="w-3 h-3 mr-1" />
            Logout
          </Badge>
        );
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  const getWebhookStatusBadge = () => {
    switch (connectionState.webhookStatus) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-500">
            <Webhook className="w-3 h-3 mr-1" />
            Ativo
          </Badge>
        );
      case 'inactive':
        return (
          <Badge variant="secondary">
            <Webhook className="w-3 h-3 mr-1" />
            Inativo
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            Erro
          </Badge>
        );
    }
  };

  const getUptimeColor = () => {
    if (connectionState.uptime >= 99) return 'bg-green-500';
    if (connectionState.uptime >= 95) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Status da Conexão
              </CardTitle>
              <CardDescription>
                Monitore o status da instância Evolution API
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                {autoRefresh ? 'Parar Auto-refresh' : 'Iniciar Auto-refresh'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={checkConnectionStatus}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Verificar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Smartphone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">WhatsApp</span>
              </div>
              {getStatusBadge()}
              <p className="text-xs text-muted-foreground">
                Última verificação: {connectionState.lastCheck.toLocaleTimeString()}
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Webhook className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Webhook</span>
              </div>
              {getWebhookStatusBadge()}
              <p className="text-xs text-muted-foreground">
                Recebendo eventos
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Instância</span>
              </div>
              <Badge variant="outline">{connectionState.instanceName}</Badge>
              <p className="text-xs text-muted-foreground">
                Nome da instância
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Uptime</span>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{connectionState.uptime.toFixed(1)}%</span>
                </div>
                <Progress
                  value={connectionState.uptime}
                  className="h-2"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Estatísticas de Mensagens</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Mensagens Enviadas</span>
                <Badge variant="outline">{connectionState.messagesSent.toLocaleString()}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Mensagens Recebidas</span>
                <Badge variant="outline">{connectionState.messagesReceived.toLocaleString()}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Taxa de Sucesso</span>
                <Badge variant="default" className="bg-green-500">
                  {((connectionState.messagesSent / (connectionState.messagesSent + 50)) * 100).toFixed(1)}%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Mensagens Falhadas</span>
                <Badge variant="destructive">50</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Ações de Manutenção</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={reconnectInstance}
                disabled={loading || connectionState.status === 'open'}
              >
                <Wifi className="h-4 w-4 mr-2" />
                Reconectar WhatsApp
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={restartInstance}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reiniciar Instância
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => {
                  toast({
                    title: 'QR Code',
                    description: 'Funcionalidade de QR Code será implementada.',
                  });
                }}
              >
                <Smartphone className="h-4 w-4 mr-2" />
                Gerar QR Code
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Historical Data */}
      <Card>
        <CardHeader>
          <CardTitle>Histórico de Conexão</CardTitle>
          <CardDescription>
            Últimas 24 horas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { time: '14:30', status: 'connected', message: 'Conexão estabelecida com sucesso' },
              { time: '14:25', status: 'connecting', message: 'Tentando reconectar...' },
              { time: '14:20', status: 'disconnected', message: 'Perda de conexão detectada' },
              { time: '13:45', status: 'connected', message: 'Sistema funcionando normalmente' },
              { time: '12:00', status: 'maintenance', message: 'Manutenção programada concluída' },
            ].map((event, index) => (
              <div key={index} className="flex items-start space-x-3 pb-3 border-b last:border-b-0">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  event.status === 'connected' ? 'bg-green-500' :
                  event.status === 'connecting' ? 'bg-yellow-500' :
                  event.status === 'disconnected' ? 'bg-red-500' : 'bg-blue-500'
                }`}></div>
                <div className="flex-1 space-y-1">
                  <p className="text-sm">{event.message}</p>
                  <p className="text-xs text-muted-foreground">{event.time}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
