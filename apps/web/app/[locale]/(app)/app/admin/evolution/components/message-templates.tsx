'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@ui/components/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@ui/components/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Switch } from '@ui/components/switch';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@ui/components/tabs';
import { useToast } from '@ui/hooks/use-toast';
import {
  MessageSquare,
  Plus,
  Edit,
  Trash2,
  Copy,
  TestTube,
  Eye,
  Save,
  RefreshCw,
  CheckCircle,
  Send
} from 'lucide-react';
import { defaultTemplates, replaceTemplateVariables, extractTemplateVariables } from '@lib/evolution';

import type { MessageTemplate } from '@lib/evolution';

export function MessageTemplates() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<MessageTemplate[]>(defaultTemplates);
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [showTestDialog, setShowTestDialog] = useState(false);

  const [createForm, setCreateForm] = useState({
    name: '',
    content: '',
    category: 'plantao' as MessageTemplate['category'],
  });

  const [editForm, setEditForm] = useState({
    name: '',
    content: '',
    category: 'plantao' as MessageTemplate['category'],
    isActive: true,
  });

  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});
  const [testForm, setTestForm] = useState({
    templateId: '',
    phoneNumber: '',
    variables: {} as Record<string, string>,
  });

  const filteredTemplates = templates.filter(template =>
    selectedCategory === 'all' || template.category === selectedCategory
  );

  const categories = [
    { value: 'all', label: 'Todos' },
    { value: 'plantao', label: 'Plantão' },
    { value: 'paciente', label: 'Paciente' },
    { value: 'medico', label: 'Médico' },
    { value: 'admin', label: 'Administrativo' },
  ];

  const handleCreateTemplate = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newTemplate: MessageTemplate = {
        id: `template-${Date.now()}`,
        name: createForm.name,
        content: createForm.content,
        category: createForm.category,
        variables: extractTemplateVariables(createForm.content),
        isActive: true,
      };

      setTemplates(prev => [...prev, newTemplate]);
      setShowCreateDialog(false);
      setCreateForm({ name: '', content: '', category: 'plantao' });

      toast({
        title: 'Template criado!',
        description: `O template "${newTemplate.name}" foi criado com sucesso.`,
      });
    } catch (error) {
      toast({
        title: 'Erro ao criar template',
        description: 'Não foi possível criar o template. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditTemplate = async () => {
    if (!selectedTemplate) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setTemplates(prev => prev.map(template =>
        template.id === selectedTemplate.id
          ? {
              ...template,
              name: editForm.name,
              content: editForm.content,
              category: editForm.category,
              isActive: editForm.isActive,
              variables: extractTemplateVariables(editForm.content),
            }
          : template
      ));

      setShowEditDialog(false);
      setSelectedTemplate(null);

      toast({
        title: 'Template atualizado!',
        description: 'O template foi atualizado com sucesso.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao atualizar template',
        description: 'Não foi possível atualizar o template. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setTemplates(prev => prev.filter(template => template.id !== templateId));

      toast({
        title: 'Template removido!',
        description: 'O template foi removido com sucesso.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao remover template',
        description: 'Não foi possível remover o template. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleTemplate = async (templateId: string) => {
    setTemplates(prev => prev.map(template =>
      template.id === templateId
        ? { ...template, isActive: !template.isActive }
        : template
    ));
  };

  const handleTestTemplate = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: 'Mensagem de teste enviada!',
        description: `Template testado com sucesso para ${testForm.phoneNumber}.`,
      });

      setShowTestDialog(false);
      setTestForm({ templateId: '', phoneNumber: '', variables: {} });
    } catch (error) {
      toast({
        title: 'Erro no teste',
        description: 'Não foi possível enviar a mensagem de teste.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const openEditDialog = (template: MessageTemplate) => {
    setSelectedTemplate(template);
    setEditForm({
      name: template.name,
      content: template.content,
      category: template.category,
      isActive: template.isActive,
    });
    setShowEditDialog(true);
  };

  const openPreviewDialog = (template: MessageTemplate) => {
    setSelectedTemplate(template);

    // Initialize preview variables with sample data
    const sampleData: Record<string, string> = {
      patientName: 'João Silva',
      doctorName: 'Dr. Maria Santos',
      urgency: 'ALTA',
      time: new Date().toLocaleTimeString(),
      date: new Date().toLocaleDateString(),
      specialty: 'Clínico Geral',
      amount: 'R$ 150,00',
      roomUrl: 'https://zapvida.com.br/room/123',
      systemUrl: 'https://zapvida.com.br/admin',
      phone: '(11) 99999-9999',
      reason: 'Dor no peito',
      currentShift: 'Manhã',
      nextShift: 'Tarde',
      patientsServed: '15',
      pendingCases: '3',
      estimatedTime: '15 minutos',
      prescriptionUrl: 'https://zapvida.com.br/prescription/123',
      supportPhone: '(11) 3333-3333',
      ratingUrl: 'https://zapvida.com.br/rating/123',
      acceptUrl: 'https://zapvida.com.br/accept/123',
      waitingTime: '10 minutos',
      startTime: '02:00',
      endTime: '06:00',
      duration: '4 horas',
      instanceName: 'zapvida-plantao',
      status: 'disconnected',
      lastCheck: new Date().toLocaleTimeString(),
    };

    const templateVariables = template.variables || [];
    const initialVariables: Record<string, string> = {};

    templateVariables.forEach(variable => {
      initialVariables[variable] = sampleData[variable] || `{{${variable}}}`;
    });

    setPreviewVariables(initialVariables);
    setShowPreviewDialog(true);
  };

  const openTestDialog = (template: MessageTemplate) => {
    const initialVariables: Record<string, string> = {};
    template.variables?.forEach(variable => {
      initialVariables[variable] = '';
    });

    setTestForm({
      templateId: template.id,
      phoneNumber: '',
      variables: initialVariables,
    });
    setSelectedTemplate(template);
    setShowTestDialog(true);
  };

  const copyTemplate = async (template: MessageTemplate) => {
    try {
      await navigator.clipboard.writeText(template.content);
      toast({
        title: 'Template copiado!',
        description: 'O conteúdo do template foi copiado para a área de transferência.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao copiar',
        description: 'Não foi possível copiar o template.',
        variant: 'destructive',
      });
    }
  };

  const getCategoryBadge = (category: MessageTemplate['category']) => {
    const colors = {
      plantao: 'bg-red-100 text-red-800',
      paciente: 'bg-blue-100 text-blue-800',
      medico: 'bg-green-100 text-green-800',
      admin: 'bg-purple-100 text-purple-800',
    };

    const labels = {
      plantao: 'Plantão',
      paciente: 'Paciente',
      medico: 'Médico',
      admin: 'Admin',
    };

    return (
      <Badge variant="secondary" className={colors[category]}>
        {labels[category]}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Templates de Mensagem
              </CardTitle>
              <CardDescription>
                Gerencie templates para notificações automáticas do plantão
              </CardDescription>
            </div>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Template
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Criar Novo Template</DialogTitle>
                  <DialogDescription>
                    Crie um template de mensagem para notificações automáticas
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="create-name">Nome do Template</Label>
                    <Input
                      id="create-name"
                      value={createForm.name}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Ex: Novo Paciente no Plantão"
                    />
                  </div>
                  <div>
                    <Label htmlFor="create-category">Categoria</Label>
                    <Select
                      value={createForm.category}
                      onValueChange={(value: MessageTemplate['category']) =>
                        setCreateForm(prev => ({ ...prev, category: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="plantao">Plantão</SelectItem>
                        <SelectItem value="paciente">Paciente</SelectItem>
                        <SelectItem value="medico">Médico</SelectItem>
                        <SelectItem value="admin">Administrativo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="create-content">Conteúdo da Mensagem</Label>
                    <Textarea
                      id="create-content"
                      value={createForm.content}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Digite o conteúdo... Use {{variavel}} para dados dinâmicos"
                      rows={6}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Use {"{{variavel}}"} para inserir dados dinâmicos
                    </p>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancelar
                    </Button>
                    <Button onClick={handleCreateTemplate} disabled={loading || !createForm.name || !createForm.content}>
                      {loading ? 'Criando...' : 'Criar Template'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <Label>Filtrar por categoria:</Label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredTemplates.map((template) => (
              <Card key={template.id} className={`${template.isActive ? '' : 'opacity-60'}`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-sm">{template.name}</CardTitle>
                      <div className="flex items-center gap-2">
                        {getCategoryBadge(template.category)}
                        <Badge variant={template.isActive ? 'default' : 'secondary'}>
                          {template.isActive ? 'Ativo' : 'Inativo'}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      <Switch
                        checked={template.isActive}
                        onCheckedChange={() => handleToggleTemplate(template.id)}
                        size="sm"
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    <div className="text-xs bg-muted p-2 rounded font-mono">
                      {template.content.length > 120
                        ? `${template.content.substring(0, 120)}...`
                        : template.content
                      }
                    </div>

                    {template.variables && template.variables.length > 0 && (
                      <div>
                        <p className="text-xs font-medium mb-1">Variáveis:</p>
                        <div className="flex flex-wrap gap-1">
                          {template.variables.map(variable => (
                            <Badge key={variable} variant="outline" className="text-xs">
                              {"{{"}{variable}{"}}"}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openPreviewDialog(template)}
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(template)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyTemplate(template)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openTestDialog(template)}
                      >
                        <TestTube className="h-3 w-3" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Remover Template</AlertDialogTitle>
                            <AlertDialogDescription>
                              Tem certeza que deseja remover o template "{template.name}"?
                              Esta ação não pode ser desfeita.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteTemplate(template.id)}>
                              Remover
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Editar Template</DialogTitle>
            <DialogDescription>
              Atualize o template de mensagem
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Nome do Template</Label>
              <Input
                id="edit-name"
                value={editForm.name}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="edit-category">Categoria</Label>
              <Select
                value={editForm.category}
                onValueChange={(value: MessageTemplate['category']) =>
                  setEditForm(prev => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="plantao">Plantão</SelectItem>
                  <SelectItem value="paciente">Paciente</SelectItem>
                  <SelectItem value="medico">Médico</SelectItem>
                  <SelectItem value="admin">Administrativo</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-content">Conteúdo da Mensagem</Label>
              <Textarea
                id="edit-content"
                value={editForm.content}
                onChange={(e) => setEditForm(prev => ({ ...prev, content: e.target.value }))}
                rows={6}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-active"
                checked={editForm.isActive}
                onCheckedChange={(checked) => setEditForm(prev => ({ ...prev, isActive: checked }))}
              />
              <Label htmlFor="edit-active">Template ativo</Label>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleEditTemplate} disabled={loading}>
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Preview do Template</DialogTitle>
            <DialogDescription>
              Visualize como a mensagem será enviada
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <Label>Template: {selectedTemplate.name}</Label>
                <div className="mt-2 p-4 bg-muted rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm">
                    {replaceTemplateVariables(selectedTemplate.content, previewVariables)}
                  </pre>
                </div>
              </div>

              {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                <div>
                  <Label>Variáveis de exemplo:</Label>
                  <div className="space-y-2 mt-2">
                    {selectedTemplate.variables.map(variable => (
                      <div key={variable} className="flex items-center gap-2">
                        <Label className="text-xs w-24">{"{{"}{variable}{"}}"}</Label>
                        <Input
                          size="sm"
                          value={previewVariables[variable] || ''}
                          onChange={(e) => setPreviewVariables(prev => ({
                            ...prev,
                            [variable]: e.target.value
                          }))}
                          placeholder={`Valor para ${variable}`}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Test Dialog */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Testar Template</DialogTitle>
            <DialogDescription>
              Envie uma mensagem de teste para um número específico
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="test-phone">Número de Teste</Label>
                <Input
                  id="test-phone"
                  value={testForm.phoneNumber}
                  onChange={(e) => setTestForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                  placeholder="5511999999999"
                />
              </div>

              {selectedTemplate.variables && selectedTemplate.variables.length > 0 && (
                <div>
                  <Label>Preencha as variáveis:</Label>
                  <div className="space-y-2 mt-2">
                    {selectedTemplate.variables.map(variable => (
                      <div key={variable}>
                        <Label className="text-xs">{"{{"}{variable}{"}}"}</Label>
                        <Input
                          value={testForm.variables[variable] || ''}
                          onChange={(e) => setTestForm(prev => ({
                            ...prev,
                            variables: { ...prev.variables, [variable]: e.target.value }
                          }))}
                          placeholder={`Valor para ${variable}`}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <Label>Preview da mensagem:</Label>
                <div className="mt-2 p-4 bg-muted rounded-lg">
                  <pre className="whitespace-pre-wrap text-sm">
                    {replaceTemplateVariables(selectedTemplate.content, testForm.variables)}
                  </pre>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowTestDialog(false)}>
                  Cancelar
                </Button>
                <Button
                  onClick={handleTestTemplate}
                  disabled={loading || !testForm.phoneNumber}
                >
                  <Send className="h-4 w-4 mr-2" />
                  {loading ? 'Enviando...' : 'Enviar Teste'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
