"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { toast } from "sonner";
import { Save, Eye, EyeOff } from "lucide-react";

export function EvolutionSettings() {
  const [showApiKey, setShowApiKey] = useState(false);
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({
    apiUrl: process.env.NEXT_PUBLIC_EVOLUTION_API_URL || "",
    apiKey: "",
    instance: process.env.NEXT_PUBLIC_EVOLUTION_INSTANCE || "zapvida-plantao",
    webhookUrl: process.env.NEXT_PUBLIC_EVOLUTION_WEBHOOK_URL || "",
    groupId: process.env.NEXT_PUBLIC_EVOLUTION_GROUP_PLANTAO_ID || ""
  });

  const handleSave = async () => {
    setLoading(true);
    try {
      // TODO: Implementar salvamento real das configurações
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Configurações salvas com sucesso!");
    } catch (error) {
      toast.error("Erro ao salvar configurações");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Configurações da Evolution API</h2>
        <p className="text-muted-foreground">
          Configure a integração com a Evolution API para WhatsApp
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Configurações da API</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="apiUrl">URL da API</Label>
            <Input
              id="apiUrl"
              placeholder="https://your-evolution-server.com"
              value={settings.apiUrl}
              onChange={(e) => setSettings({ ...settings, apiUrl: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              URL base do servidor Evolution API
            </p>
          </div>

          <div>
            <Label htmlFor="apiKey">API Key</Label>
            <div className="relative">
              <Input
                id="apiKey"
                type={showApiKey ? "text" : "password"}
                placeholder="Sua chave de API"
                value={settings.apiKey}
                onChange={(e) => setSettings({ ...settings, apiKey: e.target.value })}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowApiKey(!showApiKey)}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Chave de autenticação da Evolution API
            </p>
          </div>

          <div>
            <Label htmlFor="instance">Nome da Instância</Label>
            <Input
              id="instance"
              placeholder="zapvida-plantao"
              value={settings.instance}
              onChange={(e) => setSettings({ ...settings, instance: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Nome único da instância do WhatsApp
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Configurações de Webhook</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="webhookUrl">URL do Webhook</Label>
            <Input
              id="webhookUrl"
              placeholder="https://your-domain.com/api/webhooks/evolution"
              value={settings.webhookUrl}
              onChange={(e) => setSettings({ ...settings, webhookUrl: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Endpoint para receber notificações da Evolution API
            </p>
          </div>

          <div>
            <Label htmlFor="groupId">ID do Grupo de Plantão</Label>
            <Input
              id="groupId"
              placeholder="<EMAIL>"
              value={settings.groupId}
              onChange={(e) => setSettings({ ...settings, groupId: e.target.value })}
            />
            <p className="text-xs text-muted-foreground mt-1">
              ID do grupo onde médicos receberão notificações
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Variáveis de Ambiente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="bg-gray-100 p-3 rounded font-mono text-sm">
              <div>EVOLUTION_API_URL={settings.apiUrl || "https://your-server.com"}</div>
              <div>EVOLUTION_API_KEY={settings.apiKey ? "***" : "your-api-key"}</div>
              <div>EVOLUTION_INSTANCE={settings.instance}</div>
              <div>EVOLUTION_WEBHOOK_URL={settings.webhookUrl || "https://your-domain.com/webhook"}</div>
              <div>EVOLUTION_GROUP_PLANTAO_ID={settings.groupId || "group-id"}</div>
            </div>
            <p className="text-xs text-muted-foreground">
              Adicione essas variáveis ao seu arquivo .env
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={loading}>
          <Save className="h-4 w-4 mr-2" />
          {loading ? "Salvando..." : "Salvar Configurações"}
        </Button>
      </div>
    </div>
  );
}
