'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@ui/components/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { useToast } from '@ui/hooks/use-toast';
import {
  TestTube,
  Send,
  Phone,
  Users,
  Activity,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw,
  Trash2
} from 'lucide-react';

import type { TestResult } from '@lib/evolution/types';

export function ConnectionTests() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [testHistory, setTestHistory] = useState<TestResult[]>([
    {
      id: '1',
      type: 'connection',
      status: 'success',
      message: 'Conexão testada com sucesso',
      timestamp: new Date(Date.now() - 1000 * 60 * 5),
      details: { state: 'open', responseTime: '245ms' },
    },
    {
      id: '2',
      type: 'message',
      status: 'success',
      message: 'Mensagem enviada para 5511999999999',
      timestamp: new Date(Date.now() - 1000 * 60 * 15),
      details: { messageId: 'MSG123', deliveryStatus: 'delivered' },
    },
    {
      id: '3',
      type: 'group',
      status: 'error',
      message: 'Falha ao enviar mensagem para grupo',
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      details: { error: 'Group not found' },
    },
  ]);

  const [messageTest, setMessageTest] = useState({
    phoneNumber: '',
    message: 'Teste de conectividade - Evolution API',
  });

  const [groupTest, setGroupTest] = useState({
    groupId: '',
    message: 'Teste de mensagem em grupo - Evolution API',
  });

  const [selectedGroupId, setSelectedGroupId] = useState('');

  // Mock groups for testing
  const availableGroups = [
    { id: '<EMAIL>', name: 'Plantão Clínico - Turno A' },
    { id: '<EMAIL>', name: 'Plantão Clínico - Turno B' },
    { id: '<EMAIL>', name: 'Emergências' },
  ];

  const runConnectionTest = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const result: TestResult = {
        id: Date.now().toString(),
        type: 'connection',
        status: Math.random() > 0.1 ? 'success' : 'error',
        message: Math.random() > 0.1
          ? 'Conexão estabelecida com sucesso'
          : 'Falha na conexão com a API',
        timestamp: new Date(),
        details: {
          state: 'open',
          responseTime: `${Math.floor(Math.random() * 500 + 100)}ms`,
          instanceName: 'zapvida-plantao',
        },
      };

      setTestHistory(prev => [result, ...prev]);

      toast({
        title: result.status === 'success' ? 'Teste bem-sucedido!' : 'Teste falhou',
        description: result.message,
        variant: result.status === 'error' ? 'destructive' : 'default',
      });
    } catch (error) {
      toast({
        title: 'Erro no teste',
        description: 'Não foi possível executar o teste de conexão.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const runMessageTest = async () => {
    if (!messageTest.phoneNumber || !messageTest.message) {
      toast({
        title: 'Campos obrigatórios',
        description: 'Preencha o número e a mensagem.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));

      const result: TestResult = {
        id: Date.now().toString(),
        type: 'message',
        status: Math.random() > 0.2 ? 'success' : 'error',
        message: Math.random() > 0.2
          ? `Mensagem enviada para ${messageTest.phoneNumber}`
          : `Falha ao enviar mensagem para ${messageTest.phoneNumber}`,
        timestamp: new Date(),
        details: {
          phoneNumber: messageTest.phoneNumber,
          messageLength: messageTest.message.length,
          messageId: `MSG${Date.now()}`,
        },
      };

      setTestHistory(prev => [result, ...prev]);

      toast({
        title: result.status === 'success' ? 'Mensagem enviada!' : 'Falha no envio',
        description: result.message,
        variant: result.status === 'error' ? 'destructive' : 'default',
      });
    } catch (error) {
      toast({
        title: 'Erro no teste',
        description: 'Não foi possível enviar a mensagem de teste.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const runGroupTest = async () => {
    if (!groupTest.groupId || !groupTest.message) {
      toast({
        title: 'Campos obrigatórios',
        description: 'Selecione um grupo e digite a mensagem.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));

      const selectedGroup = availableGroups.find(g => g.id === groupTest.groupId);

      const result: TestResult = {
        id: Date.now().toString(),
        type: 'group',
        status: Math.random() > 0.15 ? 'success' : 'error',
        message: Math.random() > 0.15
          ? `Mensagem enviada para o grupo "${selectedGroup?.name}"`
          : `Falha ao enviar mensagem para o grupo "${selectedGroup?.name}"`,
        timestamp: new Date(),
        details: {
          groupId: groupTest.groupId,
          groupName: selectedGroup?.name,
          messageLength: groupTest.message.length,
        },
      };

      setTestHistory(prev => [result, ...prev]);

      toast({
        title: result.status === 'success' ? 'Mensagem enviada ao grupo!' : 'Falha no envio',
        description: result.message,
        variant: result.status === 'error' ? 'destructive' : 'default',
      });
    } catch (error) {
      toast({
        title: 'Erro no teste',
        description: 'Não foi possível enviar a mensagem para o grupo.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const clearHistory = () => {
    setTestHistory([]);
    toast({
      title: 'Histórico limpo',
      description: 'O histórico de testes foi removido.',
    });
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getTypeIcon = (type: TestResult['type']) => {
    switch (type) {
      case 'connection':
        return <Activity className="h-4 w-4" />;
      case 'message':
        return <Phone className="h-4 w-4" />;
      case 'group':
        return <Users className="h-4 w-4" />;
      default:
        return <TestTube className="h-4 w-4" />;
    }
  };

  const getTypeBadge = (type: TestResult['type']) => {
    const colors = {
      connection: 'bg-blue-100 text-blue-800',
      message: 'bg-green-100 text-green-800',
      group: 'bg-purple-100 text-purple-800',
    };

    const labels = {
      connection: 'Conexão',
      message: 'Mensagem',
      group: 'Grupo',
    };

    return (
      <Badge variant="secondary" className={colors[type]}>
        {getTypeIcon(type)}
        <span className="ml-1">{labels[type]}</span>
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Test Controls */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Connection Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Teste de Conexão
            </CardTitle>
            <CardDescription>
              Verifique se a API está respondendo
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={runConnectionTest}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Testar Conexão
            </Button>
          </CardContent>
        </Card>

        {/* Message Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Teste de Mensagem
            </CardTitle>
            <CardDescription>
              Envie uma mensagem de teste
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label htmlFor="phone">Número</Label>
              <Input
                id="phone"
                value={messageTest.phoneNumber}
                onChange={(e) => setMessageTest(prev => ({ ...prev, phoneNumber: e.target.value }))}
                placeholder="5511999999999"
              />
            </div>
            <div>
              <Label htmlFor="message">Mensagem</Label>
              <Textarea
                id="message"
                value={messageTest.message}
                onChange={(e) => setMessageTest(prev => ({ ...prev, message: e.target.value }))}
                rows={3}
              />
            </div>
            <Button
              onClick={runMessageTest}
              disabled={loading || !messageTest.phoneNumber}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Enviar Teste
            </Button>
          </CardContent>
        </Card>

        {/* Group Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Teste de Grupo
            </CardTitle>
            <CardDescription>
              Envie mensagem para um grupo
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <Label htmlFor="group">Grupo</Label>
              <Select
                value={groupTest.groupId}
                onValueChange={(value) => setGroupTest(prev => ({ ...prev, groupId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione um grupo" />
                </SelectTrigger>
                <SelectContent>
                  {availableGroups.map(group => (
                    <SelectItem key={group.id} value={group.id}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="group-message">Mensagem</Label>
              <Textarea
                id="group-message"
                value={groupTest.message}
                onChange={(e) => setGroupTest(prev => ({ ...prev, message: e.target.value }))}
                rows={3}
              />
            </div>
            <Button
              onClick={runGroupTest}
              disabled={loading || !groupTest.groupId}
              className="w-full"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Enviar ao Grupo
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Histórico de Testes</CardTitle>
              <CardDescription>
                Resultados dos últimos testes executados
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={clearHistory}>
              <Trash2 className="h-4 w-4 mr-2" />
              Limpar Histórico
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {testHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <TestTube className="h-8 w-8 mx-auto mb-2" />
              <p>Nenhum teste executado ainda</p>
              <p className="text-sm">Execute um teste acima para ver os resultados aqui</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Mensagem</TableHead>
                  <TableHead>Horário</TableHead>
                  <TableHead>Detalhes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {testHistory.map((test) => (
                  <TableRow key={test.id}>
                    <TableCell>
                      {getTypeBadge(test.type)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(test.status)}
                        <Badge variant={test.status === 'success' ? 'default' : 'destructive'}>
                          {test.status === 'success' ? 'Sucesso' : 'Erro'}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate">
                        {test.message}
                      </div>
                    </TableCell>
                    <TableCell>
                      {test.timestamp.toLocaleTimeString()}
                    </TableCell>
                    <TableCell>
                      {test.details && (
                        <div className="text-xs text-muted-foreground">
                          {Object.entries(test.details).map(([key, value]) => (
                            <div key={key}>
                              <strong>{key}:</strong> {value}
                            </div>
                          ))}
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {testHistory.filter(t => t.status === 'success').length}
                </p>
                <p className="text-xs text-muted-foreground">Testes bem-sucedidos</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {testHistory.filter(t => t.status === 'error').length}
                </p>
                <p className="text-xs text-muted-foreground">Testes com erro</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold">
                  {testHistory.length > 0
                    ? Math.round((testHistory.filter(t => t.status === 'success').length / testHistory.length) * 100)
                    : 0
                  }%
                </p>
                <p className="text-xs text-muted-foreground">Taxa de sucesso</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
