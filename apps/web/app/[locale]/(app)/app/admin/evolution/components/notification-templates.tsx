"use client";

import { useState } from "react";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { Badge } from "@ui/components/badge";
import { toast } from "sonner";
import { Save, MessageSquare, TestTube } from "lucide-react";

interface MessageTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  variables: string[];
  category: "plantao" | "paciente" | "medico";
}

export function NotificationTemplates() {
  const [templates, setTemplates] = useState<MessageTemplate[]>([
    {
      id: "1",
      name: "Novo Paciente no Plantão",
      description: "Notificação enviada para o grupo de médicos quando um novo paciente entra na fila",
      template: "🚨 NOVO PACIENTE NA FILA!\n\n👤 Nome: {{PACIENTE_NOME}}\n⚡ Urgência: {{NIVEL_URGENCIA}}\n⏰ Entrou na fila: {{HORA_ENTRADA}}\n\n🔗 Acesse a fila: {{LINK_FILA}}",
      variables: ["{{PACIENTE_NOME}}", "{{NIVEL_URGENCIA}}", "{{HORA_ENTRADA}}", "{{LINK_FILA}}"],
      category: "plantao"
    },
    {
      id: "2",
      name: "Paciente Aceito",
      description: "Notificação enviada para o paciente quando um médico aceita o atendimento",
      template: "✅ ATENDIMENTO ACEITO!\n\n👨‍⚕️ Dr. {{MEDICO_NOME}} aceitou seu atendimento\n🔗 Acesse a sala: {{LINK_SALA}}\n\n⏰ Você tem 5 minutos para entrar na sala",
      variables: ["{{MEDICO_NOME}}", "{{LINK_SALA}}"],
      category: "paciente"
    },
    {
      id: "3",
      name: "Lembrete de Consulta",
      description: "Lembrete enviado para o paciente antes da consulta",
      template: "⏰ Lembrete de Consulta\n\nOlá {{PACIENTE_NOME}}!\nSua consulta está marcada para {{DATA_HORA}}\n\n🔗 Acesse: {{LINK_CONSULTA}}",
      variables: ["{{PACIENTE_NOME}}", "{{DATA_HORA}}", "{{LINK_CONSULTA}}"],
      category: "paciente"
    }
  ]);

  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);
  const [saving, setSaving] = useState(false);

  const handleSaveTemplate = async (template: MessageTemplate) => {
    setSaving(true);
    try {
      // TODO: Implementar salvamento real
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (editingTemplate) {
        setTemplates(templates.map(t => t.id === template.id ? template : t));
        setEditingTemplate(null);
      } else {
        setTemplates([...templates, { ...template, id: Date.now().toString() }]);
      }

      toast.success("Template salvo com sucesso!");
    } catch (error) {
      toast.error("Erro ao salvar template");
    } finally {
      setSaving(false);
    }
  };

  const handleTestTemplate = async (template: MessageTemplate) => {
    try {
      // TODO: Implementar teste real de envio
      toast.success("Mensagem de teste enviada!");
    } catch (error) {
      toast.error("Erro ao enviar mensagem de teste");
    }
  };

  const getCategoryInfo = (category: string) => {
    switch (category) {
      case "plantao":
        return { label: "Plantão", variant: "default" as const, color: "bg-blue-100 text-blue-800" };
      case "paciente":
        return { label: "Paciente", variant: "secondary" as const, color: "bg-green-100 text-green-800" };
      case "medico":
        return { label: "Médico", variant: "outline" as const, color: "bg-purple-100 text-purple-800" };
      default:
        return { label: "Outro", variant: "secondary" as const, color: "bg-gray-100 text-gray-800" };
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Templates de Notificação</h2>
        <p className="text-muted-foreground">
          Configure mensagens padrão para diferentes situações do plantão
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {templates.map((template) => (
          <Card key={template.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {template.description}
                  </p>
                </div>
                <Badge className={getCategoryInfo(template.category).color}>
                  {getCategoryInfo(template.category).label}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Template da Mensagem</Label>
                <div className="mt-2 p-3 bg-gray-50 rounded border font-mono text-sm whitespace-pre-wrap">
                  {template.template}
                </div>
              </div>

              <div>
                <Label>Variáveis Disponíveis</Label>
                <div className="mt-2 flex flex-wrap gap-2">
                  {template.variables.map((variable) => (
                    <Badge key={variable} variant="outline" className="text-xs">
                      {variable}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditingTemplate(template)}
                  className="flex-1"
                >
                  <MessageSquare className="h-4 w-4 mr-1" />
                  Editar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestTemplate(template)}
                >
                  <TestTube className="h-4 w-4 mr-1" />
                  Testar
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modal de Edição */}
      {editingTemplate && (
        <Card>
          <CardHeader>
            <CardTitle>Editar Template: {editingTemplate.name}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="templateName">Nome do Template</Label>
              <Input
                id="templateName"
                value={editingTemplate.name}
                onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="templateDescription">Descrição</Label>
              <Input
                id="templateDescription"
                value={editingTemplate.description}
                onChange={(e) => setEditingTemplate({ ...editingTemplate, description: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="templateContent">Conteúdo da Mensagem</Label>
              <Textarea
                id="templateContent"
                value={editingTemplate.template}
                onChange={(e) => setEditingTemplate({ ...editingTemplate, template: e.target.value })}
                rows={6}
                placeholder="Digite o template da mensagem..."
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use {{VARIABLE}} para inserir variáveis dinâmicas
              </p>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setEditingTemplate(null)}>
                Cancelar
              </Button>
              <Button onClick={() => handleSaveTemplate(editingTemplate)} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? "Salvando..." : "Salvar"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
