'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@ui/components/tabs';
import { Badge } from '@ui/components/badge';
import { Button } from '@ui/components/button';
import {
  MessageSquare,
  Users,
  Settings,
  Activity,
  TestTube,
  Smartphone,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

import { GroupManagement } from './components/group-management';
import { ConnectionStatus } from './components/connection-status';
import { ApiSettings } from './components/api-settings';
import { MessageTemplates } from './components/message-templates';
import { ConnectionTests } from './components/connection-tests';

export default function EvolutionAdminPage() {
  // Mock data for dashboard metrics
  const metrics = {
    totalMessages: 1250,
    messagesDelivered: 1200,
    messagesFailed: 50,
    totalGroups: 5,
    activeParticipants: 45,
    connectionUptime: 99.5,
    lastConnectionCheck: new Date(),
  };

  const connectionStatus = 'open'; // 'open' | 'connecting' | 'close' | 'logout'

  const getConnectionStatusBadge = () => {
    switch (connectionStatus) {
      case 'open':
        return <Badge variant="default" className="bg-green-500"><CheckCircle className="w-3 h-3 mr-1" />Conectado</Badge>;
      case 'connecting':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Conectando</Badge>;
      case 'close':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Desconectado</Badge>;
      case 'logout':
        return <Badge variant="outline"><AlertCircle className="w-3 h-3 mr-1" />Logout</Badge>;
      default:
        return <Badge variant="secondary">Desconhecido</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Evolution API</h1>
          <p className="text-muted-foreground">
            Gerencie a integração WhatsApp para notificações de plantão médico
          </p>
        </div>
        <div className="flex items-center gap-2">
          {getConnectionStatusBadge()}
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="groups" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Grupos
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Configurações
          </TabsTrigger>
          <TabsTrigger value="connection" className="flex items-center gap-2">
            <Smartphone className="w-4 h-4" />
            Status
          </TabsTrigger>
          <TabsTrigger value="tests" className="flex items-center gap-2">
            <TestTube className="w-4 h-4" />
            Testes
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total de Mensagens</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalMessages.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  +12% em relação ao mês anterior
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Taxa de Entrega</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {((metrics.messagesDelivered / metrics.totalMessages) * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {metrics.messagesDelivered} de {metrics.totalMessages} entregues
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Grupos Ativos</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.totalGroups}</div>
                <p className="text-xs text-muted-foreground">
                  {metrics.activeParticipants} participantes ativos
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Uptime</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metrics.connectionUptime}%</div>
                <p className="text-xs text-muted-foreground">
                  Último check: {metrics.lastConnectionCheck.toLocaleTimeString()}
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Atividade Recente</CardTitle>
                <CardDescription>
                  Últimas mensagens e eventos do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-green-500 mt-2"></div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm">Novo paciente no plantão - João Silva</p>
                      <p className="text-xs text-muted-foreground">Há 2 minutos</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm">Mensagem enviada para grupo Plantão A</p>
                      <p className="text-xs text-muted-foreground">Há 5 minutos</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2"></div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm">Dr. Maria entrou no grupo Plantão B</p>
                      <p className="text-xs text-muted-foreground">Há 10 minutos</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Status do Sistema</CardTitle>
                <CardDescription>
                  Monitoramento da instância Evolution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Conexão WhatsApp</span>
                    {getConnectionStatusBadge()}
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Webhook</span>
                    <Badge variant="default" className="bg-green-500">Ativo</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Instância</span>
                    <Badge variant="default">zapvida-plantao</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Último heartbeat</span>
                    <span className="text-sm text-muted-foreground">
                      {new Date().toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="groups">
          <GroupManagement />
        </TabsContent>

        <TabsContent value="templates">
          <MessageTemplates />
        </TabsContent>

        <TabsContent value="settings">
          <ApiSettings />
        </TabsContent>

        <TabsContent value="connection">
          <ConnectionStatus />
        </TabsContent>

        <TabsContent value="tests">
          <ConnectionTests />
        </TabsContent>
      </Tabs>
    </div>
  );
}
