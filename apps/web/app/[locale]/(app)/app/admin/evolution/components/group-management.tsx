'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Textarea } from '@ui/components/textarea';
import { Badge } from '@ui/components/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@ui/components/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@ui/components/alert-dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@ui/components/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { useToast } from '@ui/hooks/use-toast';
import {
  Plus,
  Users,
  Edit,
  Trash2,
  UserPlus,
  UserMinus,
  Crown,
  MessageSquare,
  RefreshCw
} from 'lucide-react';

import type { EvolutionGroup, EvolutionParticipant } from '@lib/evolution/types';

export function GroupManagement() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [groups, setGroups] = useState<EvolutionGroup[]>([
    {
      id: '<EMAIL>',
      subject: 'Plantão Clínico - Turno A',
      description: 'Grupo para médicos de plantão - Comunicação de emergências e novos pacientes',
      participants: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      participantsCount: 3,
      admins: ['<EMAIL>'],
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-20'),
    },
    {
      id: '<EMAIL>',
      subject: 'Plantão Clínico - Turno B',
      description: 'Grupo para médicos de plantão noturno',
      participants: ['<EMAIL>', '<EMAIL>'],
      participantsCount: 2,
      admins: ['<EMAIL>'],
      createdAt: new Date('2024-01-16'),
      updatedAt: new Date('2024-01-18'),
    },
  ]);

  const [selectedGroup, setSelectedGroup] = useState<EvolutionGroup | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showMembersDialog, setShowMembersDialog] = useState(false);
  const [showMessageDialog, setShowMessageDialog] = useState(false);

  // Form states
  const [createForm, setCreateForm] = useState({
    subject: '',
    description: '',
    participants: '',
  });

  const [editForm, setEditForm] = useState({
    subject: '',
    description: '',
  });

  const [memberForm, setMemberForm] = useState({
    phoneNumber: '',
    action: 'add' as 'add' | 'remove' | 'promote' | 'demote',
  });

  const [messageForm, setMessageForm] = useState({
    message: '',
  });

  const handleCreateGroup = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newGroup: EvolutionGroup = {
        id: `120363025${Math.floor(Math.random() * 1000)}@g.us`,
        subject: createForm.subject,
        description: createForm.description,
        participants: createForm.participants.split(',').map(p => p.trim() + '@s.whatsapp.net'),
        participantsCount: createForm.participants.split(',').length,
        admins: [], // First participant becomes admin
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setGroups(prev => [...prev, newGroup]);
      setShowCreateDialog(false);
      setCreateForm({ subject: '', description: '', participants: '' });

      toast({
        title: 'Grupo criado com sucesso!',
        description: `O grupo "${newGroup.subject}" foi criado.`,
      });
    } catch (error) {
      toast({
        title: 'Erro ao criar grupo',
        description: 'Não foi possível criar o grupo. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditGroup = async () => {
    if (!selectedGroup) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setGroups(prev => prev.map(group =>
        group.id === selectedGroup.id
          ? { ...group, subject: editForm.subject, description: editForm.description, updatedAt: new Date() }
          : group
      ));

      setShowEditDialog(false);
      setSelectedGroup(null);

      toast({
        title: 'Grupo atualizado!',
        description: 'As informações do grupo foram atualizadas.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao atualizar grupo',
        description: 'Não foi possível atualizar o grupo. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManageMember = async () => {
    if (!selectedGroup) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const phoneWithJid = memberForm.phoneNumber + '@s.whatsapp.net';

      setGroups(prev => prev.map(group => {
        if (group.id !== selectedGroup.id) return group;

        let updatedParticipants = [...group.participants];
        let updatedAdmins = [...group.admins];

        switch (memberForm.action) {
          case 'add':
            if (!updatedParticipants.includes(phoneWithJid)) {
              updatedParticipants.push(phoneWithJid);
            }
            break;
          case 'remove':
            updatedParticipants = updatedParticipants.filter(p => p !== phoneWithJid);
            updatedAdmins = updatedAdmins.filter(a => a !== phoneWithJid);
            break;
          case 'promote':
            if (updatedParticipants.includes(phoneWithJid) && !updatedAdmins.includes(phoneWithJid)) {
              updatedAdmins.push(phoneWithJid);
            }
            break;
          case 'demote':
            updatedAdmins = updatedAdmins.filter(a => a !== phoneWithJid);
            break;
        }

        return {
          ...group,
          participants: updatedParticipants,
          participantsCount: updatedParticipants.length,
          admins: updatedAdmins,
          updatedAt: new Date(),
        };
      }));

      setMemberForm({ phoneNumber: '', action: 'add' });

      const actionLabels = {
        add: 'adicionado ao',
        remove: 'removido do',
        promote: 'promovido a admin no',
        demote: 'rebaixado no',
      };

      toast({
        title: 'Membro atualizado!',
        description: `Membro ${actionLabels[memberForm.action]} grupo.`,
      });
    } catch (error) {
      toast({
        title: 'Erro ao gerenciar membro',
        description: 'Não foi possível atualizar o membro. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!selectedGroup) return;

    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setShowMessageDialog(false);
      setMessageForm({ message: '' });

      toast({
        title: 'Mensagem enviada!',
        description: `Mensagem enviada para o grupo "${selectedGroup.subject}".`,
      });
    } catch (error) {
      toast({
        title: 'Erro ao enviar mensagem',
        description: 'Não foi possível enviar a mensagem. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteGroup = async (groupId: string) => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setGroups(prev => prev.filter(group => group.id !== groupId));

      toast({
        title: 'Grupo removido!',
        description: 'O grupo foi removido com sucesso.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao remover grupo',
        description: 'Não foi possível remover o grupo. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshGroups = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: 'Grupos atualizados!',
        description: 'A lista de grupos foi atualizada.',
      });
    } catch (error) {
      toast({
        title: 'Erro ao atualizar',
        description: 'Não foi possível atualizar a lista. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const openEditDialog = (group: EvolutionGroup) => {
    setSelectedGroup(group);
    setEditForm({
      subject: group.subject,
      description: group.description || '',
    });
    setShowEditDialog(true);
  };

  const openMembersDialog = (group: EvolutionGroup) => {
    setSelectedGroup(group);
    setShowMembersDialog(true);
  };

  const openMessageDialog = (group: EvolutionGroup) => {
    setSelectedGroup(group);
    setShowMessageDialog(true);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Gerenciamento de Grupos
              </CardTitle>
              <CardDescription>
                Gerencie grupos do WhatsApp para comunicação do plantão médico
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefreshGroups}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
              <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Criar Grupo
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Criar Novo Grupo</DialogTitle>
                    <DialogDescription>
                      Crie um novo grupo para comunicação do plantão médico
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="subject">Nome do Grupo</Label>
                      <Input
                        id="subject"
                        value={createForm.subject}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="Ex: Plantão Clínico - Turno A"
                      />
                    </div>
                    <div>
                      <Label htmlFor="description">Descrição</Label>
                      <Textarea
                        id="description"
                        value={createForm.description}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Descrição do grupo..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="participants">Participantes (separados por vírgula)</Label>
                      <Input
                        id="participants"
                        value={createForm.participants}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, participants: e.target.value }))}
                        placeholder="5511999999999, 5531988888888"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                        Cancelar
                      </Button>
                      <Button onClick={handleCreateGroup} disabled={loading || !createForm.subject}>
                        {loading ? 'Criando...' : 'Criar Grupo'}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nome do Grupo</TableHead>
                <TableHead>Participantes</TableHead>
                <TableHead>Admins</TableHead>
                <TableHead>Criado em</TableHead>
                <TableHead>Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groups.map((group) => (
                <TableRow key={group.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{group.subject}</div>
                      {group.description && (
                        <div className="text-sm text-muted-foreground">{group.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {group.participantsCount} membros
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {group.admins.length} admins
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {group.createdAt?.toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(group)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openMembersDialog(group)}
                      >
                        <Users className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openMessageDialog(group)}
                      >
                        <MessageSquare className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Remover Grupo</AlertDialogTitle>
                            <AlertDialogDescription>
                              Tem certeza que deseja remover o grupo "{group.subject}"?
                              Esta ação não pode ser desfeita.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleDeleteGroup(group.id)}>
                              Remover
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Group Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Grupo</DialogTitle>
            <DialogDescription>
              Atualize as informações do grupo
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-subject">Nome do Grupo</Label>
              <Input
                id="edit-subject"
                value={editForm.subject}
                onChange={(e) => setEditForm(prev => ({ ...prev, subject: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Descrição</Label>
              <Textarea
                id="edit-description"
                value={editForm.description}
                onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleEditGroup} disabled={loading}>
                {loading ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Members Management Dialog */}
      <Dialog open={showMembersDialog} onOpenChange={setShowMembersDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Gerenciar Membros</DialogTitle>
            <DialogDescription>
              Adicione, remova ou gerencie permissões dos membros
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="phone">Número do Telefone</Label>
              <Input
                id="phone"
                value={memberForm.phoneNumber}
                onChange={(e) => setMemberForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                placeholder="5511999999999"
              />
            </div>
            <div>
              <Label htmlFor="action">Ação</Label>
              <Select
                value={memberForm.action}
                onValueChange={(value: 'add' | 'remove' | 'promote' | 'demote') =>
                  setMemberForm(prev => ({ ...prev, action: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">
                    <div className="flex items-center gap-2">
                      <UserPlus className="h-4 w-4" />
                      Adicionar ao grupo
                    </div>
                  </SelectItem>
                  <SelectItem value="remove">
                    <div className="flex items-center gap-2">
                      <UserMinus className="h-4 w-4" />
                      Remover do grupo
                    </div>
                  </SelectItem>
                  <SelectItem value="promote">
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4" />
                      Promover a admin
                    </div>
                  </SelectItem>
                  <SelectItem value="demote">
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4" />
                      Remover admin
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowMembersDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleManageMember} disabled={loading || !memberForm.phoneNumber}>
                {loading ? 'Processando...' : 'Executar'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Send Message Dialog */}
      <Dialog open={showMessageDialog} onOpenChange={setShowMessageDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enviar Mensagem</DialogTitle>
            <DialogDescription>
              Envie uma mensagem para o grupo "{selectedGroup?.subject}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="message">Mensagem</Label>
              <Textarea
                id="message"
                value={messageForm.message}
                onChange={(e) => setMessageForm(prev => ({ ...prev, message: e.target.value }))}
                placeholder="Digite sua mensagem..."
                rows={4}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowMessageDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleSendMessage} disabled={loading || !messageForm.message}>
                {loading ? 'Enviando...' : 'Enviar'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
