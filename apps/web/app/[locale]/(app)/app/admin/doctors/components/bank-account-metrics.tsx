'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Banknote, CheckCircle, Clock, XCircle, AlertTriangle } from "lucide-react";

interface BankAccountMetricsProps {
  withBankAccount: number;
  withoutBankAccount: number;
  pendingVerification: number;
  approved: number;
  rejected: number;
}

export function BankAccountMetrics({
  withBankAccount,
  withoutBankAccount,
  pendingVerification,
  approved,
  rejected
}: BankAccountMetricsProps) {
  const total = withBankAccount + withoutBankAccount;
  const configuredPercentage = total > 0 ? (withBankAccount / total) * 100 : 0;
  const approvedPercentage = withBankAccount > 0 ? (approved / withBankAccount) * 100 : 0;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Com Conta</p>
                <p className="text-2xl font-bold">{withBankAccount}</p>
                <p className="text-xs text-muted-foreground">
                  {configuredPercentage.toFixed(1)}% dos médicos
                </p>
              </div>
              <Banknote className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sem Conta</p>
                <p className="text-2xl font-bold">{withoutBankAccount}</p>
                <p className="text-xs text-muted-foreground">
                  {(100 - configuredPercentage).toFixed(1)}% dos médicos
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Aprovadas</p>
                <p className="text-2xl font-bold">{approved}</p>
                <p className="text-xs text-muted-foreground">
                  {approvedPercentage.toFixed(1)}% das contas
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pendentes</p>
                <p className="text-2xl font-bold">{pendingVerification}</p>
                <p className="text-xs text-muted-foreground">
                  aguardando verificação
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Status das Contas Bancárias</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Aprovadas</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{approved}</span>
                <Badge variant="secondary" className="text-xs">
                  {approvedPercentage.toFixed(1)}%
                </Badge>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-green-500"
                style={{ width: `${approvedPercentage}%` }}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium">Verificação Pendente</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{pendingVerification}</span>
                <Badge variant="secondary" className="text-xs">
                  {withBankAccount > 0 ? ((pendingVerification / withBankAccount) * 100).toFixed(1) : 0}%
                </Badge>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-yellow-500"
                style={{ width: `${withBankAccount > 0 ? (pendingVerification / withBankAccount) * 100 : 0}%` }}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium">Rejeitadas</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">{rejected}</span>
                <Badge variant="secondary" className="text-xs">
                  {withBankAccount > 0 ? ((rejected / withBankAccount) * 100).toFixed(1) : 0}%
                </Badge>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-red-500"
                style={{ width: `${withBankAccount > 0 ? (rejected / withBankAccount) * 100 : 0}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
