'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Stethoscope } from "lucide-react";

interface SpecialtyData {
  specialty: string;
  count: number;
}

interface DoctorSpecialtyChartProps {
  data: SpecialtyData[];
}

export function DoctorSpecialtyChart({ data }: DoctorSpecialtyChartProps) {
  const total = data.reduce((sum, item) => sum + item.count, 0);
  const topSpecialties = data
    .sort((a, b) => b.count - a.count)
    .slice(0, 8);

  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-orange-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-teal-500',
    'bg-red-500'
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Distribuição por Especialidade</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {topSpecialties.map((item, index) => {
            const percentage = total > 0 ? (item.count / total) * 100 : 0;
            const color = colors[index % colors.length];

            return (
              <div key={item.specialty} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Stethoscope className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium truncate max-w-[200px]">
                      {item.specialty}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{item.count}</span>
                    <Badge variant="secondary" className="text-xs">
                      {percentage.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${color}`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}

          {data.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              Nenhum dado disponível
            </div>
          )}

          {data.length > 8 && (
            <div className="text-center text-sm text-muted-foreground pt-2">
              +{data.length - 8} outras especialidades
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
