'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@ui/components/dialog";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@ui/components/tabs";
import {
  User,
  Mail,
  Phone,
  FileText,
  Calendar,
  Star,
  Banknote,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  Activity,
  CreditCard,
  MapPin,
  Building
} from "lucide-react";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    image?: string;
    emailVerified: boolean;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
  }>;
}

interface DoctorDetailsModalProps {
  doctor: DoctorData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DoctorDetailsModal({
  doctor,
  open,
  onOpenChange
}: DoctorDetailsModalProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: 'Pendente',
          icon: Clock,
          className: 'bg-yellow-100 text-yellow-800'
        };
      case 'APPROVED':
        return {
          label: 'Aprovado',
          icon: CheckCircle,
          className: 'bg-green-100 text-green-800'
        };
      case 'REJECTED':
        return {
          label: 'Rejeitado',
          icon: XCircle,
          className: 'bg-red-100 text-red-800'
        };
      default:
        return {
          label: status,
          icon: AlertTriangle,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const getOnlineStatusConfig = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return {
          label: 'Online',
          className: 'bg-green-100 text-green-800'
        };
      case 'OFFLINE':
        return {
          label: 'Offline',
          className: 'bg-gray-100 text-gray-800'
        };
      case 'BUSY':
        return {
          label: 'Ocupado',
          className: 'bg-orange-100 text-orange-800'
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const calculateTotalRevenue = () => {
    return doctor.transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + t.amount, 0);
  };

  const calculateCompletedAppointments = () => {
    return doctor.appointments.filter(a => a.status === 'COMPLETED').length;
  };

  const getAppointmentStatusCounts = () => {
    const counts = doctor.appointments.reduce((acc, appointment) => {
      acc[appointment.status] = (acc[appointment.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return counts;
  };

  const getTransactionStatusCounts = () => {
    const counts = doctor.transactions.reduce((acc, transaction) => {
      acc[transaction.status] = (acc[transaction.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return counts;
  };

  const statusConfig = getStatusConfig(doctor.documentStatus);
  const onlineStatusConfig = getOnlineStatusConfig(doctor.onlineStatus);
  const StatusIcon = statusConfig.icon;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Detalhes do Médico</span>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="appointments">Consultas</TabsTrigger>
            <TabsTrigger value="transactions">Transações</TabsTrigger>
            <TabsTrigger value="evaluations">Avaliações</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Informações Pessoais */}
            <div className="bg-muted/50 p-6 rounded-lg">
              <div className="flex items-start space-x-6">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={doctor.user.image} />
                  <AvatarFallback className="text-xl">
                    {doctor.user.name?.charAt(0) || 'M'}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-4">
                  <div>
                    <h3 className="text-2xl font-bold">{doctor.user.name}</h3>
                    <div className="flex items-center space-x-4 mt-2">
                      <Badge className={statusConfig.className}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {statusConfig.label}
                      </Badge>
                      <Badge variant="outline" className={onlineStatusConfig.className}>
                        {onlineStatusConfig.label}
                      </Badge>
                      {doctor.user.emailVerified && (
                        <Badge variant="outline" className="text-xs">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Email Verificado
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span>{doctor.user.email}</span>
                    </div>
                    {doctor.user.phone && (
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{doctor.user.phone}</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>CRM: {doctor.crm}/{doctor.crmState}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>Cadastrado em {formatDate(doctor.createdAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Especialidades */}
            <div>
              <h4 className="font-semibold mb-3">Especialidades</h4>
              <div className="flex flex-wrap gap-2">
                {doctor.specialties.map((specialty) => (
                  <Badge key={specialty.id} variant="secondary" className="text-sm">
                    {specialty.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Configurações de Consulta */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <h4 className="font-medium mb-2">Preço da Consulta</h4>
                <p className="text-3xl font-bold text-green-600">
                  {formatCurrency(doctor.consultationPrice)}
                </p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <h4 className="font-medium mb-2">Duração</h4>
                <p className="text-3xl font-bold">{doctor.consultationDuration} min</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <h4 className="font-medium mb-2">Avaliação</h4>
                <div className="flex items-center justify-center space-x-1">
                  <Star className="h-5 w-5 fill-current text-yellow-500" />
                  <p className="text-3xl font-bold">
                    {doctor.rating ? doctor.rating.toFixed(1) : 'N/A'}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">
                  {doctor.totalRatings} avaliações
                </p>
              </div>
            </div>

            {/* Biografia */}
            {doctor.biography && (
              <div>
                <h4 className="font-semibold mb-3">Biografia</h4>
                <p className="text-muted-foreground bg-muted/50 p-4 rounded-lg">
                  {doctor.biography}
                </p>
              </div>
            )}

            {/* Configurações de Plantão */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <h4 className="font-semibold mb-3">Configurações de Plantão</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Disponível para Plantão</p>
                  <p className="font-medium">
                    {doctor.isAvailableForOnDuty ? 'Sim' : 'Não'}
                  </p>
                </div>
                {doctor.onDutyPriceMultiplier && (
                  <div>
                    <p className="text-sm text-muted-foreground">Multiplicador de Preço</p>
                    <p className="font-medium">{doctor.onDutyPriceMultiplier}x</p>
                  </div>
                )}
                <div>
                  <p className="text-sm text-muted-foreground">Máx. Consultas Simultâneas</p>
                  <p className="font-medium">{doctor.maxConcurrentOnDuty}</p>
                </div>
              </div>
            </div>

            {/* Conta Bancária */}
            <div>
              <h4 className="font-semibold mb-3">Conta Bancária</h4>
              {doctor.bankAccount ? (
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Banknote className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-800">Conta Configurada</span>
                  </div>
                  <div className="text-sm text-green-700">
                    <p>Status: {doctor.bankAccount.status || 'N/A'}</p>
                    {doctor.bankAccount.bankName && (
                      <p>Banco: {doctor.bankAccount.bankName}</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium text-yellow-800">Conta não configurada</span>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="appointments" className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              {Object.entries(getAppointmentStatusCounts()).map(([status, count]) => (
                <div key={status} className="bg-muted/50 p-4 rounded-lg text-center">
                  <p className="text-2xl font-bold">{count}</p>
                  <p className="text-sm text-muted-foreground capitalize">{status}</p>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Últimas Consultas</h4>
              {doctor.appointments.slice(0, 10).map((appointment) => (
                <div key={appointment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Consulta #{appointment.id.slice(-8)}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDateTime(appointment.scheduledAt)}
                    </p>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {appointment.status}
                  </Badge>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="transactions" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(calculateTotalRevenue())}
                </p>
                <p className="text-sm text-muted-foreground">Receita Total</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <p className="text-2xl font-bold">{doctor.transactions.length}</p>
                <p className="text-sm text-muted-foreground">Total de Transações</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <p className="text-2xl font-bold">
                  {formatCurrency(calculateTotalRevenue() / Math.max(doctor.transactions.length, 1))}
                </p>
                <p className="text-sm text-muted-foreground">Ticket Médio</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Últimas Transações</h4>
              {doctor.transactions.slice(0, 10).map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Transação #{transaction.id.slice(-8)}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDateTime(transaction.createdAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(transaction.amount)}</p>
                    <Badge variant="outline" className="capitalize">
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="evaluations" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <div className="flex items-center justify-center space-x-1">
                  <Star className="h-5 w-5 fill-current text-yellow-500" />
                  <p className="text-2xl font-bold">
                    {doctor.rating ? doctor.rating.toFixed(1) : 'N/A'}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">Avaliação Média</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <p className="text-2xl font-bold">{doctor.totalRatings}</p>
                <p className="text-sm text-muted-foreground">Total de Avaliações</p>
              </div>
              <div className="bg-muted/50 p-4 rounded-lg text-center">
                <p className="text-2xl font-bold">
                  {doctor.evaluations.filter(e => e.comment).length}
                </p>
                <p className="text-sm text-muted-foreground">Comentários</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold">Últimas Avaliações</h4>
              {doctor.evaluations.slice(0, 10).map((evaluation) => (
                <div key={evaluation.id} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < evaluation.rating ? 'fill-current text-yellow-500' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="font-medium">{evaluation.rating}/5</span>
                  </div>
                  {evaluation.comment && (
                    <p className="text-sm text-muted-foreground">{evaluation.comment}</p>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
