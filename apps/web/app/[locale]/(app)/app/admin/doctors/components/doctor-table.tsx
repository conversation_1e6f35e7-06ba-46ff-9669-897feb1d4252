'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@ui/components/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@ui/components/dropdown-menu";
import {
  Eye,
  Check,
  X,
  MoreHorizontal,
  Banknote,
  Star,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Mail,
  Phone
} from "lucide-react";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    image?: string;
    emailVerified: boolean;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
  }>;
}

interface DoctorTableProps {
  doctors: DoctorData[];
  onViewDetails: (doctor: DoctorData) => void;
  onApprove: (doctor: DoctorData) => void;
  onManageBankAccount: (doctor: DoctorData) => void;
}

export function DoctorTable({
  doctors,
  onViewDetails,
  onApprove,
  onManageBankAccount
}: DoctorTableProps) {
  const [sortField, setSortField] = useState<keyof DoctorData>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING':
        return {
          label: 'Pendente',
          icon: Clock,
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800'
        };
      case 'APPROVED':
        return {
          label: 'Aprovado',
          icon: CheckCircle,
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800'
        };
      case 'REJECTED':
        return {
          label: 'Rejeitado',
          icon: XCircle,
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800'
        };
      default:
        return {
          label: status,
          icon: AlertTriangle,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const getOnlineStatusConfig = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return {
          label: 'Online',
          className: 'bg-green-100 text-green-800'
        };
      case 'OFFLINE':
        return {
          label: 'Offline',
          className: 'bg-gray-100 text-gray-800'
        };
      case 'BUSY':
        return {
          label: 'Ocupado',
          className: 'bg-orange-100 text-orange-800'
        };
      default:
        return {
          label: status,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const calculateTotalRevenue = (transactions: any[]) => {
    return transactions
      .filter(t => t.status === 'PAID')
      .reduce((sum, t) => sum + t.amount, 0);
  };

  const calculateCompletedAppointments = (appointments: any[]) => {
    return appointments.filter(a => a.status === 'COMPLETED').length;
  };

  const sortedDoctors = [...doctors].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    if (sortField === 'user') {
      aValue = a.user.name;
      bValue = b.user.name;
    } else if (sortField === 'transactions') {
      aValue = calculateTotalRevenue(a.transactions);
      bValue = calculateTotalRevenue(b.transactions);
    } else if (sortField === 'appointments') {
      aValue = calculateCompletedAppointments(a.appointments);
      bValue = calculateCompletedAppointments(b.appointments);
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  const handleSort = (field: keyof DoctorData) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lista de Médicos ({doctors.length})</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Médico</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('documentStatus')}
                >
                  Status
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('specialties')}
                >
                  Especialidades
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('transactions')}
                >
                  Receita
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('appointments')}
                >
                  Consultas
                </TableHead>
                <TableHead>Conta Bancária</TableHead>
                <TableHead className="w-[100px]">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedDoctors.map((doctor) => {
                const statusConfig = getStatusConfig(doctor.documentStatus);
                const onlineStatusConfig = getOnlineStatusConfig(doctor.onlineStatus);
                const totalRevenue = calculateTotalRevenue(doctor.transactions);
                const completedAppointments = calculateCompletedAppointments(doctor.appointments);
                const StatusIcon = statusConfig.icon;

                return (
                  <TableRow key={doctor.id} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={doctor.user.image} />
                          <AvatarFallback>
                            {doctor.user.name?.charAt(0) || 'M'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center space-x-2">
                            <p className="font-medium truncate">{doctor.user.name}</p>
                            {doctor.user.emailVerified && (
                              <Badge variant="outline" className="text-xs">
                                <Check className="h-3 w-3 mr-1" />
                                Verificado
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span className="flex items-center space-x-1">
                              <Mail className="h-3 w-3" />
                              <span className="truncate max-w-[150px]">{doctor.user.email}</span>
                            </span>
                            {doctor.user.phone && (
                              <span className="flex items-center space-x-1">
                                <Phone className="h-3 w-3" />
                                <span>{doctor.user.phone}</span>
                              </span>
                            )}
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <span>CRM: {doctor.crm}/{doctor.crmState}</span>
                            {doctor.rating && (
                              <span className="flex items-center space-x-1">
                                <Star className="h-3 w-3 fill-current" />
                                <span>{doctor.rating.toFixed(1)} ({doctor.totalRatings})</span>
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <Badge className={statusConfig.className}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig.label}
                        </Badge>
                        <Badge variant="outline" className={onlineStatusConfig.className}>
                          {onlineStatusConfig.label}
                        </Badge>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        {doctor.specialties.slice(0, 2).map((specialty) => (
                          <Badge key={specialty.id} variant="secondary" className="text-xs">
                            {specialty.name}
                          </Badge>
                        ))}
                        {doctor.specialties.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{doctor.specialties.length - 2} mais
                          </Badge>
                        )}
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{formatCurrency(totalRevenue)}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatCurrency(doctor.consultationPrice)} por consulta
                        </p>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">{completedAppointments}</p>
                        <p className="text-xs text-muted-foreground">
                          {doctor.appointments.length} total
                        </p>
                      </div>
                    </TableCell>

                    <TableCell>
                      {doctor.bankAccount ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <Banknote className="h-3 w-3 mr-1" />
                          Configurada
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-muted-foreground">
                          Não configurada
                        </Badge>
                      )}
                    </TableCell>

                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onViewDetails(doctor)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Ver Detalhes
                          </DropdownMenuItem>

                          {doctor.documentStatus === 'PENDING' && (
                            <>
                              <DropdownMenuItem onClick={() => onApprove(doctor)}>
                                <Check className="h-4 w-4 mr-2" />
                                Aprovar/Rejeitar
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                            </>
                          )}

                          <DropdownMenuItem onClick={() => onManageBankAccount(doctor)}>
                            <Banknote className="h-4 w-4 mr-2" />
                            Gerenciar Conta Bancária
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {doctors.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              Nenhum médico encontrado
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
