'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@ui/components/dialog";
import { Button } from "@ui/components/button";
import { Textarea } from "@ui/components/textarea";
import { Label } from "@ui/components/label";
import { Badge } from "@ui/components/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  Check,
  X,
  AlertTriangle,
  User,
  Mail,
  Phone,
  FileText,
  Calendar,
  Star,
  Banknote
} from "lucide-react";
import { toast } from "sonner";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    image?: string;
    emailVerified: boolean;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
  }>;
}

interface DoctorApprovalModalProps {
  doctor: DoctorData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApprove: (doctorId: string, approved: boolean, reason?: string) => void;
}

export function DoctorApprovalModal({
  doctor,
  open,
  onOpenChange,
  onApprove
}: DoctorApprovalModalProps) {
  const [approved, setApproved] = useState<boolean | null>(null);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (approved === null) {
      toast.error('Selecione uma ação (aprovar ou rejeitar)');
      return;
    }

    if (!approved && !reason.trim()) {
      toast.error('Motivo da rejeição é obrigatório');
      return;
    }

    setLoading(true);
    try {
      await onApprove(doctor.id, approved, reason || undefined);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setApproved(null);
    setReason('');
    onOpenChange(false);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            <span>Aprovação de Médico</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações do Médico */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <div className="flex items-start space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={doctor.user.image} />
                <AvatarFallback className="text-lg">
                  {doctor.user.name?.charAt(0) || 'M'}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 space-y-2">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-semibold">{doctor.user.name}</h3>
                  {doctor.user.emailVerified && (
                    <Badge variant="outline" className="text-xs">
                      <Check className="h-3 w-3 mr-1" />
                      Email Verificado
                    </Badge>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4" />
                    <span>{doctor.user.email}</span>
                  </div>
                  {doctor.user.phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4" />
                      <span>{doctor.user.phone}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>CRM: {doctor.crm}/{doctor.crmState}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Cadastrado em {formatDate(doctor.createdAt)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Especialidades */}
          <div>
            <h4 className="font-medium mb-2">Especialidades</h4>
            <div className="flex flex-wrap gap-2">
              {doctor.specialties.map((specialty) => (
                <Badge key={specialty.id} variant="secondary">
                  {specialty.name}
                </Badge>
              ))}
            </div>
          </div>

          {/* Configurações de Consulta */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Preço da Consulta</h4>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(doctor.consultationPrice)}
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Duração da Consulta</h4>
              <p className="text-lg">{doctor.consultationDuration} minutos</p>
            </div>
          </div>

          {/* Biografia */}
          {doctor.biography && (
            <div>
              <h4 className="font-medium mb-2">Biografia</h4>
              <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                {doctor.biography}
              </p>
            </div>
          )}

          {/* Estatísticas */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">{doctor.appointments.length}</p>
              <p className="text-xs text-muted-foreground">Consultas</p>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">
                {doctor.rating ? doctor.rating.toFixed(1) : 'N/A'}
              </p>
              <p className="text-xs text-muted-foreground">Avaliação</p>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">{doctor.totalRatings}</p>
              <p className="text-xs text-muted-foreground">Avaliações</p>
            </div>
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <p className="text-2xl font-bold">
                {doctor.bankAccount ? 'Sim' : 'Não'}
              </p>
              <p className="text-xs text-muted-foreground">Conta Bancária</p>
            </div>
          </div>

          {/* Ação de Aprovação */}
          <div className="space-y-4">
            <h4 className="font-medium">Ação</h4>
            <div className="flex space-x-4">
              <Button
                variant={approved === true ? "default" : "outline"}
                onClick={() => setApproved(true)}
                className="flex-1"
              >
                <Check className="h-4 w-4 mr-2" />
                Aprovar Médico
              </Button>
              <Button
                variant={approved === false ? "destructive" : "outline"}
                onClick={() => setApproved(false)}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Rejeitar Médico
              </Button>
            </div>
          </div>

          {/* Motivo da Rejeição */}
          {approved === false && (
            <div className="space-y-2">
              <Label htmlFor="reason">Motivo da Rejeição *</Label>
              <Textarea
                id="reason"
                placeholder="Descreva o motivo da rejeição..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                rows={3}
              />
            </div>
          )}

          {/* Botões de Ação */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading || approved === null}
              className={approved === false ? "bg-red-600 hover:bg-red-700" : ""}
            >
              {loading ? 'Processando...' : approved === true ? 'Aprovar' : 'Rejeitar'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
