'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@ui/components/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@ui/components/dialog";
import {
  Banknote,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Search,
  Filter,
  Eye,
  Check,
  X,
  User,
  Mail,
  Phone,
  FileText,
  Calendar,
  Star,
  CreditCard,
  Building,
  MapPin
} from "lucide-react";
import { toast } from "sonner";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    image?: string;
    emailVerified: boolean;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
  }>;
}

interface BankAccountManagementProps {
  doctors: DoctorData[];
  onAction: (doctorId: string, action: string, data?: any) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function BankAccountManagement({
  doctors,
  onAction,
  open = false,
  onOpenChange
}: BankAccountManagementProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedDoctor, setSelectedDoctor] = useState<DoctorData | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [actionModalOpen, setActionModalOpen] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [actionData, setActionData] = useState<any>({});

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getBankAccountStatusConfig = (status: string) => {
    switch (status) {
      case 'PENDING_VERIFICATION':
        return {
          label: 'Verificação Pendente',
          icon: Clock,
          className: 'bg-yellow-100 text-yellow-800'
        };
      case 'APPROVED':
        return {
          label: 'Aprovada',
          icon: CheckCircle,
          className: 'bg-green-100 text-green-800'
        };
      case 'REJECTED':
        return {
          label: 'Rejeitada',
          icon: XCircle,
          className: 'bg-red-100 text-red-800'
        };
      default:
        return {
          label: 'Não Configurada',
          icon: AlertTriangle,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch =
      doctor.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.crm?.includes(searchTerm);

    const matchesStatus = statusFilter === 'all' ||
      (statusFilter === 'with' && doctor.bankAccount) ||
      (statusFilter === 'without' && !doctor.bankAccount) ||
      (doctor.bankAccount?.status === statusFilter);

    return matchesSearch && matchesStatus;
  });

  const handleAction = async (doctor: DoctorData, action: string) => {
    setSelectedDoctor(doctor);
    setSelectedAction(action);
    setActionData({});
    setActionModalOpen(true);
  };

  const executeAction = async () => {
    if (!selectedDoctor) return;

    try {
      await onAction(selectedDoctor.id, selectedAction, actionData);
      setActionModalOpen(false);
      setSelectedDoctor(null);
      setSelectedAction('');
      setActionData({});
    } catch (error) {
      toast.error('Erro ao executar ação');
    }
  };

  const renderBankAccountDetails = (bankAccount: any) => {
    if (!bankAccount) {
      return (
        <div className="text-center text-muted-foreground py-4">
          <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
          <p>Conta bancária não configurada</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">Banco</label>
            <p className="font-medium">{bankAccount.bankName || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Tipo de Conta</label>
            <p className="font-medium">{bankAccount.accountType || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Agência</label>
            <p className="font-medium">{bankAccount.agency || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Conta</label>
            <p className="font-medium">{bankAccount.account || 'N/A'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">CPF/CNPJ</label>
            <p className="font-medium">
              {bankAccount.document ?
                bankAccount.document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4') :
                'N/A'
              }
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">Status</label>
            <div className="mt-1">
              {(() => {
                const config = getBankAccountStatusConfig(bankAccount.status);
                const Icon = config.icon;
                return (
                  <Badge className={config.className}>
                    <Icon className="h-3 w-3 mr-1" />
                    {config.label}
                  </Badge>
                );
              })()}
            </div>
          </div>
        </div>

        {bankAccount.createdAt && (
          <div>
            <label className="text-sm font-medium text-muted-foreground">Data de Criação</label>
            <p className="font-medium">{formatDate(bankAccount.createdAt)}</p>
          </div>
        )}

        {bankAccount.rejectionReason && (
          <div>
            <label className="text-sm font-medium text-muted-foreground">Motivo da Rejeição</label>
            <p className="text-sm text-red-600 bg-red-50 p-2 rounded">
              {bankAccount.rejectionReason}
            </p>
          </div>
        )}
      </div>
    );
  };

  const renderActionModal = () => {
    if (!selectedDoctor) return null;

    return (
      <Dialog open={actionModalOpen} onOpenChange={setActionModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {selectedAction === 'approve' && 'Aprovar Conta Bancária'}
              {selectedAction === 'reject' && 'Rejeitar Conta Bancária'}
              {selectedAction === 'reset' && 'Resetar Conta Bancária'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex items-center space-x-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={selectedDoctor.user.image} />
                  <AvatarFallback>
                    {selectedDoctor.user.name?.charAt(0) || 'M'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{selectedDoctor.user.name}</p>
                  <p className="text-sm text-muted-foreground">
                    CRM: {selectedDoctor.crm}/{selectedDoctor.crmState}
                  </p>
                </div>
              </div>
            </div>

            {selectedAction === 'reject' && (
              <div>
                <label className="text-sm font-medium">Motivo da Rejeição *</label>
                <textarea
                  className="w-full mt-1 p-2 border rounded-md"
                  rows={3}
                  placeholder="Descreva o motivo da rejeição..."
                  value={actionData.reason || ''}
                  onChange={(e) => setActionData({ ...actionData, reason: e.target.value })}
                />
              </div>
            )}

            {selectedAction === 'reset' && (
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium text-yellow-800">Atenção</span>
                </div>
                <p className="text-sm text-yellow-700 mt-2">
                  Esta ação irá remover a conta bancária do médico. Ele precisará configurar novamente.
                </p>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setActionModalOpen(false)}>
                Cancelar
              </Button>
              <Button
                onClick={executeAction}
                className={
                  selectedAction === 'reject' ? 'bg-red-600 hover:bg-red-700' :
                  selectedAction === 'reset' ? 'bg-yellow-600 hover:bg-yellow-700' : ''
                }
              >
                {selectedAction === 'approve' && 'Aprovar'}
                {selectedAction === 'reject' && 'Rejeitar'}
                {selectedAction === 'reset' && 'Resetar'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  if (open && onOpenChange) {
    // Modal mode for single doctor
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Gestão de Conta Bancária</DialogTitle>
          </DialogHeader>

          {selectedDoctor && (
            <div className="space-y-6">
              {renderBankAccountDetails(selectedDoctor.bankAccount)}

              {selectedDoctor.bankAccount && (
                <div className="flex space-x-2">
                  {selectedDoctor.bankAccount.status === 'PENDING_VERIFICATION' && (
                    <>
                      <Button
                        onClick={() => handleAction(selectedDoctor, 'approve')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Aprovar
                      </Button>
                      <Button
                        onClick={() => handleAction(selectedDoctor, 'reject')}
                        variant="destructive"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Rejeitar
                      </Button>
                    </>
                  )}
                  <Button
                    onClick={() => handleAction(selectedDoctor, 'reset')}
                    variant="outline"
                    className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
                  >
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Resetar
                  </Button>
                </div>
              )}
            </div>
          )}

          {renderActionModal()}
        </DialogContent>
      </Dialog>
    );
  }

  // Full page mode
  return (
    <div className="space-y-6">
      {/* Filtros */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome, email ou CRM..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Status da Conta" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as Contas</SelectItem>
                <SelectItem value="with">Com Conta</SelectItem>
                <SelectItem value="without">Sem Conta</SelectItem>
                <SelectItem value="PENDING_VERIFICATION">Verificação Pendente</SelectItem>
                <SelectItem value="APPROVED">Aprovadas</SelectItem>
                <SelectItem value="REJECTED">Rejeitadas</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Contas Bancárias */}
      <Card>
        <CardHeader>
          <CardTitle>Contas Bancárias dos Médicos ({filteredDoctors.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Médico</TableHead>
                  <TableHead>Status da Conta</TableHead>
                  <TableHead>Banco</TableHead>
                  <TableHead>Agência/Conta</TableHead>
                  <TableHead>Data de Configuração</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDoctors.map((doctor) => {
                  const statusConfig = getBankAccountStatusConfig(doctor.bankAccount?.status);
                  const StatusIcon = statusConfig.icon;

                  return (
                    <TableRow key={doctor.id} className="hover:bg-muted/50">
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={doctor.user.image} />
                            <AvatarFallback>
                              {doctor.user.name?.charAt(0) || 'M'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{doctor.user.name}</p>
                            <p className="text-sm text-muted-foreground">
                              CRM: {doctor.crm}/{doctor.crmState}
                            </p>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <Badge className={statusConfig.className}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig.label}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        {doctor.bankAccount?.bankName || 'N/A'}
                      </TableCell>

                      <TableCell>
                        {doctor.bankAccount ? (
                          <div className="text-sm">
                            <p>{doctor.bankAccount.agency || 'N/A'}</p>
                            <p className="text-muted-foreground">
                              {doctor.bankAccount.account || 'N/A'}
                            </p>
                          </div>
                        ) : (
                          'N/A'
                        )}
                      </TableCell>

                      <TableCell>
                        {doctor.bankAccount?.createdAt ?
                          formatDate(doctor.bankAccount.createdAt) :
                          'N/A'
                        }
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedDoctor(doctor);
                              setDetailsModalOpen(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          {doctor.bankAccount && (
                            <>
                              {doctor.bankAccount.status === 'PENDING_VERIFICATION' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleAction(doctor, 'approve')}
                                    className="text-green-600 hover:text-green-700"
                                  >
                                    <Check className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleAction(doctor, 'reject')}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleAction(doctor, 'reset')}
                                className="text-yellow-600 hover:text-yellow-700"
                              >
                                <AlertTriangle className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>

            {filteredDoctors.length === 0 && (
              <div className="text-center text-muted-foreground py-8">
                Nenhum médico encontrado
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modal de Detalhes */}
      <Dialog open={detailsModalOpen} onOpenChange={setDetailsModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalhes da Conta Bancária</DialogTitle>
          </DialogHeader>

          {selectedDoctor && (
            <div className="space-y-6">
              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={selectedDoctor.user.image} />
                    <AvatarFallback>
                      {selectedDoctor.user.name?.charAt(0) || 'M'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{selectedDoctor.user.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      CRM: {selectedDoctor.crm}/{selectedDoctor.crmState}
                    </p>
                  </div>
                </div>
              </div>

              {renderBankAccountDetails(selectedDoctor.bankAccount)}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {renderActionModal()}
    </div>
  );
}
