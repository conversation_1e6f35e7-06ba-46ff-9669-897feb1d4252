'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { TrendingUp, Award, Star } from "lucide-react";

interface TopPerformer {
  id: string;
  name: string;
  revenue: number;
  appointments: number;
  rating: number;
}

interface DoctorRevenueChartProps {
  data: TopPerformer[];
}

export function DoctorRevenueChart({ data }: DoctorRevenueChartProps) {
  const topPerformers = data
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 10);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Award className="h-4 w-4 text-yellow-500" />;
      case 1:
        return <Award className="h-4 w-4 text-gray-400" />;
      case 2:
        return <Award className="h-4 w-4 text-amber-600" />;
      default:
        return <span className="text-sm font-medium text-muted-foreground">{index + 1}</span>;
    }
  };

  const getRankBadge = (index: number) => {
    switch (index) {
      case 0:
        return <Badge className="bg-yellow-100 text-yellow-800">🥇 1º</Badge>;
      case 1:
        return <Badge className="bg-gray-100 text-gray-800">🥈 2º</Badge>;
      case 2:
        return <Badge className="bg-amber-100 text-amber-800">🥉 3º</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5" />
          <span>Top 10 Médicos por Receita</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {topPerformers.map((doctor, index) => (
            <div key={doctor.id} className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors">
              <div className="flex items-center space-x-4">
                <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                  {getRankIcon(index)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium truncate">{doctor.name}</p>
                    {getRankBadge(index)}
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="flex items-center space-x-1">
                      <Star className="h-3 w-3 fill-current" />
                      <span>{doctor.rating.toFixed(1)}</span>
                    </span>
                    <span>{doctor.appointments} consultas</span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <p className="font-semibold text-lg">{formatCurrency(doctor.revenue)}</p>
                <p className="text-sm text-muted-foreground">
                  {formatCurrency(doctor.revenue / Math.max(doctor.appointments, 1))} por consulta
                </p>
              </div>
            </div>
          ))}

          {data.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              Nenhum dado disponível
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
