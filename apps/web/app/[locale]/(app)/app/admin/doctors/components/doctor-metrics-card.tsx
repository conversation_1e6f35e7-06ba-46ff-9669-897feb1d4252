'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { LucideIcon, TrendingUp, TrendingDown } from "lucide-react";
import { cn } from "@ui/lib";

interface DoctorMetricsCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: string;
  trendDirection?: 'up' | 'down' | 'neutral';
  description?: string;
  variant?: 'default' | 'success' | 'warning' | 'danger';
}

export function DoctorMetricsCard({
  title,
  value,
  icon: Icon,
  trend,
  trendDirection = 'neutral',
  description,
  variant = 'default'
}: DoctorMetricsCardProps) {
  const variantStyles = {
    default: 'border-border',
    success: 'border-green-200 bg-green-50/50',
    warning: 'border-yellow-200 bg-yellow-50/50',
    danger: 'border-red-200 bg-red-50/50'
  };

  const iconStyles = {
    default: 'text-muted-foreground',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600'
  };

  const trendStyles = {
    up: 'text-green-600 bg-green-100',
    down: 'text-red-600 bg-red-100',
    neutral: 'text-gray-600 bg-gray-100'
  };

  return (
    <Card className={cn("transition-all hover:shadow-md", variantStyles[variant])}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={cn("h-4 w-4", iconStyles[variant])} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <div className="flex items-center space-x-2 mt-2">
            <Badge
              variant="secondary"
              className={cn("text-xs", trendStyles[trendDirection])}
            >
              {trendDirection === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
              {trendDirection === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
              {trend}
            </Badge>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
