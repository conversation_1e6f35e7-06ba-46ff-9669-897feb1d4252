'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import {
  Stethoscope,
  Users,
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  TrendingUp,
  Search,
  Filter,
  Download,
  Eye,
  Check,
  X,
  AlertTriangle,
  Banknote,
  CreditCard,
  Calendar,
  Star,
  Activity,
  UserCheck,
  UserX,
  Settings,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@ui/components/dropdown-menu";
import { toast } from "sonner";
import { Doctor<PERSON>etricsCard } from "./components/doctor-metrics-card";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./components/doctor-status-chart";
import { DoctorSpecialtyChart } from "./components/doctor-specialty-chart";
import { DoctorRevenueChart } from "./components/doctor-revenue-chart";
import { DoctorTable } from "./components/doctor-table";
import { BankAccountManagement } from "./components/bank-account-management";
import { DoctorApprovalModal } from "./components/doctor-approval-modal";
import { DoctorDetailsModal } from "./components/doctor-details-modal";

interface DoctorData {
  id: string;
  userId: string;
  crm: string;
  crmState: string;
  biography?: string;
  consultationPrice: number;
  consultationDuration: number;
  documentStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  onlineStatus: 'ONLINE' | 'OFFLINE' | 'BUSY';
  rating?: number;
  totalRatings: number;
  isAvailableForOnDuty: boolean;
  onDutyPriceMultiplier?: number;
  maxConcurrentOnDuty: number;
  bankAccount?: any;
  asaasId?: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    image?: string;
    emailVerified: boolean;
  };
  specialties: Array<{
    id: string;
    name: string;
  }>;
  appointments: Array<{
    id: string;
    status: string;
    scheduledAt: string;
  }>;
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    createdAt: string;
  }>;
  evaluations: Array<{
    id: string;
    rating: number;
    comment?: string;
  }>;
}

interface DoctorMetrics {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  online: number;
  offline: number;
  withBankAccount: number;
  withoutBankAccount: number;
  totalRevenue: number;
  averageRating: number;
  totalAppointments: number;
  completedAppointments: number;
  bySpecialty: Array<{ specialty: string; count: number }>;
  byStatus: Array<{ status: string; count: number }>;
  topPerformers: Array<{
    id: string;
    name: string;
    revenue: number;
    appointments: number;
    rating: number;
  }>;
  recentActivity: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
    doctorName: string;
  }>;
}

export function AdminDoctorsClient() {
  const [doctors, setDoctors] = useState<DoctorData[]>([]);
  const [metrics, setMetrics] = useState<DoctorMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [specialtyFilter, setSpecialtyFilter] = useState<string>('all');
  const [bankAccountFilter, setBankAccountFilter] = useState<string>('all');
  const [selectedDoctor, setSelectedDoctor] = useState<DoctorData | null>(null);
  const [approvalModalOpen, setApprovalModalOpen] = useState(false);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [bankAccountModalOpen, setBankAccountModalOpen] = useState(false);

  const fetchDoctorsData = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        specialty: specialtyFilter,
        bankAccount: bankAccountFilter,
      });

      const response = await fetch(`/api/admin/doctors?${params}`);

      if (!response.ok) {
        throw new Error('Erro ao carregar dados dos médicos');
      }

      const result = await response.json();
      setDoctors(result.doctors);
      setMetrics(result.metrics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
      console.error('Error fetching doctors data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDoctorsData();
  }, [searchTerm, statusFilter, specialtyFilter, bankAccountFilter]);

  const handleApproveDoctor = async (doctorId: string, approved: boolean, reason?: string) => {
    try {
      const response = await fetch(`/api/admin/doctors/${doctorId}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ approved, reason }),
      });

      if (!response.ok) {
        throw new Error('Erro ao processar aprovação');
      }

      toast.success(approved ? 'Médico aprovado com sucesso' : 'Médico rejeitado com sucesso');
      setApprovalModalOpen(false);
      fetchDoctorsData();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao processar aprovação');
    }
  };

  const handleBankAccountAction = async (doctorId: string, action: string, data?: any) => {
    try {
      const response = await fetch(`/api/admin/doctors/${doctorId}/bank-account`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ...data }),
      });

      if (!response.ok) {
        throw new Error('Erro ao processar ação da conta bancária');
      }

      toast.success('Ação processada com sucesso');
      setBankAccountModalOpen(false);
      fetchDoctorsData();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Erro ao processar ação');
    }
  };

  const filteredDoctors = doctors.filter(doctor => {
    const matchesSearch =
      doctor.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.crm?.includes(searchTerm) ||
      doctor.specialties.some(s => s.name.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'all' || doctor.documentStatus === statusFilter;
    const matchesSpecialty = specialtyFilter === 'all' ||
      doctor.specialties.some(s => s.id === specialtyFilter);
    const matchesBankAccount = bankAccountFilter === 'all' ||
      (bankAccountFilter === 'with' && doctor.bankAccount) ||
      (bankAccountFilter === 'without' && !doctor.bankAccount);

    return matchesSearch && matchesStatus && matchesSpecialty && matchesBankAccount;
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold">Gestão de Médicos</h1>
            <p className="text-muted-foreground">
              Dashboard completo para administração de médicos
            </p>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold">Gestão de Médicos</h1>
            <p className="text-muted-foreground">
              Dashboard completo para administração de médicos
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <p className="text-lg font-medium">Erro ao carregar dados</p>
              <p className="text-sm text-muted-foreground mt-2">{error}</p>
              <Button onClick={fetchDoctorsData} className="mt-4">
                Tentar novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold">Gestão de Médicos</h1>
          <p className="text-muted-foreground">
            Dashboard completo para administração de médicos da plataforma
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={fetchDoctorsData}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Métricas Principais */}
      {metrics && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <DoctorMetricsCard
            title="Total de Médicos"
            value={metrics.total}
            icon={Stethoscope}
            trend={`+${Math.round((metrics.total / 100) * 5)}%`}
            trendDirection="up"
            description="médicos cadastrados"
          />

          <DoctorMetricsCard
            title="Pendentes de Aprovação"
            value={metrics.pending}
            icon={Clock}
            trend={`${Math.round((metrics.pending / metrics.total) * 100)}%`}
            trendDirection="neutral"
            description="aguardando aprovação"
            variant="warning"
          />

          <DoctorMetricsCard
            title="Médicos Aprovados"
            value={metrics.approved}
            icon={CheckCircle}
            trend={`${Math.round((metrics.approved / metrics.total) * 100)}%`}
            trendDirection="up"
            description="ativos na plataforma"
            variant="success"
          />

          <DoctorMetricsCard
            title="Receita Total"
            value={formatCurrency(metrics.totalRevenue)}
            icon={DollarSign}
            trend="+12%"
            trendDirection="up"
            description="gerada pelos médicos"
          />
        </div>
      )}

      {/* Filtros */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por nome, email, CRM ou especialidade..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os Status</SelectItem>
                <SelectItem value="PENDING">Pendentes</SelectItem>
                <SelectItem value="APPROVED">Aprovados</SelectItem>
                <SelectItem value="REJECTED">Rejeitados</SelectItem>
              </SelectContent>
            </Select>

            <Select value={specialtyFilter} onValueChange={setSpecialtyFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Especialidade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as Especialidades</SelectItem>
                {metrics?.bySpecialty.map((specialty) => (
                  <SelectItem key={specialty.specialty} value={specialty.specialty}>
                    {specialty.specialty} ({specialty.count})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={bankAccountFilter} onValueChange={setBankAccountFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Conta Bancária" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas as Contas</SelectItem>
                <SelectItem value="with">Com Conta</SelectItem>
                <SelectItem value="without">Sem Conta</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabs de Conteúdo */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="doctors">Lista de Médicos</TabsTrigger>
          <TabsTrigger value="bank-accounts">Contas Bancárias</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <DoctorStatusChart data={metrics?.byStatus || []} />
            <DoctorSpecialtyChart data={metrics?.bySpecialty || []} />
          </div>

          <div className="grid gap-6">
            <DoctorRevenueChart data={metrics?.topPerformers || []} />
          </div>
        </TabsContent>

        <TabsContent value="doctors" className="space-y-6">
          <DoctorTable
            doctors={filteredDoctors}
            onViewDetails={(doctor) => {
              setSelectedDoctor(doctor);
              setDetailsModalOpen(true);
            }}
            onApprove={(doctor) => {
              setSelectedDoctor(doctor);
              setApprovalModalOpen(true);
            }}
            onManageBankAccount={(doctor) => {
              setSelectedDoctor(doctor);
              setBankAccountModalOpen(true);
            }}
          />
        </TabsContent>

        <TabsContent value="bank-accounts" className="space-y-6">
          <BankAccountManagement
            doctors={doctors}
            onAction={handleBankAccountAction}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Top 10 Médicos por Receita</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics?.topPerformers.slice(0, 10).map((doctor, index) => (
                    <div key={doctor.id} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                          <span className="text-sm font-medium text-primary">
                            {index + 1}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{doctor.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {doctor.appointments} consultas • ⭐ {doctor.rating.toFixed(1)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(doctor.revenue)}</p>
                        {index === 0 && (
                          <Badge variant="secondary" className="text-xs">
                            <TrendingUp className="w-3 h-3 mr-1" />
                            Top 1
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Atividade Recente</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics?.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                        <Activity className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.description}</p>
                        <p className="text-xs text-muted-foreground">
                          {activity.doctorName} • {new Date(activity.timestamp).toLocaleString('pt-BR')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modais */}
      {selectedDoctor && (
        <>
          <DoctorApprovalModal
            doctor={selectedDoctor}
            open={approvalModalOpen}
            onOpenChange={setApprovalModalOpen}
            onApprove={handleApproveDoctor}
          />

          <DoctorDetailsModal
            doctor={selectedDoctor}
            open={detailsModalOpen}
            onOpenChange={setDetailsModalOpen}
          />

          <BankAccountManagement
            doctors={[selectedDoctor]}
            onAction={handleBankAccountAction}
            open={bankAccountModalOpen}
            onOpenChange={setBankAccountModalOpen}
          />
        </>
      )}
    </div>
  );
}
