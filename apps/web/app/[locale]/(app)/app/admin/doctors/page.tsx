import { currentUser } from "@saas/auth/lib/current-user";
import { redirect } from "next/navigation";
import { getLocale } from "next-intl/server";
import { AdminDoctorsClient } from "./admin-doctors-client";

export default async function AdminDoctorsPage() {
  const { user } = await currentUser();
  const locale = await getLocale();

  if (!user) {
    redirect(`/${locale}/auth/login`);
  }

  if (user.role !== "ADMIN") {
    redirect("/app/dashboard");
  }

  return <AdminDoctorsClient />;
}
