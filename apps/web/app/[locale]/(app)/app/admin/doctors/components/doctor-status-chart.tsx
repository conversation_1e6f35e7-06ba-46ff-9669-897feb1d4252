'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { CheckCircle, Clock, XCircle } from "lucide-react";

interface StatusData {
  status: string;
  count: number;
}

interface DoctorStatusChartProps {
  data: StatusData[];
}

export function DoctorStatusChart({ data }: DoctorStatusChartProps) {
  const total = data.reduce((sum, item) => sum + item.count, 0);

  const statusConfig = {
    PENDING: {
      label: 'Pendentes',
      icon: Clock,
      color: 'bg-yellow-500',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-800'
    },
    APPROVED: {
      label: 'Aprovados',
      icon: CheckCircle,
      color: 'bg-green-500',
      bgColor: 'bg-green-100',
      textColor: 'text-green-800'
    },
    REJECTED: {
      label: 'Rejeitados',
      icon: XCircle,
      color: 'bg-red-500',
      bgColor: 'bg-red-100',
      textColor: 'text-red-800'
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Status dos Médicos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((item) => {
            const config = statusConfig[item.status as keyof typeof statusConfig];
            const percentage = total > 0 ? (item.count / total) * 100 : 0;
            const Icon = config?.icon || Clock;

            return (
              <div key={item.status} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{config?.label || item.status}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{item.count}</span>
                    <Badge variant="secondary" className="text-xs">
                      {percentage.toFixed(1)}%
                    </Badge>
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${config?.color || 'bg-gray-500'}`}
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}

          {data.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              Nenhum dado disponível
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
