"use client";

import Link from "next/link";
import { Database, MessageSquare, RefreshCw, Settings, Users } from "lucide-react";

export default function AdminPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Dashboard de Administração</h1>
        <p className="text-sm text-muted-foreground mt-2">
          Acesse as ferramentas administrativas e configurações do sistema.
        </p>
      </div>


        {/* Card Usuários */}
        <div className="bg-white  flex flex-col">
          <div className="flex items-center gap-2 mb-2">
            <div className="bg-blue-100 p-2 rounded-full">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
            <h3 className="font-medium">Usuários</h3>
          </div>
          <p className="text-sm text-muted-foreground mb-4 flex-grow">
            <PERSON><PERSON><PERSON><PERSON> usuários, permissões e contas.
          </p>
          <Link
            href="/app/admin/users"
            className="text-sm text-blue-600 hover:text-blue-800 hover:underline flex items-center"
          >
            Gerenciar usuários
            <svg className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>



    </div>
  );
}
