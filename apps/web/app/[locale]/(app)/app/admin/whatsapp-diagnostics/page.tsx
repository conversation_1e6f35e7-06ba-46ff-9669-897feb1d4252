'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Alert, AlertDescription } from '@ui/components/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@ui/components/tabs';
import { Input } from '@ui/components/input';
import { Label } from '@ui/components/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@ui/components/select';
import { Textarea } from '@ui/components/textarea';
import {
  runWhatsAppDiagnostics,
  generateDiagnosticsReport,
  testWhatsAppNotification,
  type WhatsAppDiagnostics
} from '@lib/whatsapp-diagnostics';
import {
  MessageSquare,
  Settings,
  TestTube,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Send
} from 'lucide-react';


export default function WhatsAppDiagnosticsPage() {
  const [diagnostics, setDiagnostics] = useState<WhatsAppDiagnostics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isTestingNotification, setIsTestingNotification] = useState(false);

  // Test form data
  const [testData, setTestData] = useState({
    appointmentId: 'test-' + Date.now(),
    patientName: 'Paciente Teste',
    urgencyLevel: 'MEDIUM' as 'HIGH' | 'MEDIUM' | 'LOW'
  });

  const runDiagnostics = async () => {
    setIsLoading(true);
    try {
      const result = await runWhatsAppDiagnostics();
      setDiagnostics(result);
    } catch (error) {
      console.error('Erro ao executar diagnósticos:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testNotification = async () => {
    setIsTestingNotification(true);
    try {
      const result = await testWhatsAppNotification(testData);
      setTestResult(result);
    } catch (error) {
      console.error('Erro ao testar notificação:', error);
      setTestResult({
        success: false,
        results: {},
        errors: [error instanceof Error ? error.message : 'Erro desconhecido']
      });
    } finally {
      setIsTestingNotification(false);
    }
  };

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return status ? <CheckCircle className="w-4 h-4 text-green-500" /> : <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusBadge = (status: boolean | undefined) => {
    if (status === undefined) return <Badge variant="outline">Não testado</Badge>;
    return status ? <Badge className="bg-green-100 text-green-800">OK</Badge> : <Badge variant="destructive">Erro</Badge>;
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">WhatsApp Diagnostics</h1>
          <p className="text-muted-foreground">
            Diagnóstico e teste do sistema de notificações WhatsApp
          </p>
        </div>
        <Button onClick={runDiagnostics} disabled={isLoading}>
          {isLoading ? (
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Settings className="w-4 h-4 mr-2" />
          )}
          {isLoading ? 'Executando...' : 'Executar Diagnóstico'}
        </Button>
      </div>

      <Tabs defaultValue="diagnostics" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="diagnostics" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Diagnóstico
          </TabsTrigger>
          <TabsTrigger value="test" className="flex items-center gap-2">
            <TestTube className="w-4 h-4" />
            Teste
          </TabsTrigger>
          <TabsTrigger value="report" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            Relatório
          </TabsTrigger>
        </TabsList>

        <TabsContent value="diagnostics" className="space-y-6">
          {diagnostics && (
            <>
              {/* Evolution API Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    Evolution API
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">API Key</p>
                        <p className="text-xs text-muted-foreground">Chave de acesso</p>
                      </div>
                      {getStatusIcon(diagnostics.evolutionApi.apiKey)}
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">Instance</p>
                        <p className="text-xs text-muted-foreground">Nome da instância</p>
                      </div>
                      {getStatusIcon(diagnostics.evolutionApi.instance)}
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">URL</p>
                        <p className="text-xs text-muted-foreground">Endpoint da API</p>
                      </div>
                      {getStatusIcon(diagnostics.evolutionApi.url)}
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">Conexão</p>
                        <p className="text-xs text-muted-foreground">Teste de conectividade</p>
                      </div>
                      {getStatusIcon(diagnostics.evolutionApi.connectionTest)}
                    </div>
                  </div>

                  {diagnostics.evolutionApi.error && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Erro:</strong> {diagnostics.evolutionApi.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Groups Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5" />
                    Grupos WhatsApp
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Grupo Médicos</h4>
                        {getStatusBadge(diagnostics.groups.doctorsGroup.configured)}
                      </div>
                      {diagnostics.groups.doctorsGroup.id && (
                        <p className="text-xs text-muted-foreground font-mono">
                          ID: {diagnostics.groups.doctorsGroup.id}
                        </p>
                      )}
                      {diagnostics.groups.doctorsGroup.error && (
                        <p className="text-xs text-red-600 mt-1">
                          {diagnostics.groups.doctorsGroup.error}
                        </p>
                      )}
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Grupo Admin</h4>
                        {getStatusBadge(diagnostics.groups.adminGroup.configured)}
                      </div>
                      {diagnostics.groups.adminGroup.id && (
                        <p className="text-xs text-muted-foreground font-mono">
                          ID: {diagnostics.groups.adminGroup.id}
                        </p>
                      )}
                      {diagnostics.groups.adminGroup.error && (
                        <p className="text-xs text-red-600 mt-1">
                          {diagnostics.groups.adminGroup.error}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Services Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Serviços
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">EvolutionService</p>
                        <p className="text-xs text-muted-foreground">Serviço de integração</p>
                      </div>
                      {getStatusIcon(diagnostics.services.evolutionService)}
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">WhatsAppGroupService</p>
                        <p className="text-xs text-muted-foreground">Serviço de grupos</p>
                      </div>
                      {getStatusIcon(diagnostics.services.whatsappGroupService)}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Recommendations */}
              {diagnostics.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-yellow-500" />
                      Recomendações
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {diagnostics.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-sm text-muted-foreground">{index + 1}.</span>
                          <span className="text-sm">{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {!diagnostics && (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Clique em "Executar Diagnóstico" para começar</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Teste de Notificação</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="patientName">Nome do Paciente</Label>
                  <Input
                    id="patientName"
                    value={testData.patientName}
                    onChange={(e) => setTestData(prev => ({ ...prev, patientName: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="urgencyLevel">Nível de Urgência</Label>
                  <Select
                    value={testData.urgencyLevel}
                    onValueChange={(value: 'HIGH' | 'MEDIUM' | 'LOW') =>
                      setTestData(prev => ({ ...prev, urgencyLevel: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HIGH">🔴 MUITO URGENTE</SelectItem>
                      <SelectItem value="MEDIUM">🟡 URGENTE</SelectItem>
                      <SelectItem value="LOW">🟢 POUCO URGENTE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="appointmentId">ID do Agendamento</Label>
                <Input
                  id="appointmentId"
                  value={testData.appointmentId}
                  onChange={(e) => setTestData(prev => ({ ...prev, appointmentId: e.target.value }))}
                />
              </div>

              <Button
                onClick={testNotification}
                disabled={isTestingNotification}
                className="w-full"
              >
                {isTestingNotification ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Send className="w-4 h-4 mr-2" />
                )}
                {isTestingNotification ? 'Enviando...' : 'Enviar Teste'}
              </Button>
            </CardContent>
          </Card>

          {testResult && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {testResult.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  Resultado do Teste
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {testResult.errors.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Erros encontrados:</strong>
                      <ul className="mt-2 space-y-1">
                        {testResult.errors.map((error: string, index: number) => (
                          <li key={index} className="text-sm">• {error}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <h4 className="font-medium">Resultados por Grupo:</h4>
                  <Textarea
                    value={JSON.stringify(testResult.results, null, 2)}
                    readOnly
                    className="font-mono text-xs"
                    rows={10}
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="report" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Relatório Completo</CardTitle>
            </CardHeader>
            <CardContent>
              {diagnostics ? (
                <Textarea
                  value={generateDiagnosticsReport(diagnostics)}
                  readOnly
                  className="font-mono text-xs"
                  rows={20}
                />
              ) : (
                <p className="text-muted-foreground">Execute o diagnóstico primeiro para gerar o relatório.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
