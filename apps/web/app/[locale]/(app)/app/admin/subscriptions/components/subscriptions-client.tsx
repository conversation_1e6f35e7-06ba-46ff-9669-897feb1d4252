"use client";

import { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Button } from "@ui/components/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@ui/components/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@ui/components/table";
import { Badge } from "@ui/components/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@ui/components/collapsible";
import { Checkbox } from "@ui/components/checkbox";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
import { BulkActionsToolbar } from "../../../../../../../components/admin/subscriptions/bulk-actions-toolbar";

type SubscriptionRow = {
  id: string;
  planId: string;
  planName: string;
  planPrice: number;
  status: string;
  startDate: string | Date | null;
  nextBillingDate: string | Date | null;
  asaasSubscriptionId: string | null;
  consultationsIncluded: number;
  consultationsUsed: number;
  lastResetDate: string | Date;
  lastPaymentStatus: string | null;
  lastPaymentAt: string | Date | null;
  failedPaymentAt: string | Date | null;
  paymentMethod: string | null;
  createdAt: string | Date;
  updatedAt: string | Date;
  patient: { id: string; user: { name: string | null; email: string | null; phone: string | null } };
  transactions: Array<{
    id: string;
    amount: number;
    status: string;
    paymentMethod: string;
    paidAt: string | Date | null;
    refundedAt: string | Date | null;
    createdAt: string | Date;
  }>;
  consultationUsages: Array<{
    id: string;
    usedAt: string | Date;
    type: string;
    appointment: {
      id: string;
      scheduledAt: string | Date;
      status: string;
      doctor: {
        user: { name: string | null };
      };
    };
  }>;
};

export function SubscriptionsClient({
  subscriptions,
  currentPage,
  totalPages,
  currentSearch,
  currentStatus,
}: {
  subscriptions: SubscriptionRow[];
  currentPage: number;
  totalPages: number;
  currentSearch?: string;
  currentStatus?: string;
}) {
  // client-side URL helpers to avoid next/navigation types in lints
  const [search, setSearch] = useState<string>(currentSearch || "");
  const [status, setStatus] = useState<string>(currentStatus && currentStatus.length > 0 ? currentStatus : "ALL");
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [doctorFilter, setDoctorFilter] = useState<string>("ALL");

  const goto = (page: number) => {
    const params = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : "");
    if (search) params.set("search", search); else params.delete("search");
    if (status && status !== "ALL") params.set("status", status); else params.delete("status");
    if (doctorFilter && doctorFilter !== "ALL") params.set("doctor", doctorFilter); else params.delete("doctor");
    params.set("page", String(page));
    if (typeof window !== 'undefined') {
      window.location.assign(`/app/admin/subscriptions?${params.toString()}`);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(subscriptions.map(s => s.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectRow = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  const handleActionComplete = () => {
    // Recarregar a página para atualizar os dados
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  // Extrair médicos únicos para o filtro
  const uniqueDoctors = useMemo(() => {
    const doctors = new Map();
    subscriptions.forEach(sub => {
      sub.consultationUsages.forEach(usage => {
        if (usage.appointment.doctor?.user?.name) {
          doctors.set(usage.appointment.doctor.user.name, usage.appointment.doctor.user.name);
        }
      });
    });
    return Array.from(doctors.values()).sort();
  }, [subscriptions]);

  const formatDate = (d: string | Date | null) => {
    if (!d) return "-";
    const date = typeof d === "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat("pt-BR", { dateStyle: "short" }).format(date);
  };

  const formatCurrency = (v: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(v || 0);

  const formatDateTime = (d: string | Date | null) => {
    if (!d) return "-";
    const date = typeof d === "string" ? new Date(d) : d;
    return new Intl.DateTimeFormat("pt-BR", {
      dateStyle: "short",
      timeStyle: "short"
    }).format(date);
  };

  const statusBadge = (s: string) => {
    const map: Record<string, string> = {
      ACTIVE: "bg-green-100 text-green-800",
      PAST_DUE: "bg-yellow-100 text-yellow-800",
      UNPAID: "bg-orange-100 text-orange-800",
      PAUSED: "bg-gray-100 text-gray-800",
      CANCELED: "bg-red-100 text-red-800",
      PENDING: "bg-blue-100 text-blue-800",
    };
    const labelMap: Record<string, string> = {
      ACTIVE: "Ativa",
      PAST_DUE: "Em atraso",
      UNPAID: "Não paga",
      PAUSED: "Pausada",
      CANCELED: "Cancelada",
      PENDING: "Pendente",
    };
    return <span className={`text-xs px-2 py-1 rounded ${map[s] || "bg-gray-100 text-gray-800"}`}>{labelMap[s] || s}</span>;
  };

  const paymentStatusBadge = (s: string | null) => {
    if (!s) return <span className="text-xs text-gray-500">-</span>;
    const map: Record<string, string> = {
      PAID: "bg-green-100 text-green-800",
      PENDING: "bg-yellow-100 text-yellow-800",
      FAILED: "bg-red-100 text-red-800",
      REFUNDED: "bg-orange-100 text-orange-800",
    };
    const labelMap: Record<string, string> = {
      PAID: "Pago",
      PENDING: "Pendente",
      FAILED: "Falhou",
      REFUNDED: "Reembolsado",
    };
    return <span className={`text-xs px-2 py-1 rounded ${map[s] || "bg-gray-100 text-gray-800"}`}>{labelMap[s] || s}</span>;
  };

  const toggleRowExpansion = (rowId: string) => {
    const newExpanded = new Set(expandedRows);
    if (expandedRows.has(rowId)) {
      newExpanded.delete(rowId);
    } else {
      newExpanded.add(rowId);
    }
    setExpandedRows(newExpanded);
  };

  const applyFilters = (e: React.FormEvent) => {
    e.preventDefault();
    goto(1);
  };

  const actions = (row: SubscriptionRow) => {
    const cancel = async () => {
      if (!confirm("Confirmar cancelamento desta assinatura?")) return;
      const res = await fetch(`/app/api/admin/subscriptions/${row.id}/cancel`, { method: "POST" });
      if (!res.ok) {
        alert("Erro ao cancelar assinatura");
        return;
      }
      if (typeof window !== 'undefined') {
        window.location.reload();
      }
    };
    return (
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            if (typeof window !== 'undefined') {
              window.location.assign(`/app/admin/subscriptions/${row.id}`);
            }
          }}
        >
          Detalhes
        </Button>
        {row.status !== "CANCELED" && (
          <Button variant="error" size="sm" onClick={cancel}>
            Cancelar
          </Button>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <form onSubmit={applyFilters} className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1">
          <Input placeholder="Buscar por paciente, email, plano ou ID" value={search} onChange={(e) => setSearch(e.target.value)} />
        </div>
        <Select value={status} onValueChange={setStatus}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">Todos</SelectItem>
            <SelectItem value="ACTIVE">Ativa</SelectItem>
            <SelectItem value="PAUSED">Pausada</SelectItem>
            <SelectItem value="PAST_DUE">Em atraso</SelectItem>
            <SelectItem value="UNPAID">Não paga</SelectItem>
            <SelectItem value="CANCELED">Cancelada</SelectItem>
          </SelectContent>
        </Select>
        <Select value={doctorFilter} onValueChange={setDoctorFilter}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Médico" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">Todos os médicos</SelectItem>
            {uniqueDoctors.map(doctor => (
              <SelectItem key={doctor} value={doctor}>{doctor}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button type="submit">Aplicar</Button>
      </form>

      <BulkActionsToolbar
        selectedIds={selectedIds}
        onSelectionChange={setSelectedIds}
        onActionComplete={handleActionComplete}
      />

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedIds.length === subscriptions.length && subscriptions.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead></TableHead>
              <TableHead>Paciente</TableHead>
              <TableHead>Plano</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Consultas</TableHead>
              <TableHead>Último Pagto</TableHead>
              <TableHead>Próx. cobrança</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subscriptions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={10} className="text-center text-sm text-muted-foreground">
                  Nenhuma assinatura encontrada
                </TableCell>
              </TableRow>
            ) : (
              subscriptions.map((row) => (
                <>
                  <TableRow key={row.id} className="cursor-pointer hover:bg-muted/50">
                    <TableCell>
                      <Checkbox
                        checked={selectedIds.includes(row.id)}
                        onCheckedChange={(checked) => handleSelectRow(row.id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleRowExpansion(row.id)}
                        className="h-6 w-6 p-0"
                      >
                        {expandedRows.has(row.id) ? (
                          <ChevronDownIcon className="h-4 w-4" />
                        ) : (
                          <ChevronRightIcon className="h-4 w-4" />
                        )}
                      </Button>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{row.patient.user.name || "Paciente"}</span>
                        <span className="text-xs text-muted-foreground">{row.patient.user.email || "-"}</span>
                        {row.patient.user.phone && (
                          <span className="text-xs text-muted-foreground">{row.patient.user.phone}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{row.planName}</span>
                        <span className="text-xs text-muted-foreground">{row.planId}</span>
                        {row.paymentMethod && (
                          <span className="text-xs text-blue-600">{row.paymentMethod}</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(row.planPrice)}</TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {statusBadge(row.status)}
                        {row.lastPaymentStatus && (
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-muted-foreground">Pagto:</span>
                            {paymentStatusBadge(row.lastPaymentStatus)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {row.consultationsUsed}/{row.consultationsIncluded}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Reset: {formatDate(row.lastResetDate)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-xs">
                          {row.lastPaymentAt ? formatDateTime(row.lastPaymentAt) : "-"}
                        </span>
                        {row.failedPaymentAt && (
                          <span className="text-xs text-red-600">
                            Falha: {formatDateTime(row.failedPaymentAt)}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatDate(row.nextBillingDate)}</TableCell>
                    <TableCell className="text-right">{actions(row)}</TableCell>
                  </TableRow>

                  {/* Linha expandida com detalhes */}
                  {expandedRows.has(row.id) && (
                    <TableRow>
                      <TableCell colSpan={10} className="p-0">
                        <div className="px-4 py-3 bg-muted/30">
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            {/* Histórico de Pagamentos */}
                            <Card>
                              <CardHeader className="pb-3">
                                <CardTitle className="text-sm">Histórico de Pagamentos</CardTitle>
                              </CardHeader>
                              <CardContent className="pt-0">
                                {row.transactions.length === 0 ? (
                                  <div className="text-xs text-muted-foreground">Nenhum pagamento encontrado</div>
                                ) : (
                                  <div className="space-y-2">
                                    {row.transactions.map((transaction) => (
                                      <div key={transaction.id} className="flex justify-between items-center text-xs">
                                        <div>
                                          <div className="font-medium">{formatCurrency(transaction.amount)}</div>
                                          <div className="text-muted-foreground">
                                            {transaction.paymentMethod} • {formatDateTime(transaction.createdAt)}
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          {paymentStatusBadge(transaction.status)}
                                          {transaction.paidAt && (
                                            <div className="text-muted-foreground mt-1">
                                              Pago: {formatDateTime(transaction.paidAt)}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </CardContent>
                            </Card>

                            {/* Histórico de Consultas */}
                            <Card>
                              <CardHeader className="pb-3">
                                <CardTitle className="text-sm">Consultas Utilizadas</CardTitle>
                              </CardHeader>
                              <CardContent className="pt-0">
                                {row.consultationUsages.length === 0 ? (
                                  <div className="text-xs text-muted-foreground">Nenhuma consulta utilizada</div>
                                ) : (
                                  <div className="space-y-2">
                                    {row.consultationUsages.map((usage) => (
                                      <div key={usage.id} className="flex justify-between items-center text-xs">
                                        <div>
                                          <div className="font-medium">
                                            Dr. {usage.appointment.doctor.user.name || "Médico"}
                                          </div>
                                          <div className="text-muted-foreground">
                                            {formatDateTime(usage.appointment.scheduledAt)}
                                          </div>
                                        </div>
                                        <div className="text-right">
                                          <Badge variant="outline" className="text-xs">
                                            {usage.type === "SUBSCRIPTION" ? "Assinatura" : usage.type}
                                          </Badge>
                                          <div className="text-muted-foreground mt-1">
                                            {usage.appointment.status}
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          </div>

                          {/* Informações técnicas */}
                          <div className="mt-4 pt-3 border-t border-border/50">
                            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                              <div>
                                <span className="font-medium">ID Assinatura:</span> {row.asaasSubscriptionId || "-"}
                              </div>
                              <div>
                                <span className="font-medium">Criado em:</span> {formatDateTime(row.createdAt)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex justify-between items-center text-sm">
        <span>
          Página {currentPage} de {totalPages}
        </span>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" disabled={currentPage <= 1} onClick={() => goto(currentPage - 1)}>
            Anterior
          </Button>
          <Button variant="outline" size="sm" disabled={currentPage >= totalPages} onClick={() => goto(currentPage + 1)}>
            Próxima
          </Button>
        </div>
      </div>
    </div>
  );
}


