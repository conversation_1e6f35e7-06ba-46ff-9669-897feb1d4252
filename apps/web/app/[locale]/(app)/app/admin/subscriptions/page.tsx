import { Suspense } from "react";
import { db } from "database";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { StatsTile } from "../../../../../../modules/saas/dashboard/components/StatsTile";
import { currentUser } from "@saas/auth/lib/current-user";

import { PaginationButton } from "@ui/components/pagination-button";
import { SubscriptionsClient } from "./components/subscriptions-client";
import { SubscriptionMetrics } from "../../../../../../components/admin/subscriptions/subscription-metrics";
import { redirect } from "@i18n/routing";

export const dynamic = "force-dynamic";

type SearchParams = {
  page?: string;
  search?: string;
  status?: string;
};

export default async function AdminSubscriptionsPage({
  searchParams,
}: { searchParams?: SearchParams }) {
  const session = await currentUser();
  if (!session?.user || session.user.role !== "ADMIN") {
    redirect("/app/dashboard");
  }

  const page = Number(searchParams?.page || 1);
  const pageSize = 10;
  const skip = Math.max(0, (isNaN(page) ? 0 : page - 1) * pageSize);
  const search = (searchParams?.search || "").trim();
  const status = (searchParams?.status || "").trim().toUpperCase();

  const allowedStatuses = [
    "ACTIVE",
    "PAUSED",
    "CANCELED",
    "PAST_DUE",
    "UNPAID",
  ] as const;

  const where: any = {};
  if (status && (allowedStatuses as readonly string[]).includes(status)) {
    where.status = status;
  }
  if (search) {
    where.OR = [
      { planName: { contains: search, mode: "insensitive" } },
      { asaasSubscriptionId: { contains: search } },
      {
        patient: {
          user: {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { email: { contains: search, mode: "insensitive" } },
            ],
          },
        },
      },
    ];
  }

  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const [
    totalCount,
    subscriptions,
    activeCount,
    riskCount,
    mrrAgg,
    canceled30d,
  ] = await Promise.all([
    db.patientSubscription.count({ where }),
    db.patientSubscription.findMany({
      where,
      skip,
      take: pageSize,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        planId: true,
        planName: true,
        planPrice: true,
        status: true,
        startDate: true,
        nextBillingDate: true,
        asaasSubscriptionId: true,
        consultationsIncluded: true,
        consultationsUsed: true,
        lastResetDate: true,
        lastPaymentStatus: true,
        lastPaymentAt: true,
        failedPaymentAt: true,
        paymentMethod: true,
        createdAt: true,
        updatedAt: true,
        patient: {
          select: {
            id: true,
            user: { select: { name: true, email: true, phone: true } },
          },
        },
        transactions: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentMethod: true,
            paidAt: true,
            refundedAt: true,
            createdAt: true,
          },
          orderBy: { createdAt: "desc" },
          take: 5, // Últimas 5 transações
        },
        consultationUsages: {
          select: {
            id: true,
            usedAt: true,
            type: true,
            appointment: {
              select: {
                id: true,
                scheduledAt: true,
                status: true,
                doctor: {
                  select: {
                    user: { select: { name: true } },
                  },
                },
              },
            },
          },
          orderBy: { usedAt: "desc" },
          take: 10, // Últimas 10 consultas
        },
      },
    }),
    db.patientSubscription.count({ where: { status: "ACTIVE" } }),
    db.patientSubscription.count({ where: { status: { in: ["PAST_DUE", "UNPAID"] } } }),
    db.patientSubscription.aggregate({
      _sum: { planPrice: true },
      where: { status: "ACTIVE" },
    }),
    db.patientSubscription.count({
      where: { status: "CANCELED", canceledAt: { gte: thirtyDaysAgo } },
    }),
  ]);

  const totalPages = Math.max(1, Math.ceil(totalCount / pageSize));
  const mrr = Number(mrrAgg._sum.planPrice || 0);

  // Serialize Decimal -> number for client
  const serialized = subscriptions.map((s) => ({
    ...s,
    planPrice: Number(s.planPrice as any),
    transactions: s.transactions.map((t) => ({
      ...t,
      amount: Number(t.amount as any),
    })),
  }));

  const metrics = {
    active: activeCount,
    risk: riskCount,
    mrr,
    canceled30d,
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <StatsTile title="Assinaturas ativas" value={metrics.active} valueFormat="number" />
        <StatsTile title="Em risco (inadimplência)" value={metrics.risk} valueFormat="number" />
        <StatsTile title="MRR" value={metrics.mrr} valueFormat="currency" />
        <StatsTile title="Canceladas (30d)" value={metrics.canceled30d} valueFormat="number" />
      </div>

      <SubscriptionMetrics />

      <Card>
        <CardHeader>
          <CardTitle>Assinaturas</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Carregando...</div>}>
            <SubscriptionsClient
              subscriptions={serialized as any}
              currentPage={!isNaN(page) ? page : 1}
              totalPages={totalPages}
              currentSearch={search}
              currentStatus={status}
            />
          </Suspense>
        </CardContent>
      </Card>

      {totalPages > 1 && (
        <div className="flex justify-center">
          <PaginationButton
            currentPage={!isNaN(page) ? page : 1}
            totalPages={totalPages}
            baseUrl="/app/admin/subscriptions"
            searchParams={{ ...(search ? { search } : {}), ...(status ? { status } : {}) }}
          />
        </div>
      )}
    </div>
  );
}


