import { constructMetadata } from '@lib/utils';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Consulta Online | Telemedicina WhatsApp | ZapVida',
    description: 'Consulte médicos online via WhatsApp! Telemedicina 24h com assinatura. Prescrição digital incluída. Mais de 30.000 pacientes atendidos.',
    canonical: 'https://zapvida.com/consulta-online-gratis',
  });
}

export default function ConsultaOnlineGratisPage() {
  // Redireciona para a página de assinaturas onde explicamos os benefícios
  redirect('/assinaturas');
}
