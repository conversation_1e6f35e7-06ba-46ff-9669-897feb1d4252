import { constructMetadata } from '@lib/utils';
import { Metada<PERSON> } from 'next';
import { redirect } from 'next/navigation';

export async function generateMetadata(): Promise<Metadata> {
  return constructMetadata({
    title: 'Consulta Online Via Chat | WhatsApp Telemedicina | ZapVida',
    description: 'Consulte médicos online via chat WhatsApp 24h. Telemedicina segura, prescrição digital e atendimento imediato. Assinantes têm descontos.',
    canonical: 'https://zapvida.com/consulta-online-via-chat',
  });
}

export default function ConsultaOnlineViaChatPage() {
  // Redireciona para a página de médicos online
  redirect('/doctors?online=true');
}
